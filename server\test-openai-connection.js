const axios = require('axios');
require('dotenv').config();

async function testOpenAIConnection() {
  try {
    console.log('🤖 Testing OpenAI Connection...\n');

    // Test 1: Check if API key is configured
    console.log('1. Checking OpenAI API Key:');
    const apiKey = process.env.OPENAI_API_KEY;
    
    if (!apiKey) {
      console.log('❌ OPENAI_API_KEY not found in environment variables');
      return;
    }
    
    if (apiKey.startsWith('sk-proj-') || apiKey.startsWith('sk-')) {
      console.log('✅ OpenAI API Key format looks correct');
      console.log(`   Key starts with: ${apiKey.substring(0, 10)}...`);
    } else {
      console.log('❌ OpenAI API Key format looks incorrect');
      return;
    }

    // Test 2: Test OpenAI API connection
    console.log('\n2. Testing OpenAI API Connection:');
    
    try {
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'user',
              content: 'Generate a short social media post for a local coffee shop. Keep it under 50 words.'
            }
          ],
          max_tokens: 100,
          temperature: 0.7
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 10000
        }
      );

      console.log('✅ OpenAI API Connection Successful!');
      console.log('   Model used:', response.data.model);
      console.log('   Generated content:');
      console.log('   "' + response.data.choices[0].message.content.trim() + '"');
      console.log('   Tokens used:', response.data.usage.total_tokens);

    } catch (error) {
      if (error.response) {
        console.log('❌ OpenAI API Error:');
        console.log('   Status:', error.response.status);
        console.log('   Error:', error.response.data.error?.message || error.response.data);
        
        if (error.response.status === 401) {
          console.log('   🔑 This usually means the API key is invalid or expired');
        } else if (error.response.status === 429) {
          console.log('   ⏰ This means you hit rate limits or ran out of credits');
        }
      } else {
        console.log('❌ Network Error:', error.message);
      }
    }

    // Test 3: Check OpenAI service file
    console.log('\n3. Checking OpenAI Service Implementation:');
    
    try {
      const openaiService = require('./services/openai');
      console.log('✅ OpenAI service file found');
      
      // Test if the service has the expected methods
      if (typeof openaiService.generateContent === 'function') {
        console.log('✅ generateContent method exists');
      } else {
        console.log('❌ generateContent method not found');
      }
      
    } catch (error) {
      console.log('❌ OpenAI service file error:', error.message);
    }

    // Test 4: Test content generation endpoint
    console.log('\n4. Testing Content Generation Endpoint:');
    
    try {
      const response = await axios.get('http://localhost:5000/api/content/health');
      console.log('✅ Content endpoint is accessible');
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('⚠️ Content health endpoint not found (this is okay)');
      } else {
        console.log('❌ Content endpoint error:', error.message);
      }
    }

    console.log('\n🎯 OpenAI Test Complete!');
    console.log('\nSummary:');
    console.log('- API Key: ✅ Configured');
    console.log('- Connection: Test results above');
    console.log('- Service: Check results above');
    console.log('\nIf OpenAI is working, you can test content generation in LocalPost.ai!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testOpenAIConnection();
