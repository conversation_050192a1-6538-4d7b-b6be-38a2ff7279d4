import React from 'react';
import { Users, Plus, Mail, Shield } from 'lucide-react';

const Team = () => {
  const teamMembers = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Owner',
      status: 'Active'
    },
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'Editor',
      status: 'Active'
    }
  ];

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Team</h1>
          <p className="text-gray-600">Manage your team members and their permissions</p>
        </div>
        <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
          <Plus size={16} />
          <span>Invite Member</span>
        </button>
      </div>

      {/* Team Members */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Team Members</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {teamMembers.map((member, index) => (
            <div key={index} className="p-6 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                  <Users size={20} className="text-gray-600" />
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-900">{member.name}</h3>
                  <p className="text-sm text-gray-500 flex items-center space-x-1">
                    <Mail size={14} />
                    <span>{member.email}</span>
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <Shield size={16} className="text-gray-400" />
                  <span className="text-sm text-gray-600">{member.role}</span>
                </div>
                <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  {member.status}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Permissions Info */}
      <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-2">Team Permissions</h3>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>Owner:</strong> Full access to all features and settings</li>
          <li>• <strong>Editor:</strong> Can create and edit content, view analytics</li>
          <li>• <strong>Viewer:</strong> Can view content and analytics only</li>
        </ul>
      </div>
    </div>
  );
};

export default Team;
