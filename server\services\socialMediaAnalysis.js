const axios = require('axios');

class SocialMediaAnalysisService {
  constructor() {
    this.supportedPlatforms = ['facebook', 'instagram', 'twitter', 'linkedin'];
  }

  // Main method to analyze posts from a social media account
  async analyzeSocialMediaAccount(platform, accessToken, accountId, options = {}) {
    try {
      console.log(`Starting analysis for ${platform} account: ${accountId}`);

      // Fetch posts from the platform
      const posts = await this.fetchPosts(platform, accessToken, accountId, options);

      if (!posts || posts.length === 0) {
        return {
          success: false,
          error: 'No posts found for analysis',
          data: { posts: [], analysis: null }
        };
      }

      // Analyze the posts for patterns and performance
      const analysis = await this.analyzePostPatterns(posts, platform);

      // Store the analyzed data for AI training
      await this.storeAnalyzedPosts(posts, analysis, platform, accountId);

      return {
        success: true,
        data: {
          posts: posts,
          analysis: analysis,
          totalPosts: posts.length,
          platform: platform,
          accountId: accountId
        }
      };

    } catch (error) {
      console.error(`Social media analysis error for ${platform}:`, error.message);
      return {
        success: false,
        error: error.message,
        data: { posts: [], analysis: null }
      };
    }
  }

  // Fetch posts from different social media platforms
  async fetchPosts(platform, accessToken, accountId, options = {}) {
    const limit = options.limit || 50; // Default to last 50 posts
    const timeframe = options.timeframe || '6months'; // Default to 6 months

    switch (platform.toLowerCase()) {
      case 'facebook':
        return await this.fetchFacebookPosts(accessToken, accountId, limit);
      case 'instagram':
        return await this.fetchInstagramPosts(accessToken, accountId, limit);
      case 'twitter':
        return await this.fetchTwitterPosts(accessToken, accountId, limit);
      case 'linkedin':
        return await this.fetchLinkedInPosts(accessToken, accountId, limit);
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }
  }

  // Facebook Posts Analysis
  async fetchFacebookPosts(accessToken, pageId, limit) {
    try {
      const response = await axios.get(`https://graph.facebook.com/v18.0/${pageId}/posts`, {
        params: {
          access_token: accessToken,
          fields: 'id,message,story,created_time,type,link,picture,full_picture,likes.summary(true),comments.summary(true),shares,reactions.summary(true)',
          limit: limit
        }
      });

      return response.data.data.map(post => ({
        id: post.id,
        platform: 'facebook',
        content: post.message || post.story || '',
        createdAt: post.created_time,
        type: post.type,
        link: post.link,
        media: {
          picture: post.picture,
          fullPicture: post.full_picture
        },
        engagement: {
          likes: post.likes?.summary?.total_count || 0,
          comments: post.comments?.summary?.total_count || 0,
          shares: post.shares?.count || 0,
          reactions: post.reactions?.summary?.total_count || 0
        },
        rawData: post
      }));
    } catch (error) {
      console.error('Facebook API error:', error.response?.data || error.message);
      throw new Error(`Failed to fetch Facebook posts: ${error.message}`);
    }
  }

  // Instagram Posts Analysis
  async fetchInstagramPosts(accessToken, accountId, limit) {
    try {
      const response = await axios.get(`https://graph.instagram.com/me/media`, {
        params: {
          access_token: accessToken,
          fields: 'id,caption,media_type,media_url,thumbnail_url,timestamp,like_count,comments_count,permalink',
          limit: limit
        }
      });

      return response.data.data.map(post => ({
        id: post.id,
        platform: 'instagram',
        content: post.caption || '',
        createdAt: post.timestamp,
        type: post.media_type,
        link: post.permalink,
        media: {
          url: post.media_url,
          thumbnail: post.thumbnail_url
        },
        engagement: {
          likes: post.like_count || 0,
          comments: post.comments_count || 0,
          shares: 0, // Instagram doesn't provide share count
          reactions: post.like_count || 0
        },
        rawData: post
      }));
    } catch (error) {
      console.error('Instagram API error:', error.response?.data || error.message);
      throw new Error(`Failed to fetch Instagram posts: ${error.message}`);
    }
  }

  // Twitter Posts Analysis
  async fetchTwitterPosts(accessToken, userId, limit) {
    try {
      // Note: This would use Twitter API v2
      const response = await axios.get(`https://api.twitter.com/2/users/${userId}/tweets`, {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        params: {
          'tweet.fields': 'created_at,text,public_metrics,context_annotations,entities',
          'max_results': Math.min(limit, 100)
        }
      });

      return response.data.data.map(tweet => ({
        id: tweet.id,
        platform: 'twitter',
        content: tweet.text,
        createdAt: tweet.created_at,
        type: 'text',
        link: `https://twitter.com/user/status/${tweet.id}`,
        media: {
          // Would need to extract media from entities if present
        },
        engagement: {
          likes: tweet.public_metrics?.like_count || 0,
          comments: tweet.public_metrics?.reply_count || 0,
          shares: tweet.public_metrics?.retweet_count || 0,
          reactions: tweet.public_metrics?.like_count || 0
        },
        rawData: tweet
      }));
    } catch (error) {
      console.error('Twitter API error:', error.response?.data || error.message);
      throw new Error(`Failed to fetch Twitter posts: ${error.message}`);
    }
  }

  // LinkedIn Posts Analysis
  async fetchLinkedInPosts(accessToken, personId, limit) {
    try {
      const response = await axios.get('https://api.linkedin.com/v2/shares', {
        headers: {
          'Authorization': `Bearer ${accessToken}`
        },
        params: {
          q: 'owners',
          owners: `urn:li:person:${personId}`,
          count: Math.min(limit, 50)
        }
      });

      return response.data.elements.map(post => ({
        id: post.id,
        platform: 'linkedin',
        content: post.text?.text || '',
        createdAt: new Date(post.created?.time).toISOString(),
        type: 'text',
        link: '', // LinkedIn doesn't provide direct post URLs in API
        media: {
          // Would need to extract media from content if present
        },
        engagement: {
          likes: 0, // Would need separate API call to get engagement metrics
          comments: 0,
          shares: 0,
          reactions: 0
        },
        rawData: post
      }));
    } catch (error) {
      console.error('LinkedIn API error:', error.response?.data || error.message);
      throw new Error(`Failed to fetch LinkedIn posts: ${error.message}`);
    }
  }

  // Analyze post patterns for AI training
  async analyzePostPatterns(posts, platform) {
    const analysis = {
      totalPosts: posts.length,
      platform: platform,
      timeRange: this.getTimeRange(posts),
      contentAnalysis: this.analyzeContent(posts),
      engagementAnalysis: this.analyzeEngagement(posts),
      performancePatterns: this.identifyPerformancePatterns(posts),
      bestPractices: this.extractBestPractices(posts),
      aiTrainingData: this.prepareAITrainingData(posts)
    };

    return analysis;
  }

  // Get time range of analyzed posts
  getTimeRange(posts) {
    if (posts.length === 0) return null;

    const dates = posts.map(post => new Date(post.createdAt)).sort();
    return {
      earliest: dates[0].toISOString(),
      latest: dates[dates.length - 1].toISOString(),
      span: `${Math.ceil((dates[dates.length - 1] - dates[0]) / (1000 * 60 * 60 * 24))} days`
    };
  }

  // Analyze content patterns
  analyzeContent(posts) {
    const contentWithText = posts.filter(post => post.content && post.content.trim());

    if (contentWithText.length === 0) {
      return {
        averageLength: 0,
        commonWords: [],
        hashtags: [],
        mentions: [],
        contentTypes: {},
        toneAnalysis: 'neutral'
      };
    }

    const allText = contentWithText.map(post => post.content).join(' ');
    const words = allText.toLowerCase().match(/\b\w+\b/g) || [];
    const hashtags = allText.match(/#\w+/g) || [];
    const mentions = allText.match(/@\w+/g) || [];

    // Count word frequency
    const wordCount = {};
    words.forEach(word => {
      if (word.length > 3) { // Filter out short words
        wordCount[word] = (wordCount[word] || 0) + 1;
      }
    });

    const commonWords = Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
      .map(([word, count]) => ({ word, count }));

    // Analyze content types
    const contentTypes = {};
    posts.forEach(post => {
      const type = post.type || 'text';
      contentTypes[type] = (contentTypes[type] || 0) + 1;
    });

    return {
      averageLength: Math.round(contentWithText.reduce((sum, post) => sum + post.content.length, 0) / contentWithText.length),
      commonWords: commonWords,
      hashtags: [...new Set(hashtags)].slice(0, 20),
      mentions: [...new Set(mentions)].slice(0, 10),
      contentTypes: contentTypes,
      toneAnalysis: this.analyzeTone(allText)
    };
  }

  // Analyze engagement patterns
  analyzeEngagement(posts) {
    if (posts.length === 0) return null;

    const engagementData = posts.map(post => ({
      totalEngagement: post.engagement.likes + post.engagement.comments + post.engagement.shares,
      likes: post.engagement.likes,
      comments: post.engagement.comments,
      shares: post.engagement.shares,
      contentLength: post.content?.length || 0,
      hasMedia: !!(post.media?.url || post.media?.picture),
      type: post.type
    }));

    const avgEngagement = engagementData.reduce((sum, data) => sum + data.totalEngagement, 0) / engagementData.length;
    const avgLikes = engagementData.reduce((sum, data) => sum + data.likes, 0) / engagementData.length;
    const avgComments = engagementData.reduce((sum, data) => sum + data.comments, 0) / engagementData.length;
    const avgShares = engagementData.reduce((sum, data) => sum + data.shares, 0) / engagementData.length;

    return {
      averageEngagement: Math.round(avgEngagement),
      averageLikes: Math.round(avgLikes),
      averageComments: Math.round(avgComments),
      averageShares: Math.round(avgShares),
      topPerformingPosts: this.getTopPerformingPosts(posts, 5),
      engagementByType: this.getEngagementByType(posts),
      mediaImpact: this.analyzeMediaImpact(engagementData)
    };
  }

  // Get top performing posts
  getTopPerformingPosts(posts, count = 5) {
    return posts
      .map(post => ({
        ...post,
        totalEngagement: post.engagement.likes + post.engagement.comments + post.engagement.shares
      }))
      .sort((a, b) => b.totalEngagement - a.totalEngagement)
      .slice(0, count)
      .map(post => ({
        id: post.id,
        content: post.content?.substring(0, 100) + '...',
        engagement: post.engagement,
        totalEngagement: post.totalEngagement,
        createdAt: post.createdAt
      }));
  }

  // Analyze engagement by content type
  getEngagementByType(posts) {
    const typeEngagement = {};

    posts.forEach(post => {
      const type = post.type || 'text';
      if (!typeEngagement[type]) {
        typeEngagement[type] = { total: 0, count: 0, posts: [] };
      }

      const engagement = post.engagement.likes + post.engagement.comments + post.engagement.shares;
      typeEngagement[type].total += engagement;
      typeEngagement[type].count += 1;
      typeEngagement[type].posts.push(engagement);
    });

    // Calculate averages
    Object.keys(typeEngagement).forEach(type => {
      typeEngagement[type].average = Math.round(typeEngagement[type].total / typeEngagement[type].count);
    });

    return typeEngagement;
  }

  // Analyze impact of media on engagement
  analyzeMediaImpact(engagementData) {
    const withMedia = engagementData.filter(data => data.hasMedia);
    const withoutMedia = engagementData.filter(data => !data.hasMedia);

    if (withMedia.length === 0 || withoutMedia.length === 0) {
      return { impact: 'insufficient_data' };
    }

    const avgWithMedia = withMedia.reduce((sum, data) => sum + data.totalEngagement, 0) / withMedia.length;
    const avgWithoutMedia = withoutMedia.reduce((sum, data) => sum + data.totalEngagement, 0) / withoutMedia.length;

    return {
      withMedia: Math.round(avgWithMedia),
      withoutMedia: Math.round(avgWithoutMedia),
      improvement: Math.round(((avgWithMedia - avgWithoutMedia) / avgWithoutMedia) * 100),
      impact: avgWithMedia > avgWithoutMedia ? 'positive' : 'negative'
    };
  }

  // Identify performance patterns
  identifyPerformancePatterns(posts) {
    const patterns = {
      bestTimes: this.analyzeBestPostingTimes(posts),
      contentLength: this.analyzeOptimalContentLength(posts),
      hashtagUsage: this.analyzeHashtagPerformance(posts),
      contentThemes: this.identifySuccessfulThemes(posts)
    };

    return patterns;
  }

  // Analyze best posting times
  analyzeBestPostingTimes(posts) {
    const timeEngagement = {};

    posts.forEach(post => {
      const date = new Date(post.createdAt);
      const hour = date.getHours();
      const dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday

      const timeKey = `${dayOfWeek}-${hour}`;
      if (!timeEngagement[timeKey]) {
        timeEngagement[timeKey] = { total: 0, count: 0 };
      }

      const engagement = post.engagement.likes + post.engagement.comments + post.engagement.shares;
      timeEngagement[timeKey].total += engagement;
      timeEngagement[timeKey].count += 1;
    });

    // Calculate averages and find best times
    const timeAverages = Object.entries(timeEngagement).map(([timeKey, data]) => {
      const [day, hour] = timeKey.split('-');
      return {
        day: parseInt(day),
        hour: parseInt(hour),
        averageEngagement: Math.round(data.total / data.count),
        postCount: data.count
      };
    }).sort((a, b) => b.averageEngagement - a.averageEngagement);

    return timeAverages.slice(0, 5); // Top 5 best times
  }

  // Analyze optimal content length
  analyzeOptimalContentLength(posts) {
    const lengthRanges = {
      'short': { min: 0, max: 50, posts: [] },
      'medium': { min: 51, max: 150, posts: [] },
      'long': { min: 151, max: 300, posts: [] },
      'very_long': { min: 301, max: Infinity, posts: [] }
    };

    posts.forEach(post => {
      const length = post.content?.length || 0;
      const engagement = post.engagement.likes + post.engagement.comments + post.engagement.shares;

      Object.keys(lengthRanges).forEach(range => {
        if (length >= lengthRanges[range].min && length <= lengthRanges[range].max) {
          lengthRanges[range].posts.push(engagement);
        }
      });
    });

    // Calculate averages
    Object.keys(lengthRanges).forEach(range => {
      const posts = lengthRanges[range].posts;
      lengthRanges[range].averageEngagement = posts.length > 0
        ? Math.round(posts.reduce((sum, eng) => sum + eng, 0) / posts.length)
        : 0;
      lengthRanges[range].count = posts.length;
    });

    return lengthRanges;
  }

  // Analyze tone of content
  analyzeTone(text) {
    // Simplified tone analysis - in production, you'd use a proper NLP service
    const positiveWords = ['great', 'amazing', 'awesome', 'excellent', 'fantastic', 'wonderful', 'love', 'excited', 'happy'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'disappointed', 'frustrated', 'angry'];

    const words = text.toLowerCase().split(/\s+/);
    let positiveCount = 0;
    let negativeCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    });

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  // Store analyzed posts for future AI training
  async storeAnalyzedPosts(posts, analysis, platform, accountId) {
    try {
      // In a real implementation, you would store this in a database
      console.log(`Storing ${posts.length} analyzed posts for ${platform} account ${accountId}`);

      return {
        success: true,
        storedPosts: posts.length,
        analysisCategories: Object.keys(analysis).length
      };
    } catch (error) {
      console.error('Error storing analyzed posts:', error);
      throw error;
    }
  }

  // Additional missing methods for complete functionality

  // Analyze hashtag performance
  analyzeHashtagPerformance(posts) {
    const hashtagEngagement = {};

    posts.forEach(post => {
      const hashtags = post.content?.match(/#\w+/g) || [];
      const engagement = post.engagement.likes + post.engagement.comments + post.engagement.shares;

      hashtags.forEach(hashtag => {
        if (!hashtagEngagement[hashtag]) {
          hashtagEngagement[hashtag] = { total: 0, count: 0 };
        }
        hashtagEngagement[hashtag].total += engagement;
        hashtagEngagement[hashtag].count += 1;
      });
    });

    // Calculate averages and sort by performance
    const hashtagPerformance = Object.entries(hashtagEngagement)
      .map(([hashtag, data]) => ({
        hashtag,
        averageEngagement: Math.round(data.total / data.count),
        usageCount: data.count
      }))
      .sort((a, b) => b.averageEngagement - a.averageEngagement)
      .slice(0, 10); // Top 10 performing hashtags

    return hashtagPerformance;
  }

  // Identify successful content themes
  identifySuccessfulThemes(posts) {
    const themes = {};

    posts.forEach(post => {
      const engagement = post.engagement.likes + post.engagement.comments + post.engagement.shares;
      const words = post.content?.toLowerCase().match(/\b\w{4,}\b/g) || [];

      words.forEach(word => {
        if (!themes[word]) {
          themes[word] = { total: 0, count: 0 };
        }
        themes[word].total += engagement;
        themes[word].count += 1;
      });
    });

    // Find themes with high engagement
    const successfulThemes = Object.entries(themes)
      .filter(([word, data]) => data.count >= 3) // Must appear in at least 3 posts
      .map(([word, data]) => ({
        theme: word,
        averageEngagement: Math.round(data.total / data.count),
        frequency: data.count
      }))
      .sort((a, b) => b.averageEngagement - a.averageEngagement)
      .slice(0, 15); // Top 15 themes

    return successfulThemes;
  }

  // Extract best practices for AI training
  extractBestPractices(posts) {
    const topPosts = this.getTopPerformingPosts(posts, 10);

    const practices = {
      commonElements: this.findCommonElements(topPosts),
      successfulFormats: this.identifySuccessfulFormats(topPosts),
      engagementTriggers: this.findEngagementTriggers(topPosts),
      recommendations: this.generateRecommendations(posts)
    };

    return practices;
  }

  // Find common elements in top performing posts
  findCommonElements(topPosts) {
    const elements = {
      hasQuestions: 0,
      hasCallToAction: 0,
      hasEmojis: 0,
      hasHashtags: 0,
      hasMentions: 0,
      hasLinks: 0
    };

    topPosts.forEach(post => {
      const content = post.content || '';
      if (content.includes('?')) elements.hasQuestions++;
      if (/\b(click|visit|check|learn|discover|try|get|buy|shop|follow|share|comment|like)\b/i.test(content)) elements.hasCallToAction++;
      if (/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(content)) elements.hasEmojis++;
      if (content.includes('#')) elements.hasHashtags++;
      if (content.includes('@')) elements.hasMentions++;
      if (/https?:\/\//.test(content)) elements.hasLinks++;
    });

    // Convert to percentages
    Object.keys(elements).forEach(key => {
      elements[key] = Math.round((elements[key] / topPosts.length) * 100);
    });

    return elements;
  }

  // Identify successful post formats
  identifySuccessfulFormats(topPosts) {
    const formats = {};

    topPosts.forEach(post => {
      const content = post.content || '';
      let format = 'text';

      if (content.includes('?')) format = 'question';
      else if (/\b(tip|tips|how to|guide|tutorial)\b/i.test(content)) format = 'educational';
      else if (/\b(excited|happy|proud|thrilled|amazing|great|awesome)\b/i.test(content)) format = 'celebratory';
      else if (/\b(new|launch|announcing|introducing|now available)\b/i.test(content)) format = 'announcement';
      else if (post.media?.url || post.media?.picture) format = 'visual';

      formats[format] = (formats[format] || 0) + 1;
    });

    return formats;
  }

  // Find engagement triggers
  findEngagementTriggers(topPosts) {
    const triggers = [];

    topPosts.forEach(post => {
      const content = post.content || '';

      if (content.includes('?')) triggers.push('Questions drive engagement');
      if (/\b(share|comment|tell us|what do you think)\b/i.test(content)) triggers.push('Direct engagement requests');
      if (/\b(behind the scenes|sneak peek|exclusive)\b/i.test(content)) triggers.push('Exclusive content');
      if (/\b(thank you|grateful|appreciate)\b/i.test(content)) triggers.push('Gratitude expressions');
    });

    return [...new Set(triggers)]; // Remove duplicates
  }

  // Generate recommendations based on analysis
  generateRecommendations(posts) {
    const analysis = this.analyzeEngagement(posts);
    const contentAnalysis = this.analyzeContent(posts);

    const recommendations = [];

    if (analysis?.mediaImpact?.impact === 'positive') {
      recommendations.push('Include visual content (images/videos) to increase engagement');
    }

    if (contentAnalysis.hashtags.length > 0) {
      recommendations.push(`Use relevant hashtags like ${contentAnalysis.hashtags.slice(0, 3).join(', ')}`);
    }

    if (contentAnalysis.averageLength > 0) {
      recommendations.push(`Optimal post length appears to be around ${contentAnalysis.averageLength} characters`);
    }

    recommendations.push('Post consistently to maintain audience engagement');
    recommendations.push('Engage with your audience through questions and calls-to-action');

    return recommendations;
  }

  // Prepare data for AI training
  prepareAITrainingData(posts) {
    const topPosts = this.getTopPerformingPosts(posts, 20);

    return {
      successfulPosts: topPosts.map(post => ({
        content: post.content,
        engagement: post.totalEngagement,
        type: post.type,
        hasMedia: !!(post.media?.url || post.media?.picture)
      })),
      contentPatterns: this.extractContentPatterns(topPosts),
      styleGuide: this.createStyleGuide(topPosts)
    };
  }

  // Extract content patterns for AI
  extractContentPatterns(posts) {
    return {
      commonPhrases: this.findCommonPhrases(posts),
      sentenceStructures: this.analyzeSentenceStructures(posts),
      vocabularyStyle: this.analyzeVocabularyStyle(posts)
    };
  }

  // Find common phrases in successful posts
  findCommonPhrases(posts) {
    const phrases = {};

    posts.forEach(post => {
      const content = post.content || '';
      const words = content.toLowerCase().match(/\b\w+\b/g) || [];

      for (let i = 0; i < words.length - 1; i++) {
        const phrase = `${words[i]} ${words[i + 1]}`;
        phrases[phrase] = (phrases[phrase] || 0) + 1;
      }
    });

    return Object.entries(phrases)
      .filter(([phrase, count]) => count >= 2)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
      .map(([phrase, count]) => ({ phrase, frequency: count }));
  }

  // Analyze sentence structures
  analyzeSentenceStructures(posts) {
    const structures = {
      averageSentenceLength: 0,
      questionRatio: 0,
      exclamationRatio: 0,
      statementRatio: 0
    };

    let totalSentences = 0;
    let totalWords = 0;
    let questions = 0;
    let exclamations = 0;

    posts.forEach(post => {
      const content = post.content || '';
      const sentences = content.split(/[.!?]+/).filter(s => s.trim());

      sentences.forEach(sentence => {
        const words = sentence.trim().split(/\s+/).length;
        totalWords += words;
        totalSentences++;

        if (sentence.includes('?')) questions++;
        else if (sentence.includes('!')) exclamations++;
      });
    });

    if (totalSentences > 0) {
      structures.averageSentenceLength = Math.round(totalWords / totalSentences);
      structures.questionRatio = Math.round((questions / totalSentences) * 100);
      structures.exclamationRatio = Math.round((exclamations / totalSentences) * 100);
      structures.statementRatio = 100 - structures.questionRatio - structures.exclamationRatio;
    }

    return structures;
  }

  // Analyze vocabulary style
  analyzeVocabularyStyle(posts) {
    const allText = posts.map(post => post.content || '').join(' ');
    const words = allText.toLowerCase().match(/\b\w+\b/g) || [];

    const formalWords = ['utilize', 'demonstrate', 'facilitate', 'implement', 'comprehensive'];
    const casualWords = ['awesome', 'cool', 'amazing', 'love', 'super', 'great'];
    const technicalWords = ['algorithm', 'optimization', 'analytics', 'metrics', 'performance'];

    let formalCount = 0;
    let casualCount = 0;
    let technicalCount = 0;

    words.forEach(word => {
      if (formalWords.includes(word)) formalCount++;
      if (casualWords.includes(word)) casualCount++;
      if (technicalWords.includes(word)) technicalCount++;
    });

    const total = formalCount + casualCount + technicalCount;

    return {
      style: total > 0 ? (
        formalCount > casualCount && formalCount > technicalCount ? 'formal' :
          casualCount > technicalCount ? 'casual' : 'technical'
      ) : 'neutral',
      formalRatio: total > 0 ? Math.round((formalCount / total) * 100) : 0,
      casualRatio: total > 0 ? Math.round((casualCount / total) * 100) : 0,
      technicalRatio: total > 0 ? Math.round((technicalCount / total) * 100) : 0
    };
  }

  // Create style guide for AI
  createStyleGuide(posts) {
    const contentAnalysis = this.analyzeContent(posts);
    const commonElements = this.findCommonElements(posts);

    return {
      toneOfVoice: contentAnalysis.toneAnalysis,
      averagePostLength: contentAnalysis.averageLength,
      hashtagUsage: contentAnalysis.hashtags.length > 0,
      emojiUsage: commonElements.hasEmojis > 50,
      questionUsage: commonElements.hasQuestions > 30,
      callToActionUsage: commonElements.hasCallToAction > 40,
      preferredHashtags: contentAnalysis.hashtags.slice(0, 10),
      commonWords: contentAnalysis.commonWords.slice(0, 15)
    };
  }
}

module.exports = new SocialMediaAnalysisService();
