require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');

async function debugOpenAI() {
  console.log('🔍 Detailed OpenAI API Debug\n');

  const apiKey = process.env.OPENAI_API_KEY;
  
  console.log('📋 Environment Check:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   API Key exists: ${!!apiKey}`);
  console.log(`   API Key length: ${apiKey ? apiKey.length : 0}`);
  console.log(`   API Key format: ${apiKey ? apiKey.substring(0, 10) + '...' + apiKey.substring(apiKey.length - 10) : 'N/A'}`);
  
  if (!apiKey) {
    console.log('❌ No API key found in environment');
    return;
  }

  // Test 1: Direct HTTP request to OpenAI
  console.log('\n1️⃣ Testing Direct HTTP Request...');
  try {
    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Say hello' }],
      max_tokens: 5
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Direct HTTP request successful!');
    console.log('📝 Response:', response.data.choices[0].message.content);
  } catch (error) {
    console.log('❌ Direct HTTP request failed:', error.response?.status, error.response?.data?.error?.message || error.message);
  }

  // Test 2: OpenAI SDK
  console.log('\n2️⃣ Testing OpenAI SDK...');
  try {
    const openai = new OpenAI({ apiKey });
    
    const completion = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: 'Say hello' }],
      max_tokens: 5
    });
    
    console.log('✅ OpenAI SDK successful!');
    console.log('📝 Response:', completion.choices[0].message.content);
    console.log('🎯 Model:', completion.model);
    console.log('💰 Tokens:', completion.usage.total_tokens);
    
  } catch (error) {
    console.log('❌ OpenAI SDK failed:', error.status, error.message);
    console.log('🔍 Error details:', JSON.stringify(error.error || {}, null, 2));
  }

  // Test 3: Check API key format
  console.log('\n3️⃣ API Key Format Analysis...');
  const keyParts = apiKey.split('-');
  console.log(`   Parts: ${keyParts.length} (should be 3)`);
  console.log(`   Prefix: ${keyParts[0]} (should be 'sk')`);
  console.log(`   Type: ${keyParts[1]} (should be 'proj' for project keys)`);
  console.log(`   Key length: ${keyParts[2] ? keyParts[2].length : 0} chars`);
  
  // Test 4: Try different models
  console.log('\n4️⃣ Testing Different Models...');
  const models = ['gpt-3.5-turbo', 'gpt-4o-mini'];
  
  for (const model of models) {
    try {
      const openai = new OpenAI({ apiKey });
      const completion = await openai.chat.completions.create({
        model: model,
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 3
      });
      console.log(`✅ ${model}: Working`);
    } catch (error) {
      console.log(`❌ ${model}: ${error.status} - ${error.message}`);
    }
  }

  // Test 5: Check account status
  console.log('\n5️⃣ Checking Account Status...');
  try {
    const response = await axios.get('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      }
    });
    console.log('✅ Account access confirmed');
    console.log(`📊 Available models: ${response.data.data.length}`);
  } catch (error) {
    console.log('❌ Account access failed:', error.response?.status, error.response?.data?.error?.message);
  }
}

debugOpenAI().catch(console.error);
