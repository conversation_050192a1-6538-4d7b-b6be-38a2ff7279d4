require('dotenv').config();
const { supabase, isDemoMode } = require('./config/database');

async function testConnection() {
  console.log('🔍 Testing Supabase connection...');
  console.log('Demo Mode:', isDemoMode);
  console.log('SUPABASE_URL:', process.env.SUPABASE_URL);
  console.log('SUPABASE_ANON_KEY length:', process.env.SUPABASE_ANON_KEY?.length);
  
  if (isDemoMode) {
    console.log('❌ Running in demo mode - Supabase not configured properly');
    return;
  }

  try {
    // Test basic connection
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1);

    if (error) {
      console.log('❌ Database connection failed:', error.message);
      if (error.message.includes('relation "public.users" does not exist')) {
        console.log('📋 Database schema needs to be created');
        console.log('👉 Please run the SQL from database/schema.sql in your Supabase dashboard');
      }
    } else {
      console.log('✅ Database connection successful!');
      console.log('📊 Database is ready for use');
    }
  } catch (err) {
    console.log('❌ Connection test failed:', err.message);
  }
}

testConnection();
