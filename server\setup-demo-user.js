require('dotenv').config();
const { supabase } = require('./config/database');

async function setupDemoUser() {
  console.log('👤 Setting up demo user for LocalPost.ai\n');

  const demoEmail = '<EMAIL>';
  const demoPassword = 'demo123';

  try {
    // Check if demo user already exists
    console.log('1️⃣ Checking if demo user exists...');
    
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', demoEmail)
      .single();

    if (existingUser) {
      console.log('✅ Demo user already exists!');
      console.log(`   📧 Email: ${existingUser.email}`);
      console.log(`   🏢 Business: ${existingUser.business_name}`);
      console.log(`   📍 Location: ${existingUser.location}`);
      console.log(`   💳 Subscription: ${existingUser.subscription_status}`);
      return existingUser;
    }

    // Create demo user in Supabase Auth
    console.log('2️⃣ Creating demo user in Supabase Auth...');
    
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: demoEmail,
      password: demoPassword,
      email_confirm: true
    });

    if (authError) {
      console.log('❌ Auth user creation failed:', authError.message);
      return null;
    }

    console.log('✅ Auth user created successfully!');
    const userId = authData.user.id;

    // Create user profile
    console.log('3️⃣ Creating user profile...');
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email: demoEmail,
        business_name: 'Demo Restaurant',
        business_type: 'restaurant',
        location: 'New York, NY',
        subscription_status: 'active',
        subscription_tier: 'starter',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (userError) {
      console.log('❌ User profile creation failed:', userError.message);
      return null;
    }

    console.log('✅ User profile created successfully!');

    // Create business profile
    console.log('4️⃣ Creating business profile...');
    
    const { data: businessData, error: businessError } = await supabase
      .from('business_profiles')
      .insert({
        user_id: userId,
        business_description: 'A cozy restaurant serving delicious food in the heart of New York City.',
        target_audience: 'Local food lovers, families, young professionals',
        brand_voice: 'friendly, welcoming, professional',
        primary_color: '#3b82f6',
        secondary_color: '#1e40af',
        visual_style: {
          tone: 'warm and inviting',
          imagery: 'food photography, restaurant ambiance',
          colors: ['#3b82f6', '#1e40af', '#f59e0b']
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (businessError) {
      console.log('❌ Business profile creation failed:', businessError.message);
    } else {
      console.log('✅ Business profile created successfully!');
    }

    console.log('\n🎉 Demo user setup complete!');
    console.log('📋 Demo User Details:');
    console.log(`   📧 Email: ${demoEmail}`);
    console.log(`   🔑 Password: ${demoPassword}`);
    console.log(`   🏢 Business: Demo Restaurant`);
    console.log(`   📍 Location: New York, NY`);
    console.log(`   💳 Subscription: Active (Starter)`);

    return userData;

  } catch (error) {
    console.log('❌ Demo user setup failed:', error.message);
    return null;
  }
}

// Test login with demo user
async function testDemoLogin() {
  console.log('\n🔐 Testing demo user login...');
  
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123'
    });

    if (error) {
      console.log('❌ Login failed:', error.message);
      return null;
    }

    console.log('✅ Login successful!');
    console.log(`   🎫 Token: ${data.session.access_token.substring(0, 20)}...`);
    console.log(`   👤 User ID: ${data.user.id}`);
    
    return data.session.access_token;

  } catch (error) {
    console.log('❌ Login test failed:', error.message);
    return null;
  }
}

// Run setup
async function runSetup() {
  const user = await setupDemoUser();
  
  if (user) {
    const token = await testDemoLogin();
    
    if (token) {
      console.log('\n🚀 Demo user is ready for testing!');
      console.log('You can now:');
      console.log('1. Login to the <NAME_EMAIL> / demo123');
      console.log('2. Test content generation');
      console.log('3. Explore all features');
    }
  }
}

runSetup().catch(console.error);
