const axios = require('axios');

async function testWebsiteExtraction() {
  console.log('🌐 Testing Website Extraction API\n');

  const testUrls = [
    'https://example.com',
    'https://www.starbucks.com',
    'https://www.apple.com',
    'invalid-url',
    'https://nonexistent-website-12345.com'
  ];

  for (const url of testUrls) {
    console.log(`\n🔍 Testing URL: ${url}`);
    console.log('─'.repeat(50));

    try {
      const response = await axios.post('http://localhost:5000/api/profile/extract-website', {
        url: url
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        timeout: 30000
      });

      if (response.data.success) {
        const data = response.data.data;
        console.log('✅ Extraction successful!');
        console.log(`📊 Business Name: ${data.businessName}`);
        console.log(`🏢 Industry: ${data.industry}`);
        console.log(`📝 Description: ${data.description?.substring(0, 100)}...`);
        console.log(`📍 Location: ${data.location || 'Not found'}`);
        console.log(`🛠️  Services: ${data.services?.length || 0} found`);
        console.log(`🎨 Brand Colors: ${data.brandColors?.length || 0} found`);
        console.log(`🤖 AI Enhanced: ${data.enhanced ? 'Yes' : 'No'}`);
        
        if (data.contact?.phone) {
          console.log(`📞 Phone: ${data.contact.phone}`);
        }
        if (data.contact?.email) {
          console.log(`📧 Email: ${data.contact.email}`);
        }
        
        const socialCount = Object.keys(data.socialLinks || {}).length;
        if (socialCount > 0) {
          console.log(`📱 Social Links: ${socialCount} found`);
        }
      } else {
        console.log('❌ Extraction failed');
        console.log(`Error: ${response.data.error}`);
      }

    } catch (error) {
      console.log('❌ Request failed');
      if (error.response?.data) {
        console.log(`Error: ${error.response.data.error}`);
        if (error.response.data.details) {
          console.log(`Details: ${error.response.data.details}`);
        }
      } else {
        console.log(`Error: ${error.message}`);
      }
    }
  }

  console.log('\n🎯 Test Summary:');
  console.log('The website extraction API should:');
  console.log('✅ Successfully extract data from valid websites');
  console.log('❌ Gracefully handle invalid URLs');
  console.log('❌ Gracefully handle non-existent websites');
  console.log('🤖 Enhance data with AI when possible');
  console.log('📊 Return structured business information');
}

// Test individual extraction service
async function testExtractionService() {
  console.log('\n🔧 Testing Website Extraction Service Directly\n');

  try {
    const websiteExtractionService = require('./services/websiteExtraction');
    
    const testUrl = 'https://example.com';
    console.log(`Testing direct service with: ${testUrl}`);
    
    const result = await websiteExtractionService.extractBusinessData(testUrl);
    
    if (result.success) {
      console.log('✅ Direct service test successful!');
      console.log('📊 Extracted data keys:', Object.keys(result.data));
      console.log('🎯 Business name:', result.data.businessName);
      console.log('📝 Description length:', result.data.description?.length || 0);
    } else {
      console.log('❌ Direct service test failed:', result.error);
    }

  } catch (error) {
    console.log('❌ Direct service error:', error.message);
  }
}

// Run tests
async function runAllTests() {
  await testExtractionService();
  await testWebsiteExtraction();
  
  console.log('\n🎉 Website extraction testing complete!');
  console.log('💡 You can now test the feature in the UI at http://localhost:3000');
  console.log('📝 Go to Business Profile > Website Extract tab');
}

runAllTests().catch(console.error);
