{"name": "localpost-ai", "version": "1.0.0", "description": "AI-powered local social media content generation platform", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm start", "build": "cd client && npm run build", "server:start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ai", "social-media", "local-business", "content-generation"], "author": "LocalPost.ai", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}