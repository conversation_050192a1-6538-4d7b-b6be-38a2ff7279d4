const axios = require('axios');
require('dotenv').config();

async function testUserAuthentication() {
  try {
    console.log('🔐 Testing User Authentication & Content Generation...\n');

    const email = '<EMAIL>';
    const password = 'qwerty123456';

    // Step 1: Test login
    console.log('1. Testing login with your credentials:');
    let authToken = null;
    
    try {
      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
        email: email,
        password: password
      });
      
      console.log('✅ Login successful!');
      console.log('   User ID:', loginResponse.data.user?.id);
      console.log('   Email:', loginResponse.data.user?.email);
      authToken = loginResponse.data.token;
      console.log('   Token received:', authToken ? 'Yes' : 'No');
      
    } catch (error) {
      console.log('❌ Login failed:');
      console.log('   Status:', error.response?.status);
      console.log('   Error:', error.response?.data);
      
      if (error.response?.status === 401) {
        console.log('   🔑 Invalid credentials - check email/password');
      } else if (error.response?.status === 404) {
        console.log('   👤 User not found - may need to register first');
      }
      return;
    }

    // Step 2: Test content generation with valid token
    console.log('\n2. Testing content generation with authentication:');
    
    try {
      const contentResponse = await axios.post('http://localhost:5000/api/content/generate', {
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        platforms: ['facebook', 'instagram'],
        contentTypes: ['promotional', 'educational']
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Content generation successful!');
      console.log('   Generated posts:', contentResponse.data.posts?.length || 0);
      
      if (contentResponse.data.posts && contentResponse.data.posts.length > 0) {
        const firstPost = contentResponse.data.posts[0];
        console.log('   Sample post:');
        console.log('     Text:', firstPost.content_text?.substring(0, 100) + '...');
        console.log('     Platform:', firstPost.platform);
        console.log('     Date:', firstPost.scheduled_date);
      }
      
    } catch (error) {
      console.log('❌ Content generation failed:');
      console.log('   Status:', error.response?.status);
      console.log('   Error:', error.response?.data);
      
      if (error.response?.status === 401) {
        console.log('   🔑 Authentication issue - token may be invalid');
      } else if (error.response?.status === 403) {
        console.log('   🚫 Permission issue - subscription may be required');
      } else if (error.response?.status === 400) {
        console.log('   📝 Validation error - check request format');
      } else if (error.response?.status === 500) {
        console.log('   🔧 Server error - check backend logs');
      }
    }

    // Step 3: Test user profile
    console.log('\n3. Testing user profile access:');
    
    try {
      const profileResponse = await axios.get('http://localhost:5000/api/profile', {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });
      
      console.log('✅ Profile access successful!');
      console.log('   Business name:', profileResponse.data.business_name || 'Not set');
      console.log('   Business type:', profileResponse.data.business_type || 'Not set');
      
    } catch (error) {
      console.log('❌ Profile access failed:', error.response?.status, error.response?.data);
    }

    console.log('\n🎯 Authentication Test Complete!');
    console.log('\nIf content generation is still failing in the UI:');
    console.log('1. Make sure you are logged in with: <EMAIL>');
    console.log('2. Check browser console for specific error messages');
    console.log('3. Try logging out and logging back in');
    console.log('4. Check if your business profile is complete');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testUserAuthentication();
