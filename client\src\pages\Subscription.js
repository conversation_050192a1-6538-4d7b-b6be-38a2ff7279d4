import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  CreditCard, 
  CheckCircle, 
  AlertCircle,
  Calendar,
  DollarSign,
  Users,
  Zap
} from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import toast from 'react-hot-toast';

const Subscription = () => {
  const { user, apiCall } = useAuth();
  const [loading, setLoading] = useState(true);
  const [subscriptionData, setSubscriptionData] = useState(null);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [showUpgrade, setShowUpgrade] = useState(false);

  useEffect(() => {
    fetchSubscriptionStatus();
  }, []);

  const fetchSubscriptionStatus = async () => {
    try {
      setLoading(true);
      
      const response = await apiCall('/api/subscriptions/status');
      
      if (response.ok) {
        const data = await response.json();
        setSubscriptionData(data);
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
      toast.error('Failed to load subscription data');
    } finally {
      setLoading(false);
    }
  };

  const handleCancelSubscription = async () => {
    if (!window.confirm('Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your current billing period.')) {
      return;
    }

    try {
      const response = await apiCall('/api/subscriptions/cancel', {
        method: 'POST'
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Subscription canceled. You will retain access until the end of your billing period.');
        fetchSubscriptionStatus();
      } else {
        toast.error('Failed to cancel subscription');
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      toast.error('Failed to cancel subscription');
    }
  };

  const handleReactivateSubscription = async () => {
    try {
      const response = await apiCall('/api/subscriptions/reactivate', {
        method: 'POST'
      });

      if (response.ok) {
        toast.success('Subscription reactivated successfully!');
        fetchSubscriptionStatus();
      } else {
        toast.error('Failed to reactivate subscription');
      }
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      toast.error('Failed to reactivate subscription');
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading subscription..." />;
  }

  const plans = subscriptionData?.plans || {};
  const currentPlan = subscriptionData?.tier || 'starter';
  const currentStatus = subscriptionData?.status || 'trial';

  const statusColors = {
    active: 'text-green-600 bg-green-100',
    trial: 'text-blue-600 bg-blue-100',
    canceled: 'text-red-600 bg-red-100',
    past_due: 'text-yellow-600 bg-yellow-100'
  };

  const statusLabels = {
    active: 'Active',
    trial: 'Free Trial',
    canceled: 'Canceled',
    past_due: 'Past Due'
  };

  return (
    <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Subscription</h1>
        <p className="text-gray-600 mt-2">
          Manage your LocalPost.ai subscription and billing
        </p>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Current Subscription */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Current Plan</h2>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${statusColors[currentStatus] || 'text-gray-600 bg-gray-100'}`}>
                {statusLabels[currentStatus] || currentStatus}
              </span>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-600">Plan</span>
                <span className="font-medium text-gray-900 capitalize">
                  {currentPlan} Plan
                </span>
              </div>

              {subscriptionData?.currentPeriodEnd && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">
                    {subscriptionData.cancelAtPeriodEnd ? 'Expires' : 'Renews'}
                  </span>
                  <span className="font-medium text-gray-900">
                    {new Date(subscriptionData.currentPeriodEnd).toLocaleDateString()}
                  </span>
                </div>
              )}

              <div className="flex items-center justify-between">
                <span className="text-gray-600">Monthly Price</span>
                <span className="font-medium text-gray-900">
                  ${plans[currentPlan]?.price ? (plans[currentPlan].price / 100).toFixed(2) : '0.00'}
                </span>
              </div>
            </div>

            {/* Plan Features */}
            {plans[currentPlan]?.features && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Plan Features</h3>
                <ul className="space-y-2">
                  {plans[currentPlan].features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <CheckCircle className="text-green-500" size={16} />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Actions */}
            <div className="mt-6 pt-6 border-t border-gray-200 flex space-x-4">
              {currentStatus === 'active' && !subscriptionData?.cancelAtPeriodEnd && (
                <button
                  onClick={handleCancelSubscription}
                  className="text-red-600 hover:text-red-700 font-medium"
                >
                  Cancel Subscription
                </button>
              )}

              {subscriptionData?.cancelAtPeriodEnd && (
                <button
                  onClick={handleReactivateSubscription}
                  className="btn-primary"
                >
                  Reactivate Subscription
                </button>
              )}

              <button
                onClick={() => setShowUpgrade(true)}
                className="btn-secondary"
              >
                {currentPlan === 'agency' ? 'Change Plan' : 'Upgrade Plan'}
              </button>
            </div>
          </div>

          {/* Billing History */}
          <div className="card mt-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">Billing History</h2>
            
            <div className="text-center py-8">
              <CreditCard className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No billing history</h3>
              <p className="mt-1 text-sm text-gray-500">
                Your billing history will appear here once you have transactions.
              </p>
            </div>
          </div>
        </div>

        {/* Plan Comparison */}
        <div className="space-y-6">
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage This Month</h3>
            
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-600">Posts Generated</span>
                  <span className="text-sm font-medium text-gray-900">12 / 30</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-primary-600 h-2 rounded-full" style={{ width: '40%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex items-center justify-between mb-1">
                  <span className="text-sm text-gray-600">Platforms</span>
                  <span className="text-sm font-medium text-gray-900">2 / 2</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-primary-600 h-2 rounded-full" style={{ width: '100%' }}></div>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Stats</h3>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="text-gray-400" size={16} />
                  <span className="text-sm text-gray-600">Member Since</span>
                </div>
                <span className="text-sm font-medium text-gray-900">
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Zap className="text-gray-400" size={16} />
                  <span className="text-sm text-gray-600">Total Posts</span>
                </div>
                <span className="text-sm font-medium text-gray-900">47</span>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Users className="text-gray-400" size={16} />
                  <span className="text-sm text-gray-600">Locations</span>
                </div>
                <span className="text-sm font-medium text-gray-900">1</span>
              </div>
            </div>
          </div>

          {/* Support */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
            
            <div className="space-y-3">
              <a
                href="mailto:<EMAIL>"
                className="block text-primary-600 hover:text-primary-700 text-sm"
              >
                Contact Support
              </a>
              <a
                href="#"
                className="block text-primary-600 hover:text-primary-700 text-sm"
              >
                View Documentation
              </a>
              <a
                href="#"
                className="block text-primary-600 hover:text-primary-700 text-sm"
              >
                Feature Requests
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Upgrade Modal */}
      {showUpgrade && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-gray-900">Choose Your Plan</h2>
                <button
                  onClick={() => setShowUpgrade(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>

              <div className="grid md:grid-cols-3 gap-6">
                {Object.entries(plans).map(([planId, plan]) => (
                  <div
                    key={planId}
                    className={`border-2 rounded-lg p-6 ${
                      planId === currentPlan
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold text-gray-900">{plan.name}</h3>
                      <div className="mt-2">
                        <span className="text-3xl font-bold text-gray-900">
                          ${(plan.price / 100).toFixed(0)}
                        </span>
                        <span className="text-gray-600">/month</span>
                      </div>
                    </div>

                    <ul className="space-y-2 mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <CheckCircle className="text-green-500" size={16} />
                          <span className="text-sm text-gray-700">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    <button
                      disabled={planId === currentPlan}
                      className={`w-full py-2 px-4 rounded-lg font-medium ${
                        planId === currentPlan
                          ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                          : 'bg-primary-600 hover:bg-primary-700 text-white'
                      }`}
                    >
                      {planId === currentPlan ? 'Current Plan' : 'Select Plan'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Subscription;
