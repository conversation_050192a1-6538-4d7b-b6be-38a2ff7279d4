import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePosts } from '../contexts/PostsContext';
import {
  Calendar as CalendarIcon,
  ChevronLeft,
  ChevronRight,
  Edit,
  Trash2,
  RefreshCw,
  Plus,
  Filter
} from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import PostModal from '../components/PostModal';

const ContentCalendar = () => {
  const { apiCall } = useAuth();
  const { posts, getPostsForDateRange, deletePost, updatePost: updatePostInContext, addPostsToCalendar, loading: postsLoading } = usePosts();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [loading, setLoading] = useState(false);
  const [selectedPost, setSelectedPost] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filter, setFilter] = useState('all'); // all, facebook, instagram, linkedin, twitter

  // Get posts for current month from context
  const getCurrentMonthPosts = () => {
    const startOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
    const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    return getPostsForDateRange(startOfMonth, endOfMonth);
  };

  const currentMonthPosts = getCurrentMonthPosts();

  // Load posts from API if not already loaded
  useEffect(() => {
    const loadPosts = async () => {
      if (posts.length === 0) {
        try {
          setLoading(true);
          // Get current month date range
          const now = new Date();
          const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
          const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

          // Fetch posts for current month
          const response = await apiCall(`/api/content/posts?startDate=${startOfMonth.toISOString().split('T')[0]}&endDate=${endOfMonth.toISOString().split('T')[0]}`);

          if (response.ok) {
            const data = await response.json();
            const apiPosts = data.posts || [];

            // Convert API posts to calendar format
            const calendarPosts = apiPosts.map(post => ({
              id: post.id,
              title: (post.content_text || '').split('\n')[0].substring(0, 50) + '...',
              content: post.content_text || '',
              platform: post.platform,
              scheduledDate: post.date_scheduled,
              status: post.status || 'generated',
              hashtags: post.hashtags || [],
              weather: post.weather_context || '',
              localEvent: post.events_context || '',
              imageDesign: post.image_url ? { mockUrl: post.image_url } : null,
              createdAt: post.created_at,
              updatedAt: post.updated_at
            }));

            // Add posts to context
            await addPostsToCalendar(calendarPosts);
          }
        } catch (error) {
          console.error('Error loading posts:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    loadPosts();
  }, [posts.length, apiCall, addPostsToCalendar]);

  // Content generation is now handled in the GenerateContent page

  const handleDeletePost = async (postId) => {
    await deletePost(postId);
  };

  const regeneratePost = async (postId) => {
    try {
      // For now, just show a message that this feature is coming soon
      console.log('Regenerate post:', postId);
      // In a real app, you would call the API to regenerate the post
    } catch (error) {
      console.error('Error regenerating post:', error);
    }
  };

  const openPostModal = (post) => {
    setSelectedPost(post);
    setIsModalOpen(true);
  };

  const closePostModal = () => {
    setSelectedPost(null);
    setIsModalOpen(false);
  };

  const handleUpdatePost = async (updatedPost) => {
    await updatePostInContext(updatedPost.id, updatedPost);
  };

  // Calendar helpers
  const getDaysInMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const navigateMonth = (direction) => {
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + direction);
    setCurrentDate(newDate);
  };

  const getPostsForDate = (day) => {
    const dateStr = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
      .toISOString().split('T')[0];

    let dayPosts = currentMonthPosts.filter(post => {
      const postDate = new Date(post.scheduledDate).toISOString().split('T')[0];
      return postDate === dateStr;
    });

    if (filter !== 'all') {
      dayPosts = dayPosts.filter(post => post.platform === filter);
    }

    return dayPosts;
  };

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const platformColors = {
    facebook: 'bg-blue-500',
    instagram: 'bg-pink-500',
    linkedin: 'bg-blue-700',
    twitter: 'bg-blue-400'
  };

  if (loading) {
    return <LoadingSpinner text="Loading calendar..." />;
  }

  const daysInMonth = getDaysInMonth(currentDate);
  const firstDay = getFirstDayOfMonth(currentDate);
  const days = Array.from({ length: daysInMonth }, (_, i) => i + 1);
  const emptyDays = Array.from({ length: firstDay }, (_, i) => i);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Content Calendar</h1>
          <p className="text-gray-600 mt-2">
            Manage and schedule your social media content
          </p>
        </div>

        <div className="flex items-center space-x-4 mt-4 sm:mt-0">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="input-field text-sm"
          >
            <option value="all">All Platforms</option>
            <option value="facebook">Facebook</option>
            <option value="instagram">Instagram</option>
            <option value="linkedin">LinkedIn</option>
            <option value="twitter">Twitter</option>
          </select>

          <button
            onClick={() => window.location.href = '/dashboard/generate'}
            className="btn-primary flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>Generate Content</span>
          </button>
        </div>
      </div>

      {/* Calendar Navigation */}
      <div className="card mb-6">
        <div className="flex items-center justify-between mb-6">
          <button
            onClick={() => navigateMonth(-1)}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ChevronLeft size={20} />
          </button>

          <h2 className="text-xl font-semibold text-gray-900">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h2>

          <button
            onClick={() => navigateMonth(1)}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <ChevronRight size={20} />
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Day headers */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="p-3 text-center text-sm font-medium text-gray-500">
              {day}
            </div>
          ))}

          {/* Empty days */}
          {emptyDays.map(day => (
            <div key={`empty-${day}`} className="p-3 h-32"></div>
          ))}

          {/* Calendar days */}
          {days.map(day => {
            const dayPosts = getPostsForDate(day);
            const isToday = new Date().toDateString() ===
              new Date(currentDate.getFullYear(), currentDate.getMonth(), day).toDateString();

            return (
              <div
                key={day}
                className={`p-2 h-32 border border-gray-200 ${isToday ? 'bg-primary-50 border-primary-200' : 'bg-white'
                  }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-sm font-medium ${isToday ? 'text-primary-600' : 'text-gray-900'
                    }`}>
                    {day}
                  </span>
                </div>

                <div className="space-y-1 overflow-y-auto max-h-24">
                  {dayPosts.map(post => (
                    <div
                      key={post.id}
                      onClick={() => openPostModal(post)}
                      className={`text-xs p-2 rounded cursor-pointer hover:opacity-80 text-white ${platformColors[post.platform] || 'bg-gray-500'
                        }`}
                    >
                      <div className="flex items-center space-x-2">
                        {post.imageDesign && (
                          <div className="w-6 h-6 rounded overflow-hidden flex-shrink-0">
                            <img
                              src={post.imageDesign.mockUrl}
                              alt="Post image"
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="font-medium capitalize">{post.platform}</div>
                          <div className="truncate">{post.content ? post.content.substring(0, 25) : post.title}...</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Post Modal */}
      {isModalOpen && selectedPost && (
        <PostModal
          post={selectedPost}
          isOpen={isModalOpen}
          onClose={closePostModal}
          onUpdate={handleUpdatePost}
          onDelete={handleDeletePost}
          onRegenerate={regeneratePost}
        />
      )}
    </div>
  );
};

export default ContentCalendar;
