require('dotenv').config();

function checkKeyFormat() {
  const apiKey = process.env.OPENAI_API_KEY;
  
  console.log('🔍 Detailed Key Format Check\n');
  
  if (!apiKey) {
    console.log('❌ No API key found');
    return;
  }
  
  console.log('📋 Raw Key Analysis:');
  console.log(`   Full length: ${apiKey.length}`);
  console.log(`   First 20 chars: "${apiKey.substring(0, 20)}"`);
  console.log(`   Last 20 chars: "${apiKey.substring(apiKey.length - 20)}"`);
  
  // Check for hidden characters
  console.log('\n🔍 Character Analysis:');
  for (let i = 0; i < Math.min(30, apiKey.length); i++) {
    const char = apiKey[i];
    const code = char.charCodeAt(0);
    if (code < 32 || code > 126) {
      console.log(`   Position ${i}: Hidden character (code: ${code})`);
    }
  }
  
  // Split by dashes
  const parts = apiKey.split('-');
  console.log('\n📊 Key Parts:');
  parts.forEach((part, index) => {
    console.log(`   Part ${index + 1}: "${part}" (length: ${part.length})`);
  });
  
  // Expected format for project keys: sk-proj-{long-string}
  console.log('\n✅ Format Validation:');
  console.log(`   Starts with 'sk-': ${apiKey.startsWith('sk-') ? '✅' : '❌'}`);
  console.log(`   Has 'proj': ${apiKey.includes('proj') ? '✅' : '❌'}`);
  console.log(`   Total parts: ${parts.length} ${parts.length === 3 ? '✅' : '❌ (should be 3)'}`);
  
  // Check if it's a valid base64-like string
  const keyPart = parts[2] || '';
  const validChars = /^[A-Za-z0-9_-]+$/.test(keyPart);
  console.log(`   Valid characters: ${validChars ? '✅' : '❌'}`);
  
  // Reconstruct the key to check for issues
  if (parts.length >= 3) {
    const reconstructed = `${parts[0]}-${parts[1]}-${parts.slice(2).join('-')}`;
    console.log(`   Reconstructed matches: ${reconstructed === apiKey ? '✅' : '❌'}`);
    
    if (reconstructed !== apiKey) {
      console.log(`   Original:      "${apiKey}"`);
      console.log(`   Reconstructed: "${reconstructed}"`);
    }
  }
}

checkKeyFormat();
