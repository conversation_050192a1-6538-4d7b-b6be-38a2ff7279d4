const axios = require('axios');

class EventsService {
  constructor() {
    this.eventbriteApiKey = process.env.EVENTBRITE_API_KEY;
    this.baseUrl = 'https://www.eventbriteapi.com/v3';
    
    if (!this.eventbriteApiKey) {
      console.warn('Eventbrite API key not provided. Events features will use mock data.');
    }
  }

  // Get local events for a location
  async getLocalEvents(location, dateRange = { start: new Date(), end: null }) {
    if (!this.eventbriteApiKey) {
      return this.getMockEvents(location);
    }

    try {
      // First, get location coordinates (simplified - in production, use proper geocoding)
      const locationQuery = this.parseLocation(location);
      
      const endDate = dateRange.end || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days from now
      
      const response = await axios.get(`${this.baseUrl}/events/search/`, {
        headers: {
          'Authorization': `Bearer ${this.eventbriteApiKey}`
        },
        params: {
          'location.address': locationQuery,
          'location.within': '25mi', // 25 mile radius
          'start_date.range_start': dateRange.start.toISOString(),
          'start_date.range_end': endDate.toISOString(),
          'categories': '103,105,108,110,113,116', // Business, Community, Arts, Music, etc.
          'sort_by': 'date',
          'expand': 'venue,category',
          'page_size': 20
        }
      });

      const events = response.data.events || [];
      
      return events.map(event => ({
        id: event.id,
        name: event.name.text,
        description: event.description?.text || '',
        startDate: new Date(event.start.utc),
        endDate: new Date(event.end.utc),
        venue: event.venue ? {
          name: event.venue.name,
          address: event.venue.address ? 
            `${event.venue.address.address_1}, ${event.venue.address.city}` : 
            'Location TBD'
        } : null,
        category: event.category?.name || 'Community',
        url: event.url,
        isFree: event.is_free,
        attendance: this.estimateAttendance(event)
      }));

    } catch (error) {
      console.error('Eventbrite API error:', error.message);
      return this.getMockEvents(location);
    }
  }

  // Get events for content generation context
  async getEventsForContent(location, targetDate) {
    const dateRange = {
      start: new Date(targetDate.getTime() - 7 * 24 * 60 * 60 * 1000), // 7 days before
      end: new Date(targetDate.getTime() + 7 * 24 * 60 * 60 * 1000)    // 7 days after
    };

    const events = await this.getLocalEvents(location, dateRange);
    
    // Filter for relevant events
    return events.filter(event => {
      const eventDate = new Date(event.startDate);
      const daysDiff = Math.abs(eventDate - targetDate) / (1000 * 60 * 60 * 24);
      
      // Include events within 3 days and with decent attendance
      return daysDiff <= 3 && event.attendance > 50;
    });
  }

  // Get holiday and observance data
  async getHolidays(location, year = new Date().getFullYear()) {
    try {
      // Using a free holidays API (you might want to use a more comprehensive one)
      const response = await axios.get(`https://date.nager.at/api/v3/PublicHolidays/${year}/US`);
      
      const holidays = response.data || [];
      
      return holidays.map(holiday => ({
        name: holiday.name,
        date: new Date(holiday.date),
        type: holiday.type || 'public',
        global: holiday.global || false,
        counties: holiday.counties || []
      }));

    } catch (error) {
      console.error('Holidays API error:', error.message);
      return this.getMockHolidays();
    }
  }

  // Get seasonal events and observances
  getSeasonalEvents(date) {
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    const seasonalEvents = {
      // January
      1: [
        { name: "New Year's Day", date: 1 },
        { name: "National Mentoring Month", type: "awareness" }
      ],
      // February
      2: [
        { name: "Valentine's Day", date: 14 },
        { name: "Presidents Day", date: 15, type: "federal" },
        { name: "Black History Month", type: "awareness" }
      ],
      // March
      3: [
        { name: "St. Patrick's Day", date: 17 },
        { name: "Women's History Month", type: "awareness" }
      ],
      // April
      4: [
        { name: "Easter", date: "varies" },
        { name: "Earth Day", date: 22 },
        { name: "National Small Business Week", type: "awareness" }
      ],
      // May
      5: [
        { name: "Mother's Day", date: "second Sunday" },
        { name: "Memorial Day", date: "last Monday", type: "federal" },
        { name: "National Small Business Week", type: "awareness" }
      ],
      // June
      6: [
        { name: "Father's Day", date: "third Sunday" },
        { name: "Pride Month", type: "awareness" }
      ],
      // July
      7: [
        { name: "Independence Day", date: 4, type: "federal" },
        { name: "Summer season", type: "seasonal" }
      ],
      // August
      8: [
        { name: "Back to School season", type: "seasonal" },
        { name: "National Small Business Week", type: "awareness" }
      ],
      // September
      9: [
        { name: "Labor Day", date: "first Monday", type: "federal" },
        { name: "Back to School", type: "seasonal" }
      ],
      // October
      10: [
        { name: "Halloween", date: 31 },
        { name: "Breast Cancer Awareness Month", type: "awareness" },
        { name: "Small Business Month", type: "awareness" }
      ],
      // November
      11: [
        { name: "Thanksgiving", date: "fourth Thursday", type: "federal" },
        { name: "Black Friday", date: "day after Thanksgiving" },
        { name: "Small Business Saturday", date: "Saturday after Thanksgiving" }
      ],
      // December
      12: [
        { name: "Christmas", date: 25, type: "federal" },
        { name: "New Year's Eve", date: 31 },
        { name: "Holiday Shopping Season", type: "seasonal" }
      ]
    };

    return seasonalEvents[month] || [];
  }

  // Generate event-based content suggestions
  getEventContentSuggestions(events, businessType) {
    const suggestions = [];

    events.forEach(event => {
      const eventSuggestions = this.getEventSpecificSuggestions(event, businessType);
      if (eventSuggestions.length > 0) {
        suggestions.push({
          event: event.name,
          date: event.startDate,
          suggestions: eventSuggestions
        });
      }
    });

    return suggestions;
  }

  // Get business-specific event suggestions
  getEventSpecificSuggestions(event, businessType) {
    const eventName = event.name.toLowerCase();
    const category = event.category.toLowerCase();
    
    const suggestions = {
      'restaurant': this.getRestaurantEventSuggestions(eventName, category),
      'retail': this.getRetailEventSuggestions(eventName, category),
      'service': this.getServiceEventSuggestions(eventName, category),
      'fitness': this.getFitnessEventSuggestions(eventName, category),
      'default': this.getDefaultEventSuggestions(eventName, category)
    };

    return suggestions[businessType] || suggestions['default'];
  }

  // Helper methods for business-specific suggestions
  getRestaurantEventSuggestions(eventName, category) {
    if (category.includes('music') || eventName.includes('concert')) {
      return ['Pre-show dining specials', 'Post-event late night menu', 'Live music atmosphere'];
    }
    if (category.includes('sports') || eventName.includes('game')) {
      return ['Game day specials', 'Watch party hosting', 'Team spirit menu items'];
    }
    if (category.includes('festival')) {
      return ['Festival-themed menu', 'Catering services', 'Special event hours'];
    }
    return ['Event-themed specials', 'Extended hours for event-goers'];
  }

  getRetailEventSuggestions(eventName, category) {
    if (category.includes('arts') || eventName.includes('art')) {
      return ['Art-inspired products', 'Local artist collaborations', 'Creative supplies'];
    }
    if (category.includes('sports')) {
      return ['Team merchandise', 'Sports equipment', 'Fan gear'];
    }
    return ['Event-related products', 'Special event discounts'];
  }

  getServiceEventSuggestions(eventName, category) {
    return ['Event preparation services', 'Special event rates', 'Extended availability'];
  }

  getFitnessEventSuggestions(eventName, category) {
    if (eventName.includes('run') || eventName.includes('marathon')) {
      return ['Training programs', 'Pre-race preparation', 'Recovery services'];
    }
    return ['Event fitness preparation', 'Special classes'];
  }

  getDefaultEventSuggestions(eventName, category) {
    return ['Community involvement', 'Event support', 'Local partnership opportunities'];
  }

  // Helper methods
  parseLocation(location) {
    // Simple location parsing - in production, use proper geocoding
    return location.replace(/,/g, ' ');
  }

  estimateAttendance(event) {
    // Simple attendance estimation based on event data
    if (event.capacity) return event.capacity;
    if (event.is_free) return Math.random() * 500 + 100;
    return Math.random() * 200 + 50;
  }

  // Mock data for development/fallback
  getMockEvents(location) {
    const mockEvents = [
      {
        id: 'mock-1',
        name: 'Local Farmers Market',
        description: 'Weekly farmers market featuring local vendors',
        startDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 4 * 60 * 60 * 1000),
        venue: { name: 'Downtown Square', address: `${location} Downtown` },
        category: 'Community',
        url: '#',
        isFree: true,
        attendance: 300
      },
      {
        id: 'mock-2',
        name: 'Summer Concert Series',
        description: 'Free outdoor concert in the park',
        startDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000 + 3 * 60 * 60 * 1000),
        venue: { name: 'City Park', address: `${location} City Park` },
        category: 'Music',
        url: '#',
        isFree: true,
        attendance: 500
      },
      {
        id: 'mock-3',
        name: 'Business Networking Mixer',
        description: 'Monthly networking event for local businesses',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000),
        venue: { name: 'Chamber of Commerce', address: `${location} Business District` },
        category: 'Business',
        url: '#',
        isFree: false,
        attendance: 150
      }
    ];

    return mockEvents;
  }

  getMockHolidays() {
    const currentYear = new Date().getFullYear();
    return [
      { name: "New Year's Day", date: new Date(currentYear, 0, 1), type: 'federal', global: true },
      { name: "Valentine's Day", date: new Date(currentYear, 1, 14), type: 'observance', global: false },
      { name: "St. Patrick's Day", date: new Date(currentYear, 2, 17), type: 'observance', global: false },
      { name: "Easter", date: new Date(currentYear, 3, 12), type: 'observance', global: false },
      { name: "Mother's Day", date: new Date(currentYear, 4, 10), type: 'observance', global: false },
      { name: "Memorial Day", date: new Date(currentYear, 4, 25), type: 'federal', global: true },
      { name: "Father's Day", date: new Date(currentYear, 5, 21), type: 'observance', global: false },
      { name: "Independence Day", date: new Date(currentYear, 6, 4), type: 'federal', global: true },
      { name: "Labor Day", date: new Date(currentYear, 8, 7), type: 'federal', global: true },
      { name: "Halloween", date: new Date(currentYear, 9, 31), type: 'observance', global: false },
      { name: "Thanksgiving", date: new Date(currentYear, 10, 26), type: 'federal', global: true },
      { name: "Christmas", date: new Date(currentYear, 11, 25), type: 'federal', global: true }
    ];
  }
}

module.exports = new EventsService();
