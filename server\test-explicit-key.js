const fs = require('fs');
const path = require('path');
const OpenAI = require('openai');

async function testExplicitKey() {
  console.log('🔑 Testing OpenAI with Explicit Key from .env File\n');

  // Read the .env file directly
  const envPath = path.join(__dirname, '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  // Extract the OpenAI API key
  const keyMatch = envContent.match(/OPENAI_API_KEY=(.+)/);
  if (!keyMatch) {
    console.log('❌ No OpenAI API key found in .env file');
    return;
  }
  
  const apiKey = keyMatch[1].replace(/"/g, '').trim();
  
  console.log('📋 API Key from .env file:');
  console.log(`   Length: ${apiKey.length} characters`);
  console.log(`   Starts with: ${apiKey.substring(0, 8)}`);
  console.log(`   Ends with: ${apiKey.substring(apiKey.length - 8)}`);
  console.log(`   Format: ${apiKey.startsWith('sk-') ? '✅ Correct' : '❌ Invalid'}`);

  if (!apiKey.startsWith('sk-')) {
    console.log('\n❌ Invalid API key format');
    return;
  }

  console.log('\n🧪 Testing API Connection with explicit key...');
  
  try {
    const openai = new OpenAI({ apiKey });
    
    // Try the simplest possible API call
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Say 'Hello World'"
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    console.log('🎉 SUCCESS! OpenAI API is working!');
    console.log('📝 Response:', completion.choices[0].message.content);
    console.log('🎯 Model used:', completion.model);
    console.log('💰 Tokens used:', completion.usage.total_tokens);
    
    // Test real content generation for LocalPost.ai
    console.log('\n🎨 Testing LocalPost.ai Content Generation...');
    
    const contentCompletion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert social media content creator for local businesses. Always respond with valid JSON only."
        },
        {
          role: "user",
          content: `Create a Facebook post for a restaurant called "Demo Restaurant" in New York, NY. 
                   Current weather: sunny, 71°F. Make it engaging, weather-aware, and locally relevant.
                   Return ONLY valid JSON with this exact format:
                   {"text": "post content here", "hashtags": ["tag1", "tag2", "tag3"], "imagePrompt": "detailed image description"}`
        }
      ],
      max_tokens: 400,
      temperature: 0.7,
    });

    const aiContent = contentCompletion.choices[0].message.content;
    console.log('✅ Real AI content generation successful!');
    
    try {
      const parsedContent = JSON.parse(aiContent);
      console.log('\n🎯 AI-Generated Content for LocalPost.ai:');
      console.log('📄 Text:', parsedContent.text);
      console.log('🏷️  Hashtags:', parsedContent.hashtags?.join(', '));
      console.log('🖼️  Image Prompt:', parsedContent.imagePrompt);
      
      console.log('\n🌟 This is what your users will get instead of mock data!');
      
    } catch (parseError) {
      console.log('📝 Raw AI Response:', aiContent);
      console.log('⚠️  JSON parsing needs adjustment, but AI generation works!');
    }
    
    // Test image generation
    console.log('\n🖼️  Testing DALL-E Image Generation...');
    
    try {
      const imageResponse = await openai.images.generate({
        model: "dall-e-3",
        prompt: "A cozy restaurant interior with warm lighting, wooden tables, and a welcoming atmosphere for a sunny day. Professional food photography style, high quality, appetizing.",
        size: "1024x1024",
        quality: "standard",
        n: 1,
      });

      console.log('✅ AI image generation successful!');
      console.log('🖼️  Image URL:', imageResponse.data[0].url);
      console.log('💡 Image will be available for download for 1 hour');
      
    } catch (imageError) {
      console.log('⚠️  Image generation error:', imageError.message);
    }
    
    return true;

  } catch (error) {
    console.log('❌ API Error:', error.message);
    
    if (error.status === 401) {
      console.log('\n🔍 This means the API key is invalid or not activated yet');
      console.log('💡 Try waiting 10-15 minutes for new keys to activate');
      console.log('🔄 Or generate a completely new API key');
    } else if (error.status === 429) {
      console.log('\n⏱️  Rate limit - try again in a moment');
    } else if (error.status === 402) {
      console.log('\n💳 Billing issue - check your OpenAI account');
    }
    
    return false;
  }
}

// Run the test
testExplicitKey().then(success => {
  if (success) {
    console.log('\n🎉 AMAZING! OpenAI integration is fully working!');
    console.log('🚀 LocalPost.ai now has complete AI-powered content generation!');
    console.log('✨ Users will get real AI-generated posts with weather awareness!');
  } else {
    console.log('\n⚠️  OpenAI integration still needs attention');
    console.log('🔧 Check your OpenAI account status and billing');
  }
}).catch(console.error);
