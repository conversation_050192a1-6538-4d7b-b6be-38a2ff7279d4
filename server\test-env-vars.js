require('dotenv').config();

console.log('🔍 Testing Environment Variables...\n');

console.log('1. Supabase Configuration:');
console.log('   SUPABASE_URL:', process.env.SUPABASE_URL || 'NOT SET');
console.log('   SUPABASE_ANON_KEY:', process.env.SUPABASE_ANON_KEY ? 'SET (length: ' + process.env.SUPABASE_ANON_KEY.length + ')' : 'NOT SET');
console.log('   SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'SET (length: ' + process.env.SUPABASE_SERVICE_ROLE_KEY.length + ')' : 'NOT SET');

console.log('\n2. Validation Checks:');
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

console.log('   URL exists:', !!supabaseUrl);
console.log('   URL starts with https:', supabaseUrl?.startsWith('https://'));
console.log('   URL does not contain "your_supabase":', !supabaseUrl?.includes('your_supabase'));
console.log('   Key exists:', !!supabaseKey);
console.log('   Key length > 20:', supabaseKey?.length > 20);
console.log('   Key does not contain "your_supabase":', !supabaseKey?.includes('your_supabase'));

const hasValidSupabaseConfig = supabaseUrl &&
  supabaseKey &&
  supabaseUrl.startsWith('https://') &&
  !supabaseUrl.includes('your_supabase') &&
  supabaseKey.length > 20 &&
  !supabaseKey.includes('your_supabase');

console.log('\n3. Final Validation Result:');
console.log('   hasValidSupabaseConfig:', hasValidSupabaseConfig);

if (hasValidSupabaseConfig) {
  console.log('✅ Supabase should be configured correctly!');
} else {
  console.log('❌ Supabase validation failed');
}

console.log('\n4. Testing Supabase Client Creation:');
try {
  const { createClient } = require('@supabase/supabase-js');
  const supabase = createClient(supabaseUrl, supabaseKey);
  console.log('✅ Supabase client created successfully');
  
  // Test a simple operation
  supabase.auth.getSession()
    .then(result => {
      console.log('✅ Supabase client is functional');
    })
    .catch(error => {
      console.log('❌ Supabase client test failed:', error.message);
    });
    
} catch (error) {
  console.log('❌ Failed to create Supabase client:', error.message);
}
