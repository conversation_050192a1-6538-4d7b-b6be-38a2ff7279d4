const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Check if we have valid Supabase credentials
const hasValidSupabaseConfig = supabaseUrl &&
  supabaseKey &&
  supabaseUrl.startsWith('https://') &&
  !supabaseUrl.includes('your_supabase') &&
  supabaseKey.length > 20 &&
  !supabaseKey.includes('your_supabase');

if (!hasValidSupabaseConfig) {
  console.warn('⚠️  Supabase not configured - running in DEMO MODE');
  console.warn('   To enable full functionality, set up Supabase credentials in server/.env');

  // Create mock clients for demo mode
  const mockClient = {
    auth: {
      signUp: () => Promise.resolve({ data: { user: null }, error: { message: 'Demo mode - Supabase not configured' } }),
      signInWithPassword: () => Promise.resolve({ data: { user: null }, error: { message: 'Demo mode - Supabase not configured' } }),
      signOut: () => Promise.resolve({ error: null }),
      getUser: () => Promise.resolve({ data: { user: null }, error: { message: 'Demo mode - Supabase not configured' } }),
      refreshSession: () => Promise.resolve({ data: { session: null }, error: { message: 'Demo mode - Supabase not configured' } })
    },
    from: () => ({
      select: () => ({ eq: () => ({ single: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }) }) }),
      insert: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }),
      update: () => ({ eq: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } }) }),
      delete: () => ({ eq: () => Promise.resolve({ error: { message: 'Demo mode - Supabase not configured' } }) }),
      upsert: () => Promise.resolve({ data: null, error: { message: 'Demo mode - Supabase not configured' } })
    })
  };

  module.exports = {
    supabase: mockClient,
    supabaseAdmin: mockClient,
    isDemoMode: true
  };
} else {
  // Client for user operations (with RLS)
  const supabase = createClient(supabaseUrl, supabaseKey);

  // Admin client for service operations (bypasses RLS)
  const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

  module.exports = {
    supabase,
    supabaseAdmin,
    isDemoMode: false
  };
}
