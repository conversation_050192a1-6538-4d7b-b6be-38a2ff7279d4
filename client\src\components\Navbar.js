import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import {
  Calendar,
  User,
  LogOut,
  CreditCard,
  BarChart3,
  TrendingUp,
  Users,
  HelpCircle,
  Sparkles,
  Settings,
  Plug
} from 'lucide-react';

const Navbar = ({ children }) => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/');
  };

  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: BarChart3, highlight: true },
    { path: '/calendar', label: 'Content Calendar', icon: Calendar },
    { path: '/generate', label: 'Generate Content', icon: Sparkles },
    { path: '/business-profile', label: 'Business Profile', icon: User },
    { path: '/connect-social-media', label: 'Connect Social Media', icon: Plug },
    { path: '/analytics', label: 'Analytics', icon: TrendingUp },
    { path: '/team', label: 'Team', icon: Users },
    { path: '/billing', label: 'Billing', icon: CreditCard },
    { path: '/profile', label: 'Account Settings', icon: Settings },
    { path: '/support', label: 'Help & Support', icon: HelpCircle },
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-sm border-r border-gray-200 flex flex-col">
        {/* Logo */}
        <div className="p-6 border-b border-gray-200">
          <h1 className="text-2xl font-bold text-primary-600">LocalPost.ai</h1>
        </div>

        {/* Navigation */}
        <nav className="flex-1 p-4 space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);

            return (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-3 px-4 py-3 rounded-lg text-sm font-medium transition-colors ${item.highlight && !active
                  ? 'bg-orange-500 text-white hover:bg-orange-600'
                  : active
                    ? 'bg-primary-50 text-primary-600'
                    : 'text-gray-700 hover:bg-gray-100'
                  }`}
              >
                <Icon size={20} />
                <span>{item.label}</span>
              </Link>
            );
          })}
        </nav>

        {/* User section */}
        <div className="p-4 border-t border-gray-200">
          <div className="mb-3">
            <p className="text-sm text-gray-600 truncate">
              {user?.businessName || user?.email}
            </p>
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors w-full"
          >
            <LogOut size={18} />
            <span>Logout</span>
          </button>
        </div>
      </div>

      {/* Main content area */}
      <div className="flex-1 overflow-auto">
        {children}
      </div>
    </div>
  );
};

export default Navbar;
