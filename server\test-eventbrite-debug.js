require('dotenv').config();
const axios = require('axios');

async function testEventbriteDebug() {
  console.log('🔍 Eventbrite API Debug Test\n');

  const apiKey = process.env.EVENTBRITE_API_KEY;
  const baseUrl = 'https://api.eventbrite.com/v3';
  
  try {
    // Test the exact response structure
    console.log('🧪 Testing API response structure...');
    
    const response = await axios.get(`${baseUrl}/events/search/`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      },
      params: {
        'location.address': 'New York',
        'page_size': 3
      }
    });

    console.log('✅ API call successful!');
    console.log('📊 Response status:', response.status);
    console.log('🔍 Response structure:');
    console.log('   Keys:', Object.keys(response.data));
    
    // Check if events exist
    if (response.data.events) {
      console.log(`   Events array length: ${response.data.events.length}`);
      
      if (response.data.events.length > 0) {
        console.log('\n📅 Sample Event:');
        const event = response.data.events[0];
        console.log('   Event keys:', Object.keys(event));
        console.log('   Name:', event.name?.text || 'N/A');
        console.log('   Start:', event.start?.local || 'N/A');
        console.log('   URL:', event.url || 'N/A');
      } else {
        console.log('   No events in response');
      }
    } else {
      console.log('   No events property in response');
      console.log('   Full response:', JSON.stringify(response.data, null, 2));
    }

    // Test pagination info
    if (response.data.pagination) {
      console.log('\n📄 Pagination info:');
      console.log('   Total count:', response.data.pagination.object_count);
      console.log('   Page count:', response.data.pagination.page_count);
    }

    // Try different search parameters
    console.log('\n🔍 Testing different search parameters...');
    
    const searches = [
      { 'q': 'food' },
      { 'location.address': 'Manhattan, NY' },
      { 'categories': '110' }, // Food & Drink category
      { 'start_date.range_start': new Date().toISOString() }
    ];

    for (const params of searches) {
      try {
        const searchResponse = await axios.get(`${baseUrl}/events/search/`, {
          headers: {
            'Authorization': `Bearer ${apiKey}`
          },
          params: {
            ...params,
            'page_size': 1
          }
        });
        
        const count = searchResponse.data.events?.length || 0;
        console.log(`   ${JSON.stringify(params)}: ${count} events`);
        
      } catch (searchError) {
        console.log(`   ${JSON.stringify(params)}: Error - ${searchError.response?.status}`);
      }
    }

    return true;

  } catch (error) {
    console.log('\n❌ API Error:', error.response?.status || 'Network Error');
    console.log('📝 Error message:', error.message);
    
    if (error.response?.data) {
      console.log('🔍 Error details:', JSON.stringify(error.response.data, null, 2));
    }
    
    return false;
  }
}

testEventbriteDebug().catch(console.error);
