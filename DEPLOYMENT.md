# LocalPost.ai Deployment Guide

## Prerequisites

Before deploying LocalPost.ai, ensure you have:

1. **Supabase Account** - For database and authentication
2. **OpenAI API Key** - For content generation
3. **Stripe Account** - For payment processing
4. **OpenWeatherMap API Key** - For weather data
5. **Eventbrite API Key** (Optional) - For local events
6. **Vercel Account** - For hosting (recommended)

## Environment Variables

### Server Environment Variables (.env)

```bash
# Server Configuration
PORT=5000
NODE_ENV=production

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_STARTER_PRICE_ID=price_starter_plan_id
STRIPE_PROFESSIONAL_PRICE_ID=price_professional_plan_id
STRIPE_AGENCY_PRICE_ID=price_agency_plan_id

# Weather API Configuration
OPENWEATHER_API_KEY=your_openweather_api_key

# Events API Configuration (Optional)
EVENTBRITE_API_KEY=your_eventbrite_api_key

# JWT Configuration
JWT_SECRET=your_jwt_secret_key

# CORS Configuration
CLIENT_URL=https://your-domain.com
```

### Client Environment Variables (.env)

```bash
# Supabase Configuration
REACT_APP_SUPABASE_URL=your_supabase_project_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key

# Stripe Configuration
REACT_APP_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
```

## Database Setup

### 1. Create Supabase Project

1. Go to [Supabase](https://supabase.com) and create a new project
2. Wait for the project to be fully initialized
3. Go to Settings > API to get your project URL and keys

### 2. Run Database Schema

1. Go to the SQL Editor in your Supabase dashboard
2. Copy and paste the contents of `database/schema.sql`
3. Run the SQL to create all tables and policies

### 3. Configure Row Level Security

The schema includes RLS policies, but verify they're enabled:

1. Go to Authentication > Policies
2. Ensure all tables have appropriate policies
3. Test with a sample user account

## API Keys Setup

### 1. OpenAI API Key

1. Go to [OpenAI Platform](https://platform.openai.com)
2. Create an API key with GPT-4 and DALL-E access
3. Set usage limits to control costs

### 2. Stripe Setup

1. Create a [Stripe](https://stripe.com) account
2. Create products and prices for your subscription plans:
   - Starter: $49/month
   - Professional: $99/month  
   - Agency: $199/month
3. Set up webhooks pointing to your domain `/api/webhooks/stripe`
4. Get your API keys from the Stripe dashboard

### 3. Weather API

1. Sign up at [OpenWeatherMap](https://openweathermap.org/api)
2. Get a free API key (1000 calls/day)
3. Upgrade if you need more calls

### 4. Events API (Optional)

1. Sign up at [Eventbrite](https://www.eventbrite.com/platform/api)
2. Create an app and get your API key
3. This is optional - the app will use mock data if not provided

## Deployment Options

### Option 1: Vercel (Recommended)

1. **Connect Repository**
   ```bash
   # Push your code to GitHub
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Go to [Vercel](https://vercel.com)
   - Import your GitHub repository
   - Vercel will automatically detect the configuration

3. **Set Environment Variables**
   - In Vercel dashboard, go to Settings > Environment Variables
   - Add all the environment variables listed above
   - Make sure to set them for Production environment

4. **Configure Domains**
   - Add your custom domain in Vercel settings
   - Update CORS settings in your environment variables

### Option 2: Manual Deployment

1. **Build the Application**
   ```bash
   # Install dependencies
   npm run install:all
   
   # Build client
   cd client && npm run build
   
   # The build folder contains the production build
   ```

2. **Deploy Server**
   - Deploy the server folder to your hosting provider
   - Ensure Node.js 18+ is available
   - Set all environment variables
   - Start with `npm start`

3. **Deploy Client**
   - Upload the client/build folder to your web server
   - Configure your web server to serve the React app
   - Set up redirects for client-side routing

## Post-Deployment Setup

### 1. Test Core Functionality

1. **User Registration**
   - Create a test account
   - Verify email confirmation works
   - Check database entries

2. **Content Generation**
   - Complete onboarding flow
   - Generate test content
   - Verify AI integration works

3. **Payment Processing**
   - Test subscription signup
   - Verify Stripe webhooks
   - Check subscription status updates

### 2. Configure Monitoring

1. **Error Tracking**
   - Set up error monitoring (Sentry recommended)
   - Configure alerts for critical errors

2. **Performance Monitoring**
   - Monitor API response times
   - Track database query performance
   - Set up uptime monitoring

3. **Usage Analytics**
   - Track user engagement
   - Monitor API usage and costs
   - Set up billing alerts

### 3. Security Checklist

1. **API Security**
   - Verify rate limiting is working
   - Test authentication flows
   - Check CORS configuration

2. **Database Security**
   - Verify RLS policies
   - Test user data isolation
   - Check backup configuration

3. **Environment Security**
   - Rotate API keys regularly
   - Use strong JWT secrets
   - Enable HTTPS everywhere

## Scaling Considerations

### Database Scaling

- Monitor Supabase usage and upgrade plan as needed
- Consider read replicas for high traffic
- Implement database connection pooling

### API Scaling

- Monitor OpenAI API usage and costs
- Implement caching for weather/events data
- Consider API rate limiting per user

### Infrastructure Scaling

- Vercel automatically scales serverless functions
- Monitor function execution times
- Consider CDN for static assets

## Maintenance

### Regular Tasks

1. **Weekly**
   - Review error logs
   - Check API usage and costs
   - Monitor user feedback

2. **Monthly**
   - Update dependencies
   - Review security alerts
   - Analyze user metrics

3. **Quarterly**
   - Rotate API keys
   - Review and update pricing
   - Performance optimization

### Backup Strategy

1. **Database Backups**
   - Supabase provides automatic backups
   - Consider additional backup strategy for critical data

2. **Code Backups**
   - Use Git for version control
   - Tag releases for easy rollback

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check CLIENT_URL environment variable
   - Verify domain configuration

2. **Database Connection Issues**
   - Check Supabase credentials
   - Verify RLS policies

3. **API Rate Limits**
   - Monitor OpenAI usage
   - Implement proper error handling

4. **Payment Issues**
   - Check Stripe webhook configuration
   - Verify webhook secret

### Support Resources

- Supabase Documentation: https://supabase.com/docs
- OpenAI API Documentation: https://platform.openai.com/docs
- Stripe Documentation: https://stripe.com/docs
- Vercel Documentation: https://vercel.com/docs

## Cost Optimization

### API Costs

- Monitor OpenAI usage closely
- Implement content caching
- Set usage limits per user

### Infrastructure Costs

- Vercel has generous free tier
- Supabase free tier suitable for early stage
- Monitor and optimize as you scale

### Monitoring Costs

- Set up billing alerts
- Track cost per user
- Optimize expensive operations
