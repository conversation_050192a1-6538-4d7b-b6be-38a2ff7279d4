require('dotenv').config();
const { supabase } = require('./config/database');

async function createDemoSimple() {
  console.log('👤 Creating demo user with simple approach\n');

  // Use a known UUID for consistency
  const userId = '550e8400-e29b-41d4-a716-************'; // Example UUID
  const demoEmail = '<EMAIL>';

  try {
    // Check if user already exists
    const { data: existingUser } = await supabase
      .from('users')
      .select('*')
      .eq('email', demoEmail)
      .single();

    if (existingUser) {
      console.log('✅ Demo user already exists!');
      console.log(`   👤 ID: ${existingUser.id}`);
      console.log(`   📧 Email: ${existingUser.email}`);
      console.log(`   🏢 Business: ${existingUser.business_name}`);
      return existingUser;
    }

    // Create user record
    console.log('1️⃣ Creating user record...');
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email: demoEmail,
        business_name: 'Demo Restaurant',
        business_type: 'restaurant',
        location: 'New York, NY',
        subscription_status: 'active',
        subscription_tier: 'starter',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (userError) {
      console.log('❌ User creation failed:', userError.message);
      return null;
    }

    console.log('✅ User created successfully!');

    // Create business profile
    console.log('\n2️⃣ Creating business profile...');
    
    const { data: businessData, error: businessError } = await supabase
      .from('business_profiles')
      .insert({
        user_id: userId,
        business_description: 'A cozy restaurant serving delicious food in the heart of New York City.',
        target_audience: 'Local food lovers, families, young professionals',
        brand_voice: 'friendly, welcoming, professional',
        primary_color: '#3b82f6',
        secondary_color: '#1e40af',
        visual_style: {
          tone: 'warm and inviting',
          imagery: 'food photography, restaurant ambiance',
          colors: ['#3b82f6', '#1e40af', '#f59e0b']
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (businessError) {
      console.log('⚠️  Business profile creation failed:', businessError.message);
    } else {
      console.log('✅ Business profile created successfully!');
    }

    // Create sample posts
    console.log('\n3️⃣ Creating sample posts...');
    
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const samplePosts = [
      {
        user_id: userId,
        platform: 'facebook',
        content_text: '✨ At Demo Restaurant, we\'re passionate about providing excellent restaurant services to our New York, NY community. Visit us today!',
        hashtags: ['#NewYorkNY', '#restaurant', '#LocalBusiness'],
        date_scheduled: today.toISOString().split('T')[0],
        status: 'generated',
        weather_context: 'sunny, 71°F',
        created_at: new Date().toISOString()
      },
      {
        user_id: userId,
        platform: 'instagram',
        content_text: '📍 New York, NY | Demo Restaurant Where quality meets excellence in restaurant services 🌟',
        hashtags: ['#NewYorkNY', '#restaurant', '#LocalBusiness'],
        date_scheduled: today.toISOString().split('T')[0],
        status: 'generated',
        weather_context: 'sunny, 71°F',
        created_at: new Date().toISOString()
      },
      {
        user_id: userId,
        platform: 'facebook',
        content_text: '📍 Located in beautiful New York, NY, Demo Restaurant brings you the best restaurant experience. Come see what makes us special!',
        hashtags: ['#NewYorkNY', '#restaurant', '#LocalBusiness'],
        date_scheduled: tomorrow.toISOString().split('T')[0],
        status: 'generated',
        weather_context: 'sunny, 71°F',
        created_at: new Date().toISOString()
      },
      {
        user_id: userId,
        platform: 'instagram',
        content_text: '🌟 New York, NY | Demo Restaurant Experience the difference at Demo Restaurant! 📍',
        hashtags: ['#NewYorkNY', '#restaurant', '#LocalBusiness'],
        date_scheduled: tomorrow.toISOString().split('T')[0],
        status: 'generated',
        weather_context: 'sunny, 71°F',
        created_at: new Date().toISOString()
      }
    ];

    const { data: postsData, error: postsError } = await supabase
      .from('generated_posts')
      .insert(samplePosts)
      .select();

    if (postsError) {
      console.log('⚠️  Sample posts creation failed:', postsError.message);
    } else {
      console.log(`✅ Created ${postsData.length} sample posts`);
    }

    console.log('\n🎉 Demo user setup complete!');
    console.log('📋 Demo User Details:');
    console.log(`   👤 ID: ${userId}`);
    console.log(`   📧 Email: ${demoEmail}`);
    console.log(`   🏢 Business: Demo Restaurant`);
    console.log(`   📍 Location: New York, NY`);
    console.log(`   💳 Subscription: Active (Starter)`);

    console.log('\n🔧 Next Steps:');
    console.log('1. Create this user in Supabase Auth dashboard:');
    console.log(`   - Email: ${demoEmail}`);
    console.log('   - Password: demo123');
    console.log(`   - User ID: ${userId}`);
    console.log('2. Or modify auth to work with database users');

    return userData;

  } catch (error) {
    console.log('❌ Demo user creation failed:', error.message);
    return null;
  }
}

// Run creation
createDemoSimple().catch(console.error);
