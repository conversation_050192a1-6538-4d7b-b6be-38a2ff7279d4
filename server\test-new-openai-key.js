// Clear any cached environment variables
delete require.cache[require.resolve('dotenv')];
require('dotenv').config();
const OpenAI = require('openai');

async function testNewOpenAIKey() {
  console.log('🔑 Testing New OpenAI API Key\n');

  const apiKey = process.env.OPENAI_API_KEY;
  
  console.log('📋 API Key Check:');
  console.log(`   Length: ${apiKey ? apiKey.length : 0} characters`);
  console.log(`   Starts with: ${apiKey ? apiKey.substring(0, 8) : 'N/A'}`);
  console.log(`   Ends with: ${apiKey ? apiKey.substring(apiKey.length - 8) : 'N/A'}`);
  console.log(`   Format: ${apiKey && apiKey.startsWith('sk-') ? '✅ Correct' : '❌ Invalid'}`);

  if (!apiKey || !apiKey.startsWith('sk-')) {
    console.log('\n❌ Invalid API key format');
    return;
  }

  console.log('\n🧪 Testing API Connection...');
  
  try {
    const openai = new OpenAI({ apiKey });
    
    // Try the simplest possible API call
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Say 'Hello World'"
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    console.log('🎉 SUCCESS! OpenAI API is working!');
    console.log('📝 Response:', completion.choices[0].message.content);
    console.log('🎯 Model used:', completion.model);
    console.log('💰 Tokens used:', completion.usage.total_tokens);
    
    // Test a real content generation
    console.log('\n🎨 Testing Real Content Generation...');
    
    const contentCompletion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert social media content creator for local businesses."
        },
        {
          role: "user",
          content: `Create a Facebook post for a restaurant called "Demo Restaurant" in New York, NY. 
                   The weather is sunny and 71°F. Make it engaging and include relevant hashtags.
                   Return only JSON with this format: {"text": "post content", "hashtags": ["tag1", "tag2"], "imagePrompt": "description for image"}`
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    });

    const aiContent = contentCompletion.choices[0].message.content;
    console.log('✅ Real content generation successful!');
    console.log('📝 AI Generated Content:', aiContent.substring(0, 200) + '...');
    
    try {
      const parsedContent = JSON.parse(aiContent);
      console.log('\n🎯 Parsed Content:');
      console.log('📄 Text:', parsedContent.text?.substring(0, 100) + '...');
      console.log('🏷️  Hashtags:', parsedContent.hashtags?.join(', '));
      console.log('🖼️  Image Prompt:', parsedContent.imagePrompt?.substring(0, 50) + '...');
    } catch (parseError) {
      console.log('⚠️  JSON parsing needs adjustment, but content generation works!');
    }
    
    return true;

  } catch (error) {
    console.log('❌ API Error:', error.message);
    
    if (error.status === 401) {
      console.log('\n🔍 Troubleshooting 401 Error:');
      console.log('1. Check if the API key is correct');
      console.log('2. Verify billing is set up on OpenAI account');
      console.log('3. Make sure the key has proper permissions');
      console.log('4. Try generating a new API key');
      console.log('5. Wait a few minutes for new keys to activate');
    } else if (error.status === 429) {
      console.log('\n⏱️  Rate limit - try again in a moment');
    } else if (error.status === 402) {
      console.log('\n💳 Billing issue - check your OpenAI account');
    }
    
    return false;
  }
}

// Run the test
testNewOpenAIKey().then(success => {
  if (success) {
    console.log('\n🎉 OpenAI integration is ready for LocalPost.ai!');
    console.log('🚀 Your system now has full AI-powered content generation!');
  } else {
    console.log('\n⚠️  OpenAI integration needs attention');
  }
}).catch(console.error);
