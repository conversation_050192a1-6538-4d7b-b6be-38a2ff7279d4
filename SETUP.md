# LocalPost.ai Development Setup

## Quick Start

Follow these steps to get LocalPost.ai running locally:

### 1. <PERSON><PERSON> and <PERSON>stall

```bash
# Clone the repository
git clone <repository-url>
cd LocalPost

# Install all dependencies
npm run install:all
```

### 2. Environment Setup

```bash
# Copy environment files
cp server/.env.example server/.env
cp client/.env.example client/.env
```

### 3. Configure Environment Variables

Edit `server/.env` with your API keys:

```bash
# Required for basic functionality
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
OPENAI_API_KEY=your_openai_api_key

# Optional (will use mock data if not provided)
OPENWEATHER_API_KEY=your_openweather_api_key
EVENTBRITE_API_KEY=your_eventbrite_api_key
STRIPE_SECRET_KEY=your_stripe_secret_key
```

Edit `client/.env`:

```bash
REACT_APP_SUPABASE_URL=your_supabase_url
REACT_APP_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Database Setup

1. Create a Supabase project at https://supabase.com
2. Go to SQL Editor and run the schema from `database/schema.sql`
3. Verify tables are created and RLS policies are enabled

### 5. Start Development

```bash
# Start both client and server
npm run dev

# Or start individually
npm run server:dev  # Server on http://localhost:5000
npm run client:dev  # Client on http://localhost:3000
```

## Detailed Setup Guide

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Git

### API Keys Required

#### Essential (Required for core functionality)

1. **Supabase** (Free)
   - Go to https://supabase.com
   - Create new project
   - Get URL and anon key from Settings > API

2. **OpenAI** (Paid - ~$5-10/month for development)
   - Go to https://platform.openai.com
   - Create API key
   - Ensure GPT-4 and DALL-E access

#### Optional (Will use mock data if not provided)

3. **OpenWeatherMap** (Free tier available)
   - Go to https://openweathermap.org/api
   - Sign up for free API key
   - 1000 calls/day free

4. **Eventbrite** (Free)
   - Go to https://www.eventbrite.com/platform/api
   - Create app and get API key

5. **Stripe** (Free for testing)
   - Go to https://stripe.com
   - Get test API keys
   - Set up webhook endpoint

### Project Structure

```
LocalPost/
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── contexts/       # React contexts
│   │   ├── pages/          # Page components
│   │   └── utils/          # Utility functions
│   ├── public/             # Static assets
│   └── package.json
├── server/                 # Node.js backend
│   ├── config/             # Configuration files
│   ├── middleware/         # Express middleware
│   ├── routes/             # API routes
│   ├── services/           # Business logic
│   ├── utils/              # Utility functions
│   └── package.json
├── database/               # Database schema
└── package.json           # Root package.json
```

### Development Workflow

#### 1. Feature Development

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and test
npm run dev

# Commit changes
git add .
git commit -m "Add your feature"

# Push and create PR
git push origin feature/your-feature-name
```

#### 2. Testing

```bash
# Run tests (when implemented)
npm test

# Test specific components
cd client && npm test
cd server && npm test
```

#### 3. Code Quality

```bash
# Format code (if prettier is configured)
npm run format

# Lint code (if eslint is configured)
npm run lint
```

### Common Development Tasks

#### Adding New API Endpoints

1. Create route file in `server/routes/`
2. Add validation schema in `server/utils/validation.js`
3. Implement business logic in `server/services/`
4. Add route to `server/index.js`
5. Test with Postman or similar tool

#### Adding New React Components

1. Create component in `client/src/components/`
2. Add to appropriate page in `client/src/pages/`
3. Update routing if needed in `client/src/App.js`
4. Test in browser

#### Database Changes

1. Update schema in `database/schema.sql`
2. Run new schema in Supabase SQL Editor
3. Update API endpoints to use new fields
4. Update frontend to display new data

### Troubleshooting

#### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 3000 or 5000
   npx kill-port 3000
   npx kill-port 5000
   ```

2. **Module not found errors**
   ```bash
   # Clear node_modules and reinstall
   rm -rf node_modules client/node_modules server/node_modules
   npm run install:all
   ```

3. **Database connection issues**
   - Check Supabase URL and keys
   - Verify project is not paused
   - Check RLS policies

4. **CORS errors**
   - Verify CLIENT_URL in server/.env
   - Check proxy setting in client/package.json

#### Debug Mode

```bash
# Run server with debug logging
DEBUG=* npm run server:dev

# Run with specific debug namespace
DEBUG=app:* npm run server:dev
```

### Development Tips

#### 1. Hot Reloading

Both client and server support hot reloading:
- Client: React hot reloading via Create React App
- Server: Nodemon restarts on file changes

#### 2. API Testing

Use tools like:
- Postman for API testing
- Browser DevTools for frontend debugging
- Supabase dashboard for database inspection

#### 3. Mock Data

The app includes mock data for development:
- Weather service returns mock data without API key
- Events service returns sample events
- Payment flows work with Stripe test mode

#### 4. Environment Switching

```bash
# Development
NODE_ENV=development npm run server:dev

# Production simulation
NODE_ENV=production npm run server:start
```

### Performance Optimization

#### Development

- Use React DevTools for component profiling
- Monitor API response times
- Check database query performance in Supabase

#### Production Preparation

- Build and test production bundle
- Optimize images and assets
- Test with production API keys

### Security Considerations

#### Development

- Never commit API keys to Git
- Use environment variables for all secrets
- Test authentication flows thoroughly

#### Production Preparation

- Rotate all API keys
- Enable HTTPS
- Configure proper CORS settings
- Test rate limiting

### Getting Help

#### Resources

- React Documentation: https://reactjs.org/docs
- Node.js Documentation: https://nodejs.org/docs
- Supabase Documentation: https://supabase.com/docs
- OpenAI API Documentation: https://platform.openai.com/docs

#### Community

- Create GitHub issues for bugs
- Check existing issues for solutions
- Join relevant Discord/Slack communities

### Next Steps

Once you have the basic setup working:

1. Explore the codebase structure
2. Try generating content with different business types
3. Test the onboarding flow
4. Experiment with different AI prompts
5. Customize the UI/UX to your needs

Happy coding! 🚀
