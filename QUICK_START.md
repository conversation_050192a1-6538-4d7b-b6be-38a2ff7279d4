# 🚀 LocalPost.ai - Quick Start Guide

## ✅ Dependencies Installed Successfully!

All npm packages have been installed. Now let's get LocalPost.ai running on localhost.

## 🔑 Required API Keys

To run LocalPost.ai, you need at least these two API keys:

### 1. Supabase (Free - Required)
1. Go to https://supabase.com
2. Create a new project
3. Wait for it to initialize (2-3 minutes)
4. Go to Settings > API
5. Copy your Project URL and anon public key

### 2. OpenAI (Paid - Required for AI features)
1. Go to https://platform.openai.com
2. Create an API key
3. Add $5-10 credit to your account
4. Copy your API key

## 📝 Setup Environment Variables

Edit these files with your API keys:

### `server/.env`
```bash
# Replace these with your actual values:
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
OPENAI_API_KEY=sk-your_openai_key_here

# These are optional (app will use mock data):
OPENWEATHER_API_KEY=your_weather_key_here
EVENTBRITE_API_KEY=your_eventbrite_key_here
```

### `client/.env`
```bash
# Same Supabase values as server:
REACT_APP_SUPABASE_URL=https://your-project.supabase.co
REACT_APP_SUPABASE_ANON_KEY=your_anon_key_here
```

## 🗄️ Database Setup

1. In your Supabase dashboard, go to SQL Editor
2. Copy the contents of `database/schema.sql`
3. Paste and run it to create all tables

## 🏃‍♂️ Run the Application

Open two terminals in the LocalPost folder:

### Terminal 1 - Start Server
```bash
cd server
npm run dev
```

### Terminal 2 - Start Client
```bash
cd client
npm start
```

The app will open at http://localhost:3000

## 🎯 What You Can Test

### Without API Keys (Mock Mode)
- ✅ User interface and navigation
- ✅ Registration/login flow (with Supabase)
- ✅ Profile management
- ✅ Content calendar interface
- ✅ Mock weather and events data

### With API Keys (Full Features)
- ✅ AI content generation
- ✅ Real weather data integration
- ✅ Brand voice analysis
- ✅ Image generation
- ✅ Local events integration

## 🔧 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill processes on ports 3000 or 5000
   npx kill-port 3000
   npx kill-port 5000
   ```

2. **Module not found errors**
   ```bash
   # Reinstall dependencies
   npm run install:all
   ```

3. **Database connection issues**
   - Check your Supabase URL and keys
   - Make sure your Supabase project isn't paused
   - Verify the database schema was created

4. **CORS errors**
   - Make sure CLIENT_URL=http://localhost:3000 in server/.env

## 📱 Test the App

1. **Register a new account**
2. **Complete the onboarding flow**
3. **Try generating content** (needs OpenAI key)
4. **Explore the content calendar**
5. **Edit and manage posts**

## 🎉 Success!

If everything is working, you should see:
- ✅ React app running on http://localhost:3000
- ✅ Server running on http://localhost:5000
- ✅ Database connected to Supabase
- ✅ AI features working (with OpenAI key)

## 🆘 Need Help?

If you run into issues:
1. Check the console for error messages
2. Verify your API keys are correct
3. Make sure all environment variables are set
4. Check that the database schema was created properly

## 🚀 Next Steps

Once you have it running locally:
1. Test all features thoroughly
2. Customize the UI/branding
3. Add your own business logic
4. Deploy to production (see DEPLOYMENT.md)

Happy coding! 🎯
