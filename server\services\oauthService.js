const axios = require('axios');
const crypto = require('crypto');
const { v4: uuidv4 } = require('uuid');

class OAuthService {
  constructor() {
    this.sessions = new Map(); // In production, use Redis or database
  }

  // Generate secure state parameter for OAuth
  generateState(userId) {
    const state = uuidv4();
    this.sessions.set(state, {
      userId,
      timestamp: Date.now(),
      expires: Date.now() + (10 * 60 * 1000) // 10 minutes
    });
    return state;
  }

  // Validate state parameter
  validateState(state, userId) {
    const session = this.sessions.get(state);
    if (!session) return false;
    if (session.expires < Date.now()) {
      this.sessions.delete(state);
      return false;
    }
    if (session.userId !== userId) return false;
    
    this.sessions.delete(state); // One-time use
    return true;
  }

  // Generate Facebook OAuth URL
  getFacebookAuthUrl(userId) {
    const state = this.generateState(userId);
    const params = new URLSearchParams({
      client_id: process.env.FACEBOOK_APP_ID,
      redirect_uri: process.env.FACEBOOK_REDIRECT_URI,
      scope: 'pages_read_engagement,pages_show_list,instagram_basic',
      response_type: 'code',
      state: state
    });

    return `https://www.facebook.com/v18.0/dialog/oauth?${params.toString()}`;
  }

  // Generate Instagram OAuth URL (uses Facebook OAuth)
  getInstagramAuthUrl(userId) {
    const state = this.generateState(userId);
    const params = new URLSearchParams({
      client_id: process.env.INSTAGRAM_APP_ID,
      redirect_uri: process.env.INSTAGRAM_REDIRECT_URI,
      scope: 'user_profile,user_media',
      response_type: 'code',
      state: state
    });

    return `https://api.instagram.com/oauth/authorize?${params.toString()}`;
  }

  // Exchange Facebook authorization code for access token
  async exchangeFacebookCode(code, state, userId) {
    try {
      // Validate state
      if (!this.validateState(state, userId)) {
        throw new Error('Invalid or expired state parameter');
      }

      // Exchange code for access token
      const tokenResponse = await axios.post('https://graph.facebook.com/v18.0/oauth/access_token', {
        client_id: process.env.FACEBOOK_APP_ID,
        client_secret: process.env.FACEBOOK_APP_SECRET,
        redirect_uri: process.env.FACEBOOK_REDIRECT_URI,
        code: code
      });

      const { access_token, expires_in } = tokenResponse.data;

      // Get user's Facebook pages
      const pagesResponse = await axios.get(`https://graph.facebook.com/v18.0/me/accounts`, {
        params: {
          access_token: access_token,
          fields: 'id,name,access_token,category'
        }
      });

      // Get user info
      const userResponse = await axios.get(`https://graph.facebook.com/v18.0/me`, {
        params: {
          access_token: access_token,
          fields: 'id,name,email'
        }
      });

      return {
        platform: 'facebook',
        accessToken: access_token,
        expiresIn: expires_in,
        expiresAt: new Date(Date.now() + (expires_in * 1000)),
        user: userResponse.data,
        pages: pagesResponse.data.data || [],
        connectedAt: new Date()
      };

    } catch (error) {
      console.error('Facebook OAuth error:', error.response?.data || error.message);
      throw new Error('Failed to connect Facebook account');
    }
  }

  // Exchange Instagram authorization code for access token
  async exchangeInstagramCode(code, state, userId) {
    try {
      // Validate state
      if (!this.validateState(state, userId)) {
        throw new Error('Invalid or expired state parameter');
      }

      // Exchange code for access token
      const tokenResponse = await axios.post('https://api.instagram.com/oauth/access_token', {
        client_id: process.env.INSTAGRAM_APP_ID,
        client_secret: process.env.INSTAGRAM_APP_SECRET,
        grant_type: 'authorization_code',
        redirect_uri: process.env.INSTAGRAM_REDIRECT_URI,
        code: code
      });

      const { access_token, user_id } = tokenResponse.data;

      // Get user info
      const userResponse = await axios.get(`https://graph.instagram.com/me`, {
        params: {
          access_token: access_token,
          fields: 'id,username,account_type,media_count'
        }
      });

      return {
        platform: 'instagram',
        accessToken: access_token,
        userId: user_id,
        expiresAt: new Date(Date.now() + (60 * 24 * 60 * 60 * 1000)), // 60 days
        user: userResponse.data,
        connectedAt: new Date()
      };

    } catch (error) {
      console.error('Instagram OAuth error:', error.response?.data || error.message);
      throw new Error('Failed to connect Instagram account');
    }
  }

  // Get Facebook posts using stored token
  async getFacebookPosts(accessToken, pageId, limit = 25) {
    try {
      const response = await axios.get(`https://graph.facebook.com/v18.0/${pageId}/posts`, {
        params: {
          access_token: accessToken,
          fields: 'id,message,created_time,likes.summary(true),comments.summary(true),shares,full_picture,permalink_url',
          limit: limit
        }
      });

      return response.data.data.map(post => ({
        id: post.id,
        platform: 'Facebook',
        content: post.message || 'Facebook post (image/video content)',
        fullContent: post.message || 'Facebook post (image/video content)',
        date: post.created_time.split('T')[0],
        engagement: {
          likes: post.likes?.summary?.total_count || 0,
          comments: post.comments?.summary?.total_count || 0,
          shares: post.shares?.count || 0
        },
        media: {
          url: post.full_picture || null,
          thumbnail: post.full_picture || null,
          type: post.full_picture ? 'photo' : 'text',
          hasMedia: !!post.full_picture
        },
        sourceUrl: post.permalink_url,
        scraped: true,
        scrapedWith: 'Facebook Graph API',
        performance: this.calculatePerformance(post.likes?.summary?.total_count || 0, post.comments?.summary?.total_count || 0)
      }));

    } catch (error) {
      console.error('Facebook API error:', error.response?.data || error.message);
      throw new Error('Failed to fetch Facebook posts');
    }
  }

  // Get Instagram posts using stored token
  async getInstagramPosts(accessToken, limit = 25) {
    try {
      const response = await axios.get(`https://graph.instagram.com/me/media`, {
        params: {
          access_token: accessToken,
          fields: 'id,caption,media_type,media_url,thumbnail_url,permalink,timestamp,like_count,comments_count',
          limit: limit
        }
      });

      return response.data.data.map(post => ({
        id: post.id,
        platform: 'Instagram',
        content: post.caption || 'Instagram post (image/video content)',
        fullContent: post.caption || 'Instagram post (image/video content)',
        date: post.timestamp.split('T')[0],
        engagement: {
          likes: post.like_count || 0,
          comments: post.comments_count || 0,
          shares: 0
        },
        media: {
          url: post.media_url || null,
          thumbnail: post.thumbnail_url || post.media_url || null,
          type: post.media_type?.toLowerCase() || 'photo',
          hasMedia: !!post.media_url
        },
        sourceUrl: post.permalink,
        scraped: true,
        scrapedWith: 'Instagram Graph API',
        performance: this.calculatePerformance(post.like_count || 0, post.comments_count || 0)
      }));

    } catch (error) {
      console.error('Instagram API error:', error.response?.data || error.message);
      throw new Error('Failed to fetch Instagram posts');
    }
  }

  // Calculate post performance
  calculatePerformance(likes, comments) {
    const totalEngagement = likes + (comments * 3); // Comments weighted more
    if (totalEngagement > 100) return 'high';
    if (totalEngagement > 20) return 'medium';
    return 'low';
  }

  // Refresh Facebook token (if needed)
  async refreshFacebookToken(accessToken) {
    try {
      const response = await axios.get('https://graph.facebook.com/v18.0/oauth/access_token', {
        params: {
          grant_type: 'fb_exchange_token',
          client_id: process.env.FACEBOOK_APP_ID,
          client_secret: process.env.FACEBOOK_APP_SECRET,
          fb_exchange_token: accessToken
        }
      });

      return {
        accessToken: response.data.access_token,
        expiresIn: response.data.expires_in,
        expiresAt: new Date(Date.now() + (response.data.expires_in * 1000))
      };

    } catch (error) {
      console.error('Token refresh error:', error.response?.data || error.message);
      throw new Error('Failed to refresh access token');
    }
  }
}

module.exports = new OAuthService();
