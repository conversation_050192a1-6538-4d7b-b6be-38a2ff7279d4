require('dotenv').config();
const OpenAI = require('openai');

async function testOpenAISimple() {
  console.log('🔍 Simple OpenAI API Test\n');

  const apiKey = process.env.OPENAI_API_KEY;
  
  console.log('🔑 API Key Check:');
  console.log(`   Length: ${apiKey ? apiKey.length : 0} characters`);
  console.log(`   Starts with: ${apiKey ? apiKey.substring(0, 8) : 'N/A'}`);
  console.log(`   Ends with: ${apiKey ? apiKey.substring(apiKey.length - 8) : 'N/A'}`);
  console.log(`   Format: ${apiKey && apiKey.startsWith('sk-') ? '✅ Correct' : '❌ Invalid'}`);

  if (!apiKey || !apiKey.startsWith('sk-')) {
    console.log('\n❌ Invalid API key format');
    return;
  }

  console.log('\n🧪 Testing API Connection...');
  
  try {
    const openai = new OpenAI({ apiKey });
    
    // Try the simplest possible API call
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Say 'Hello World'"
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    console.log('✅ SUCCESS! OpenAI API is working!');
    console.log('📝 Response:', completion.choices[0].message.content);
    console.log('🎯 Model used:', completion.model);
    console.log('💰 Tokens used:', completion.usage.total_tokens);
    
    return true;

  } catch (error) {
    console.log('❌ API Error:', error.message);
    
    if (error.status === 401) {
      console.log('\n🔍 Troubleshooting 401 Error:');
      console.log('1. Check if the API key is correct');
      console.log('2. Verify billing is set up on OpenAI account');
      console.log('3. Make sure the key has proper permissions');
      console.log('4. Try generating a new API key');
      console.log('5. Wait a few minutes for new keys to activate');
    } else if (error.status === 429) {
      console.log('\n⏱️  Rate limit - try again in a moment');
    } else if (error.status === 402) {
      console.log('\n💳 Billing issue - check your OpenAI account');
    }
    
    return false;
  }
}

// Run the test
testOpenAISimple().then(success => {
  if (success) {
    console.log('\n🎉 OpenAI integration is ready for LocalPost.ai!');
  } else {
    console.log('\n⚠️  OpenAI integration needs attention');
  }
}).catch(console.error);
