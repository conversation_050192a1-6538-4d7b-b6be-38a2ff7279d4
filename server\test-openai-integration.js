require('dotenv').config();

console.log('🔍 Debugging OpenAI Integration...\n');

// Test the validation logic
console.log('1. Environment Variable Check:');
console.log('   OPENAI_API_KEY exists:', !!process.env.OPENAI_API_KEY);
console.log('   Key starts with sk-:', process.env.OPENAI_API_KEY?.startsWith('sk-'));
console.log('   Key contains "your_openai":', process.env.OPENAI_API_KEY?.includes('your_openai'));

const hasValidOpenAIKey = process.env.OPENAI_API_KEY &&
  process.env.OPENAI_API_KEY.startsWith('sk-') &&
  !process.env.OPENAI_API_KEY.includes('your_openai');

console.log('   hasValidOpenAIKey result:', hasValidOpenAIKey);

// Test the OpenAI service
console.log('\n2. Testing OpenAI Service:');
try {
  const openaiService = require('./services/openai');
  console.log('✅ OpenAI service loaded successfully');
  
  // Test content generation with mock context
  const testContext = {
    businessName: 'Test Coffee Shop',
    businessType: 'Coffee Shop',
    location: 'Downtown',
    tone: 'friendly',
    contentType: 'social_post'
  };
  
  console.log('\n3. Testing Content Generation:');
  console.log('   Using test context:', testContext);
  
  openaiService.generateContent(testContext)
    .then(result => {
      console.log('✅ Content generation successful!');
      console.log('   Generated content:', result);
    })
    .catch(error => {
      console.log('❌ Content generation failed:', error.message);
    });
    
} catch (error) {
  console.log('❌ Failed to load OpenAI service:', error.message);
}

// Test direct OpenAI initialization
console.log('\n4. Testing Direct OpenAI Initialization:');
try {
  const OpenAI = require('openai');
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });
  console.log('✅ OpenAI client initialized successfully');
  
  // Test a simple API call
  openai.chat.completions.create({
    model: "gpt-3.5-turbo",
    messages: [{ role: "user", content: "Say hello" }],
    max_tokens: 10
  })
  .then(response => {
    console.log('✅ Direct OpenAI API call successful!');
    console.log('   Response:', response.choices[0].message.content);
  })
  .catch(error => {
    console.log('❌ Direct OpenAI API call failed:', error.message);
  });
  
} catch (error) {
  console.log('❌ Failed to initialize OpenAI client:', error.message);
}
