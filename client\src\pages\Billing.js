import React from 'react';
import { CreditCard, Download, Calendar, CheckCircle } from 'lucide-react';

const Billing = () => {
  const currentPlan = {
    name: 'Professional',
    price: '$99',
    period: 'month',
    nextBilling: 'February 15, 2024'
  };

  const invoices = [
    {
      date: 'Jan 15, 2024',
      amount: '$99.00',
      status: 'Paid',
      invoice: 'INV-001'
    },
    {
      date: 'Dec 15, 2023',
      amount: '$99.00',
      status: 'Paid',
      invoice: 'INV-002'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Billing</h1>
        <p className="text-gray-600">Manage your subscription and billing information</p>
      </div>

      {/* Current Plan */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Current Plan</h2>
        </div>
        
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-900">{currentPlan.name}</h3>
              <p className="text-gray-600">
                {currentPlan.price}/{currentPlan.period}
              </p>
              <p className="text-sm text-gray-500 mt-2 flex items-center space-x-1">
                <Calendar size={14} />
                <span>Next billing: {currentPlan.nextBilling}</span>
              </p>
            </div>
            
            <div className="text-right">
              <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg mb-2">
                Change Plan
              </button>
              <br />
              <button className="text-red-600 hover:text-red-700 text-sm">
                Cancel Subscription
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Method */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Payment Method</h2>
        </div>
        
        <div className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 rounded-lg">
                <CreditCard size={20} className="text-gray-600" />
              </div>
              <div>
                <p className="font-medium text-gray-900">•••• •••• •••• 4242</p>
                <p className="text-sm text-gray-500">Expires 12/25</p>
              </div>
            </div>
            
            <button className="text-primary-600 hover:text-primary-700 text-sm font-medium">
              Update
            </button>
          </div>
        </div>
      </div>

      {/* Billing History */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Billing History</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {invoices.map((invoice, index) => (
            <div key={index} className="p-6 flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle size={16} className="text-green-500" />
                  <span className="text-sm font-medium text-gray-900">{invoice.date}</span>
                </div>
                <span className="text-sm text-gray-500">{invoice.invoice}</span>
              </div>
              
              <div className="flex items-center space-x-4">
                <span className="text-sm font-medium text-gray-900">{invoice.amount}</span>
                <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                  {invoice.status}
                </span>
                <button className="text-primary-600 hover:text-primary-700 p-1">
                  <Download size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Billing;
