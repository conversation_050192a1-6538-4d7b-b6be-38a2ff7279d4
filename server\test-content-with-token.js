const axios = require('axios');

async function testContentWithToken() {
  console.log('🧪 Testing Content Generation with Test Token\n');

  const baseUrl = 'http://localhost:5000';
  
  try {
    // Test content generation with test token
    console.log('1️⃣ Testing content generation with test token...');
    
    const generateData = {
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      platforms: ['facebook', 'instagram'],
      regenerate: true
    };

    console.log('📤 Request data:', JSON.stringify(generateData, null, 2));

    const response = await axios.post(`${baseUrl}/api/content/generate`, generateData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token' // Using test token
      },
      timeout: 60000 // 60 second timeout for AI generation
    });

    console.log('✅ Content generation successful!');
    console.log('📊 Response status:', response.status);
    
    if (response.data.generatedPosts) {
      console.log(`\n🎉 Generated ${response.data.generatedPosts.length} posts!`);
      
      response.data.generatedPosts.forEach((post, index) => {
        console.log(`\n📱 Post ${index + 1} (${post.platform}):`);
        console.log(`   📄 Content: ${post.content_text?.substring(0, 150)}...`);
        console.log(`   🏷️  Hashtags: ${post.hashtags?.join(', ') || 'None'}`);
        console.log(`   📅 Date: ${post.date_scheduled}`);
        console.log(`   🌤️  Weather: ${post.weather_context || 'None'}`);
        console.log(`   🖼️  Image: ${post.image_url ? 'Generated' : 'None'}`);
      });
      
      // Check if we got real AI content
      const firstPost = response.data.generatedPosts[0];
      if (firstPost && firstPost.content_text) {
        const isAIGenerated = firstPost.content_text.length > 100 && 
                             !firstPost.content_text.includes('Demo Restaurant, we\'re passionate');
        
        console.log(`\n🤖 AI Content Status: ${isAIGenerated ? '✅ Real AI Generated!' : '⚠️  Using Mock Data'}`);
        
        if (isAIGenerated) {
          console.log('🎉 SUCCESS! OpenAI integration is working in the app!');
        } else {
          console.log('💡 Content generation is working but using fallback data');
        }
      }
    }

    if (response.data.errors && response.data.errors.length > 0) {
      console.log('\n⚠️  Generation Errors:');
      response.data.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    return true;

  } catch (error) {
    console.log('\n❌ Content generation failed!');
    console.log('📊 Status:', error.response?.status || 'Network Error');
    console.log('📝 Error message:', error.message);
    
    if (error.response?.data) {
      console.log('🔍 Error details:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔧 Server is not running on port 5000');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏱️  Request timed out - AI generation can take time');
    }
    
    return false;
  }
}

// Test getting posts
async function testGetPosts() {
  console.log('\n2️⃣ Testing get posts endpoint...');
  
  try {
    const response = await axios.get('http://localhost:5000/api/content/posts', {
      headers: {
        'Authorization': 'Bearer test-token'
      }
    });

    console.log('✅ Get posts successful!');
    console.log(`📊 Found ${response.data.posts?.length || 0} existing posts`);
    
    if (response.data.posts?.length > 0) {
      console.log('\n📱 Existing Posts:');
      response.data.posts.slice(0, 3).forEach((post, index) => {
        console.log(`   ${index + 1}. ${post.platform} - ${post.content_text?.substring(0, 50)}...`);
      });
    }
    
  } catch (error) {
    console.log('❌ Get posts failed:', error.response?.data?.error || error.message);
  }
}

// Run tests
async function runTests() {
  const success = await testContentWithToken();
  await testGetPosts();
  
  console.log('\n🎯 Test Summary:');
  if (success) {
    console.log('✅ Content generation API is working!');
    console.log('🎉 The "Failed to generate content" issue should be resolved!');
    console.log('\n🔧 Next Steps:');
    console.log('1. The test token bypass is working');
    console.log('2. You can now test content generation in the UI');
    console.log('3. For production, set up proper Supabase Auth user');
  } else {
    console.log('❌ Content generation still has issues');
    console.log('🔍 Check server logs for more details');
  }
}

runTests().catch(console.error);
