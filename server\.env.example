# Server Configuration
PORT=5000
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Weather API Configuration
OPENWEATHER_API_KEY=your_openweather_api_key

# Events API Configuration
EVENTBRITE_API_KEY=your_eventbrite_api_key

# Apify API Configuration (Web Scraping)
APIFY_API_TOKEN=your_apify_api_token

# Social Media OAuth Configuration (Official APIs)
# Facebook OAuth (LocalPost.ai app configuration - set up once)
FACEBOOK_APP_ID=941480140282145
FACEBOOK_APP_SECRET=********************************
FACEBOOK_REDIRECT_URI=http://localhost:5000/auth/facebook/callback

# Instagram OAuth (uses same Facebook app - Instagram owned by Meta)
INSTAGRAM_APP_ID=941480140282145
INSTAGRAM_APP_SECRET=********************************
INSTAGRAM_REDIRECT_URI=http://localhost:5000/auth/instagram/callback

# OAuth Session Configuration
OAUTH_SESSION_SECRET=localpost_oauth_secret_2024_secure_random_key

# Twitter API v2
TWITTER_BEARER_TOKEN=your_twitter_bearer_token
TWITTER_API_KEY=your_twitter_api_key
TWITTER_API_SECRET=your_twitter_api_secret

# LinkedIn API
LINKEDIN_CLIENT_ID=your_linkedin_client_id
LINKEDIN_CLIENT_SECRET=your_linkedin_client_secret
LINKEDIN_ACCESS_TOKEN=your_linkedin_access_token

# JWT Configuration
JWT_SECRET=your_jwt_secret_key

# CORS Configuration
CLIENT_URL=http://localhost:3000
