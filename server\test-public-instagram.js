const { ApifyClient } = require('apify-client');
require('dotenv').config();

async function testPublicInstagram() {
  console.log('🔍 Testing Public Instagram Accounts\n');

  const client = new ApifyClient({
    token: process.env.APIFY_API_TOKEN,
  });

  // Test with highly public Instagram accounts
  const publicAccounts = [
    'instagram',      // Instagram's own account
    'cristiano',      // <PERSON><PERSON><PERSON> (most followed)
    'therock',        // The Rock
    'kyliejenner',    // <PERSON>
    'selena<PERSON>z',    // <PERSON>
    'kimkar<PERSON>hian'   // <PERSON>
  ];

  for (const username of publicAccounts) {
    try {
      console.log(`🧪 Testing: @${username}`);
      
      const input = {
        usernames: [username],
        resultsLimit: 3,
        resultsType: 'posts'
      };

      const run = await client.actor('apify/instagram-scraper').call(input, {
        timeout: 120,
        memory: 1024,
      });

      if (run.status === 'SUCCEEDED') {
        const { items } = await client.dataset(run.defaultDatasetId).listItems();
        console.log(`   📊 Items returned: ${items.length}`);
        
        if (items.length > 0 && !items[0].error) {
          console.log(`   ✅ SUCCESS: @${username} returned real data!`);
          
          const item = items[0];
          console.log(`   📝 Sample content: "${(item.text || item.caption || 'No text').substring(0, 80)}..."`);
          console.log(`   🖼️  Has image: ${item.displayUrl ? 'Yes' : 'No'}`);
          console.log(`   👍 Likes: ${item.likesCount || item.likes || 0}`);
          console.log(`   💬 Comments: ${item.commentsCount || item.comments || 0}`);
          
          // This account works - we can use it for testing
          console.log(`   🎯 WORKING ACCOUNT FOUND: @${username}`);
          break;
          
        } else if (items[0]?.error) {
          console.log(`   ❌ Error: ${items[0].error} - ${items[0].errorDescription}`);
        } else {
          console.log(`   ⚠️  No data returned`);
        }
      } else {
        console.log(`   ❌ Run failed: ${run.status}`);
      }
      
    } catch (error) {
      console.log(`   ❌ Test failed: ${error.message}`);
    }
    
    console.log(''); // Empty line
  }

  console.log('🎯 INSTAGRAM FINDINGS:');
  console.log('1. Many Instagram accounts are private or restricted');
  console.log('2. Corporate accounts (like Microsoft) often have limited access');
  console.log('3. Highly public celebrity accounts work better');
  console.log('4. Instagram heavily restricts automated scraping');
  
  console.log('\n💡 SOLUTIONS:');
  console.log('1. 🔧 Update Instagram to use working public accounts for testing');
  console.log('2. 📱 Add better error handling for private/restricted accounts');
  console.log('3. 🎭 Improve fallback content with helpful explanations');
  console.log('4. 📊 Focus on Facebook for business analysis');
  console.log('5. 🔄 Add manual Instagram post upload option');
}

testPublicInstagram().catch(console.error);
