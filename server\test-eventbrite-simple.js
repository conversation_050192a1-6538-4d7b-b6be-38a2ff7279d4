require('dotenv').config();
const axios = require('axios');

async function testEventbriteSimple() {
  console.log('🎉 Simple Eventbrite API Test\n');

  const apiKey = process.env.EVENTBRITE_API_KEY;
  
  console.log(`🔑 API Key: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);

  // Test different endpoints to find the correct one
  const endpoints = [
    'https://www.eventbriteapi.com/v3/users/me/',
    'https://www.eventbriteapi.com/v3/events/search/',
    'https://www.eventbriteapi.com/v3/categories/',
    'https://api.eventbrite.com/v3/users/me/',
    'https://api.eventbrite.com/v3/events/search/'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`\n🧪 Testing: ${endpoint}`);
      
      const response = await axios.get(endpoint, {
        headers: {
          'Authorization': `Bearer ${apiKey}`
        },
        params: endpoint.includes('search') ? {
          'location.address': 'New York',
          'page_size': 1
        } : {}
      });

      console.log(`✅ SUCCESS: ${endpoint}`);
      console.log(`📊 Response status: ${response.status}`);
      
      if (endpoint.includes('users/me')) {
        console.log(`👤 User: ${response.data.name || 'N/A'}`);
        console.log(`📧 Email: ${response.data.email || 'N/A'}`);
      } else if (endpoint.includes('search')) {
        console.log(`🎉 Events found: ${response.data.events?.length || 0}`);
        if (response.data.events?.length > 0) {
          console.log(`📅 First event: ${response.data.events[0].name?.text || 'N/A'}`);
        }
      } else if (endpoint.includes('categories')) {
        console.log(`📂 Categories: ${response.data.categories?.length || 0}`);
      }
      
      // If we found a working endpoint, use it for a real test
      if (endpoint.includes('search') && response.data.events) {
        console.log('\n🎯 Working endpoint found! Testing event search...');
        
        const searchResponse = await axios.get(endpoint, {
          headers: {
            'Authorization': `Bearer ${apiKey}`
          },
          params: {
            'location.address': 'New York, NY',
            'location.within': '10mi',
            'start_date.range_start': new Date().toISOString(),
            'page_size': 3
          }
        });
        
        const events = searchResponse.data.events;
        console.log(`\n✅ Found ${events.length} events in New York!`);
        
        events.forEach((event, index) => {
          console.log(`   ${index + 1}. ${event.name.text}`);
          console.log(`      📅 ${new Date(event.start.local).toLocaleDateString()}`);
        });
        
        return true;
      }
      
    } catch (error) {
      console.log(`❌ FAILED: ${endpoint}`);
      console.log(`   Status: ${error.response?.status || 'Network Error'}`);
      console.log(`   Error: ${error.response?.data?.error_description || error.message}`);
    }
  }
  
  return false;
}

testEventbriteSimple().then(success => {
  if (success) {
    console.log('\n🎉 Eventbrite integration ready!');
  } else {
    console.log('\n⚠️  Eventbrite integration needs attention');
  }
}).catch(console.error);
