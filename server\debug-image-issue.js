const axios = require('axios');

async function debugImageIssue() {
  console.log('🔍 Debugging Image Display Issue\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    console.log('🧪 Testing Paya Finance Post Extraction...');
    
    const response = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      options: {
        limit: 25
      }
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Raw Response Data:');
      console.log(`   📊 Total Posts: ${data.totalPosts}`);
      console.log(`   🔧 Scraping Method: ${data.scrapedWith}`);
      
      console.log('\n📝 Detailed Post Data Structure:');
      data.posts.forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      ID: ${post.id}`);
        console.log(`      Content: "${post.content.substring(0, 80)}..."`);
        console.log(`      Platform: ${post.platform}`);
        console.log(`      Date: ${post.date}`);
        console.log(`      Performance: ${post.performance}`);
        
        console.log(`      Engagement:`);
        console.log(`         Likes: ${post.engagement.likes}`);
        console.log(`         Comments: ${post.engagement.comments}`);
        console.log(`         Shares: ${post.engagement.shares}`);
        
        console.log(`      Media Object:`);
        console.log(`         URL: ${post.media?.url || 'NULL'}`);
        console.log(`         Type: ${post.media?.type || 'NULL'}`);
        console.log(`         Has Media: ${post.media?.hasMedia || 'NULL'}`);
        console.log(`         Thumbnail: ${post.media?.thumbnail || 'NULL'}`);
        
        // Check if URL is accessible
        if (post.media?.url) {
          console.log(`         🔗 URL Analysis:`);
          console.log(`            Length: ${post.media.url.length} characters`);
          console.log(`            Starts with: ${post.media.url.substring(0, 50)}...`);
          console.log(`            Contains 'photo': ${post.media.url.includes('photo')}`);
          console.log(`            Contains 'fbid': ${post.media.url.includes('fbid')}`);
        }
      });

      // Test if we can access the Facebook URLs directly
      console.log('\n🌐 Testing Facebook URL Accessibility:');
      for (let i = 0; i < Math.min(data.posts.length, 2); i++) {
        const post = data.posts[i];
        if (post.media?.url) {
          console.log(`\n   Testing Post ${i + 1} URL:`);
          console.log(`   URL: ${post.media.url}`);
          
          try {
            const urlTest = await axios.head(post.media.url, {
              timeout: 5000,
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
              }
            });
            console.log(`   ✅ URL Accessible: ${urlTest.status}`);
          } catch (error) {
            console.log(`   ❌ URL Not Accessible: ${error.response?.status || error.message}`);
            console.log(`   🔧 Issue: Facebook photo URLs may require authentication or special handling`);
          }
        }
      }

      // Check the raw Apify data
      console.log('\n🔍 Raw Apify Data Sample:');
      if (data.posts[0]?.rawData) {
        const rawData = data.posts[0].rawData;
        console.log('   Raw data keys:', Object.keys(rawData));
        console.log('   Raw image field:', rawData.image || 'NULL');
        console.log('   Raw photo field:', rawData.photo || 'NULL');
        console.log('   Raw picture field:', rawData.picture || 'NULL');
        console.log('   Raw url field:', rawData.url || 'NULL');
        console.log('   Raw attachments:', rawData.attachments ? 'Present' : 'NULL');
        console.log('   Raw media:', rawData.media ? 'Present' : 'NULL');
      }

    } else {
      console.log('❌ Request failed:', response.data.error);
    }

  } catch (error) {
    console.error('❌ Debug Error:', error.response?.data || error.message);
  }

  console.log('\n🎯 DEBUGGING CONCLUSIONS:');
  console.log('1. Check if media.url is properly set in the response');
  console.log('2. Verify if Facebook URLs are accessible from browser');
  console.log('3. Determine if we need alternative image extraction method');
  console.log('4. Consider using Facebook Graph API for direct image access');
}

debugImageIssue().catch(console.error);
