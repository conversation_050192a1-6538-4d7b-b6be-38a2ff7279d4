require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');

async function demonstrateRealVsMock() {
  console.log('🎭 Real API vs Mock Data Demonstration\n');
  console.log('This shows the difference between mock data and real AI-generated content.\n');

  // Check API configuration
  const hasOpenAI = process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY.startsWith('sk-');
  const hasWeather = process.env.OPENWEATHER_API_KEY && process.env.OPENWEATHER_API_KEY !== 'your_openweather_api_key';

  console.log('📊 Current Configuration:');
  console.log(`OpenAI: ${hasOpenAI ? '✅ Real API' : '❌ Mock Data'}`);
  console.log(`Weather: ${hasWeather ? '✅ Real API' : '❌ Mock Data'}`);
  console.log('');

  // Mock Content Example
  console.log('🎭 MOCK CONTENT (What you get without API keys):');
  console.log('=' .repeat(60));
  
  const mockContent = {
    text: "✨ At Demo Restaurant, we're passionate about providing excellent restaurant services to our New York, NY community. Visit us today!",
    hashtags: ["#NewYorkNY", "#restaurant", "#LocalBusiness"],
    imagePrompt: "A professional restaurant interior with warm lighting"
  };
  
  console.log('📝 Text:', mockContent.text);
  console.log('🏷️  Hashtags:', mockContent.hashtags.join(', '));
  console.log('🖼️  Image:', 'https://via.placeholder.com/1024x1024/3b82f6/ffffff?text=Demo+Image');
  console.log('🌤️  Weather Context: Mock sunny weather, 75°F');
  console.log('');

  // Real API Content Example
  if (hasOpenAI) {
    console.log('🤖 REAL AI CONTENT (What you get with OpenAI API):');
    console.log('=' .repeat(60));
    
    try {
      const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
      
      // Get real weather if available
      let weatherContext = 'sunny, 75°F';
      if (hasWeather) {
        try {
          const weatherResponse = await axios.get('https://api.openweathermap.org/data/2.5/weather', {
            params: {
              q: 'New York,NY',
              appid: process.env.OPENWEATHER_API_KEY,
              units: 'imperial'
            }
          });
          const weather = weatherResponse.data;
          weatherContext = `${weather.weather[0].description}, ${Math.round(weather.main.temp)}°F`;
        } catch (weatherError) {
          console.log('⚠️  Weather API error, using mock weather');
        }
      }

      const prompt = `Create a Facebook post for a restaurant called "Demo Restaurant" in New York, NY. 
                     Current weather: ${weatherContext}. 
                     Make it engaging, weather-aware, and locally relevant.
                     Return ONLY valid JSON with this exact format:
                     {"text": "post content here", "hashtags": ["tag1", "tag2", "tag3"], "imagePrompt": "detailed image description"}`;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an expert social media content creator for local businesses. Always respond with valid JSON only."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 400,
        temperature: 0.7,
      });

      const aiResponse = completion.choices[0].message.content;
      
      try {
        const aiContent = JSON.parse(aiResponse);
        console.log('📝 AI Text:', aiContent.text);
        console.log('🏷️  AI Hashtags:', aiContent.hashtags?.join(', ') || 'None');
        console.log('🖼️  AI Image Prompt:', aiContent.imagePrompt);
        console.log('🌤️  Real Weather Context:', weatherContext);
        
        // Try to generate a real image
        console.log('\n🎨 Generating AI Image...');
        try {
          const imageResponse = await openai.images.generate({
            model: "dall-e-3",
            prompt: aiContent.imagePrompt + " - professional, high quality, restaurant photography style",
            size: "1024x1024",
            quality: "standard",
            n: 1,
          });
          
          console.log('✅ Real AI Image Generated!');
          console.log('🖼️  Image URL:', imageResponse.data[0].url);
          console.log('💡 Image available for 1 hour');
          
        } catch (imageError) {
          console.log('❌ Image generation failed:', imageError.message);
        }
        
      } catch (parseError) {
        console.log('❌ AI response parsing failed. Raw response:');
        console.log(aiResponse);
      }
      
    } catch (error) {
      console.log('❌ OpenAI API Error:', error.message);
    }
  } else {
    console.log('🤖 REAL AI CONTENT: Not available (OpenAI API key needed)');
    console.log('=' .repeat(60));
    console.log('To see real AI-generated content:');
    console.log('1. Get OpenAI API key from https://platform.openai.com/');
    console.log('2. Add to server/.env: OPENAI_API_KEY=sk-your-key-here');
    console.log('3. Run this demo again');
  }

  console.log('\n📊 COMPARISON SUMMARY:');
  console.log('=' .repeat(60));
  console.log('Mock Data:');
  console.log('• ❌ Generic, repetitive content');
  console.log('• ❌ No weather awareness');
  console.log('• ❌ No local context');
  console.log('• ❌ Placeholder images');
  console.log('• ✅ Always works (no API costs)');
  
  console.log('\nReal AI Data:');
  console.log('• ✅ Unique, creative content');
  console.log('• ✅ Weather-aware messaging');
  console.log('• ✅ Local context integration');
  console.log('• ✅ Custom AI-generated images');
  console.log('• ✅ Engaging, professional quality');
  console.log('• 💰 Small cost per generation (~$0.05-0.10 per post)');

  console.log('\n🎯 RECOMMENDATION:');
  if (hasOpenAI) {
    console.log('✅ You have OpenAI configured! Your content will be AI-powered.');
    if (!hasWeather) {
      console.log('💡 Add weather API for even better, weather-aware content.');
    }
  } else {
    console.log('🚨 Set up OpenAI API for professional AI-generated content!');
    console.log('   The difference in quality is significant for your business.');
  }

  console.log('\n🚀 Next Steps:');
  console.log('1. Run: node setup-real-apis.js (to configure APIs)');
  console.log('2. Run: node server/test-external-apis.js (to test setup)');
  console.log('3. Restart server: npm run server:dev');
  console.log('4. Generate content in the app to see real AI in action!');
}

demonstrateRealVsMock().catch(console.error);
