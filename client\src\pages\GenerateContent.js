import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { usePosts } from '../contexts/PostsContext';
import { toast } from 'react-hot-toast';
import {
  Sparkles,
  Calendar,
  MapPin,
  Cloud,
  Users,
  Zap,
  Copy,
  Download,
  Share2,
  RefreshCw,
  Image,
  Palette,
  Eye,
  Edit3,
  RotateCcw,
  Save,
  X
} from 'lucide-react';

const GenerateContent = () => {
  const { user, apiCall } = useAuth();
  const { addPostToCalendar, addPostsToCalendar, loading: postsLoading } = usePosts();
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedPosts, setGeneratedPosts] = useState([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState(['facebook', 'instagram']);
  const [contentType, setContentType] = useState('promotional');
  const [dateRange, setDateRange] = useState('7');
  const [error, setError] = useState('');

  // Edit functionality state
  const [editingPost, setEditingPost] = useState(null);
  const [editedContent, setEditedContent] = useState('');
  const [regeneratingPost, setRegeneratingPost] = useState(null);
  const [regeneratingImage, setRegeneratingImage] = useState(null);

  const platforms = [
    { id: 'facebook', name: 'Facebook', color: 'bg-blue-500' },
    { id: 'instagram', name: 'Instagram', color: 'bg-pink-500' },
    { id: 'linkedin', name: 'LinkedIn', color: 'bg-blue-600' },
    { id: 'twitter', name: 'Twitter', color: 'bg-sky-500' }
  ];

  const contentTypes = [
    { id: 'promotional', name: 'Promotional', description: 'Sales and offers' },
    { id: 'educational', name: 'Educational', description: 'Tips and how-tos' },
    { id: 'engaging', name: 'Engaging', description: 'Questions and polls' },
    { id: 'seasonal', name: 'Seasonal', description: 'Weather and events' },
    { id: 'mixed', name: 'Mixed', description: 'Variety of content types' }
  ];

  // Platform-specific image design templates
  const getPlatformOptimizedImageDesign = (platform, contentType, weather, localEvent) => {
    const designs = {
      facebook: {
        dimensions: '1200x630',
        aspectRatio: '1.91:1 (Landscape)',
        bestPractices: ['High contrast text overlay', 'Brand logo placement', 'Call-to-action friendly'],
        style: 'engaging, shareable content with clear branding',
        textOverlay: true,
        brandingSpace: 'top-right corner'
      },
      instagram: {
        dimensions: '1080x1080',
        aspectRatio: '1:1 (Square)',
        bestPractices: ['Aesthetic appeal', 'Story-friendly', 'Hashtag optimized'],
        style: 'visually stunning, lifestyle-focused imagery',
        textOverlay: false,
        brandingSpace: 'subtle watermark'
      },
      linkedin: {
        dimensions: '1200x627',
        aspectRatio: '1.91:1 (Landscape)',
        bestPractices: ['Professional appearance', 'Industry relevant', 'Thought leadership'],
        style: 'clean, professional, business-appropriate',
        textOverlay: true,
        brandingSpace: 'bottom-left corner'
      },
      twitter: {
        dimensions: '1200x675',
        aspectRatio: '16:9 (Landscape)',
        bestPractices: ['Quick visual impact', 'Mobile optimized', 'Conversation starter'],
        style: 'bold, attention-grabbing, concise messaging',
        textOverlay: true,
        brandingSpace: 'minimal branding'
      }
    };

    return designs[platform] || designs.facebook;
  };

  // Smart content generation based on date proximity
  const generateSmartContent = (daysFromNow) => {
    if (daysFromNow <= 7) {
      // Weather-aware content (accurate forecasts available)
      return {
        contentType: 'weather-aware',
        weatherAccuracy: 'real-time',
        description: 'Uses live weather data and immediate local events'
      };
    } else {
      // Seasonal/evergreen content (no specific weather claims)
      return {
        contentType: 'seasonal-strategic',
        weatherAccuracy: 'seasonal-themes',
        description: 'Uses seasonal themes, educational content, and community focus'
      };
    }
  };

  const mockPosts = [
    // DAYS 1-7: Weather-Aware Content (Real-time accuracy)
    {
      id: 1,
      platform: 'facebook',
      content: "🌟 Beautiful sunny day in New York TODAY! Perfect weather to try our new summer menu. Our fresh garden salad with locally sourced ingredients is the perfect light meal for this gorgeous 75°F afternoon! ☀️🥗\n\nWhat's your favorite sunny day meal? Let us know in the comments!\n\n#LocalEats #SummerMenu #NYC #FreshFood",
      hashtags: ['#LocalEats', '#SummerMenu', '#NYC', '#FreshFood'],
      date: '2024-01-15',
      daysFromNow: 2,
      contentStrategy: 'weather-aware',
      weather: 'Sunny, 75°F (Real-time)',
      localEvent: 'Summer Festival Downtown',
      imageDesign: {
        type: 'food_photography',
        platform: 'facebook',
        description: 'Fresh garden salad with vibrant greens and colorful vegetables on outdoor table, with text overlay "Fresh Summer Menu" and restaurant logo',
        style: 'bright, engaging with clear text overlay for Facebook sharing',
        elements: ['Fresh salad bowl', 'Sunny outdoor setting', 'Text overlay', 'Brand logo', 'Call-to-action'],
        dimensions: '1200x630 (Facebook Landscape)',
        aspectRatio: '1.91:1',
        platformFeatures: {
          textOverlay: 'Fresh Summer Menu - Perfect for 75°F Weather!',
          brandingPlacement: 'top-right corner',
          callToAction: 'Visit us today!',
          engagement: 'Question in post to drive comments'
        },
        mockUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=1200&h=630&fit=crop',
        optimization: 'High contrast for news feed visibility, shareable design'
      }
    },
    {
      id: 2,
      platform: 'instagram',
      content: "Rainy day vibes call for comfort food! ☔ \n\nWarm up with our signature hot soup and fresh-baked bread. Nothing beats the cozy atmosphere when it's drizzling TONIGHT. 🍲✨\n\n#ComfortFood #RainyDay #CozyVibes #WarmUp #LocalEats #Foodie #NYC #WeatherPerfect #SoupSeason #Cozy",
      hashtags: ['#ComfortFood', '#RainyDay', '#CozyVibes', '#WarmUp', '#LocalEats', '#Foodie', '#NYC', '#WeatherPerfect', '#SoupSeason', '#Cozy'],
      date: '2024-01-16',
      daysFromNow: 5,
      contentStrategy: 'weather-aware',
      weather: 'Rainy, 62°F (Real-time)',
      localEvent: 'Indoor Market Weekend',
      imageDesign: {
        type: 'lifestyle_aesthetic',
        platform: 'instagram',
        description: 'Artistic overhead shot of steaming soup bowl with artisanal bread, moody lighting, rain droplets on window, Instagram-worthy composition',
        style: 'aesthetic, moody, lifestyle-focused with warm tones perfect for Instagram feed',
        elements: ['Steaming soup bowl', 'Artisanal bread', 'Moody lighting', 'Rain droplets', 'Aesthetic composition'],
        dimensions: '1080x1080 (Instagram Square)',
        aspectRatio: '1:1',
        platformFeatures: {
          composition: 'Rule of thirds, overhead shot',
          lighting: 'Warm, moody, story-worthy',
          aesthetic: 'Feed-worthy, lifestyle brand',
          hashtagOptimized: '10 relevant hashtags for discovery'
        },
        mockUrl: 'https://images.unsplash.com/photo-1547592180-85f173990554?w=1080&h=1080&fit=crop',
        optimization: 'Square format for feed, story-friendly, aesthetic appeal'
      }
    },
    {
      id: 3,
      platform: 'linkedin',
      content: "As a local business, we're proud to support our community during the Downtown Business Expo this week. 🤝\n\nNetworking with fellow entrepreneurs and showcasing what makes our neighborhood special. It's inspiring to see how local businesses come together to drive economic growth and community engagement.\n\nWhat local business partnerships have made the biggest impact in your area?\n\n#LocalBusiness #Community #Networking #BusinessExpo #Entrepreneurship #CommunitySupport",
      hashtags: ['#LocalBusiness', '#Community', '#Networking', '#BusinessExpo', '#Entrepreneurship', '#CommunitySupport'],
      date: '2024-01-17',
      weather: 'Partly Cloudy, 68°F',
      localEvent: 'Downtown Business Expo',
      imageDesign: {
        type: 'business_professional',
        platform: 'linkedin',
        description: 'Professional business networking scene with diverse professionals in business attire, modern conference setting, subtle company branding',
        style: 'clean, professional, corporate-appropriate with thought leadership focus',
        elements: ['Business professionals', 'Modern conference room', 'Networking scene', 'Professional attire', 'Subtle branding'],
        dimensions: '1200x627 (LinkedIn Landscape)',
        aspectRatio: '1.91:1',
        platformFeatures: {
          professionalism: 'Corporate-appropriate imagery',
          thoughtLeadership: 'Industry-relevant content',
          networking: 'Business relationship focus',
          brandingStyle: 'Subtle, professional logo placement'
        },
        mockUrl: 'https://images.unsplash.com/photo-1521737711867-e3b97375f902?w=1200&h=627&fit=crop',
        optimization: 'Professional appearance, industry credibility, B2B focused'
      }
    },
    {
      id: 4,
      platform: 'twitter',
      content: "☀️ Perfect 75°F weather = perfect patio dining weather! 🌿\n\nOur fresh summer salad is flying off the menu today. Sometimes the best marketing is just great weather and great food! 😄\n\n#SummerVibes #PatioSeason #LocalEats #NYC",
      hashtags: ['#SummerVibes', '#PatioSeason', '#LocalEats', '#NYC'],
      date: '2024-01-15',
      weather: 'Sunny, 75°F',
      localEvent: 'Summer Festival Downtown',
      imageDesign: {
        type: 'social_media_graphic',
        platform: 'twitter',
        description: 'Bold, eye-catching graphic with fresh salad image, weather icon, and concise text overlay optimized for Twitter engagement',
        style: 'bold, attention-grabbing design optimized for mobile Twitter feed',
        elements: ['Fresh salad hero shot', 'Weather icon', 'Bold text overlay', 'Minimal branding', 'Mobile-first design'],
        dimensions: '1200x675 (Twitter Landscape)',
        aspectRatio: '16:9',
        platformFeatures: {
          visualImpact: 'Quick scroll-stopping design',
          mobileOptimized: 'Clear visibility on mobile devices',
          conversationStarter: 'Weather-related content for engagement',
          brandingStyle: 'Minimal, non-intrusive logo'
        },
        mockUrl: 'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=1200&h=675&fit=crop',
        optimization: 'Mobile-first design, quick visual impact, conversation-friendly'
      }
    },

    // DAYS 8-30: Seasonal/Strategic Content (No specific weather claims)
    {
      id: 5,
      platform: 'facebook',
      content: "🏠 Winter Home Maintenance Tip: Check your gutters and downspouts before the heavy snow season arrives! \n\nProper drainage prevents ice dams and water damage. Our team has been helping Denver homeowners prepare for winter for over 15 years.\n\nNeed a professional inspection? We're here to help! 💪\n\n#WinterPrep #HomeMaintenace #Denver #ProfessionalService",
      hashtags: ['#WinterPrep', '#HomeMaintenace', '#Denver', '#ProfessionalService'],
      date: '2024-01-25',
      daysFromNow: 15,
      contentStrategy: 'seasonal-strategic',
      weather: 'Seasonal Theme (Winter)',
      localEvent: 'Educational Content',
      imageDesign: {
        type: 'educational_infographic',
        platform: 'facebook',
        description: 'Clean, professional infographic showing winter home maintenance checklist with branded elements',
        style: 'educational, trustworthy design with clear visual hierarchy',
        elements: ['Home maintenance checklist', 'Winter preparation tips', 'Professional branding', 'Clear typography'],
        dimensions: '1200x630 (Facebook Landscape)',
        aspectRatio: '1.91:1',
        platformFeatures: {
          educationalValue: 'Helpful winter preparation tips',
          brandingStyle: 'Professional, trustworthy appearance',
          shareability: 'Valuable content people want to save/share',
          callToAction: 'Professional service offering'
        },
        mockUrl: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=1200&h=630&fit=crop',
        optimization: 'Educational value, shareable content, professional credibility'
      }
    },
    {
      id: 6,
      platform: 'linkedin',
      content: "15 years of serving the Denver community has taught us one thing: preparation beats reaction every time. 🎯\n\nWhether it's preventive maintenance, emergency repairs, or seasonal preparations, our proactive approach has saved our clients thousands in potential damage costs.\n\nWhat's your business philosophy when it comes to serving your community?\n\n#BusinessPhilosophy #CommunityService #Denver #PreventiveMaintenance #CustomerFirst",
      hashtags: ['#BusinessPhilosophy', '#CommunityService', '#Denver', '#PreventiveMaintenance', '#CustomerFirst'],
      date: '2024-02-05',
      daysFromNow: 25,
      contentStrategy: 'seasonal-strategic',
      weather: 'Community Focus (No weather)',
      localEvent: 'Brand Storytelling',
      imageDesign: {
        type: 'brand_storytelling',
        platform: 'linkedin',
        description: 'Professional team photo or company milestone image with Denver skyline background',
        style: 'authentic, professional brand storytelling with community focus',
        elements: ['Team photo', 'Denver skyline', 'Professional setting', 'Community connection'],
        dimensions: '1200x627 (LinkedIn Landscape)',
        aspectRatio: '1.91:1',
        platformFeatures: {
          authenticity: 'Real team, real community connection',
          professionalism: 'Business-appropriate imagery',
          thoughtLeadership: 'Industry experience and philosophy',
          engagement: 'Question to drive professional discussion'
        },
        mockUrl: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=1200&h=627&fit=crop',
        optimization: 'Professional networking, thought leadership, community connection'
      }
    }
  ];

  const handleGenerate = async () => {
    setIsGenerating(true);
    setError('');

    try {
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() + parseInt(dateRange));

      // Prepare API request data
      const generateData = {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        platforms: selectedPlatforms.length > 0 ? selectedPlatforms : ['facebook', 'instagram'],
        regenerate: true
      };

      // Call the real API
      const response = await apiCall('/api/content/generate', {
        method: 'POST',
        body: JSON.stringify(generateData)
      });

      if (response.ok) {
        const data = await response.json();

        if (data.generatedPosts && data.generatedPosts.length > 0) {
          // Convert API posts to frontend format
          const formattedPosts = data.generatedPosts.map(post => ({
            id: post.id,
            platform: post.platform,
            content: post.content_text,
            hashtags: post.hashtags || [],
            date: post.date_scheduled,
            weather: post.weather_context || '',
            localEvent: post.events_context || '',
            imageDesign: post.image_url ? {
              mockUrl: post.image_url,
              optimization: `AI-generated image for ${post.platform}`
            } : null
          }));

          setGeneratedPosts(formattedPosts);

          // Add posts to calendar context
          await addPostsToCalendar(formattedPosts);

          toast.success(`Generated ${formattedPosts.length} posts successfully!`);
        } else {
          setError('No posts were generated. Please try again.');
        }

        // Show any errors that occurred during generation
        if (data.errors && data.errors.length > 0) {
          console.warn('Generation errors:', data.errors);
          toast.warning(`Generated posts with ${data.errors.length} warnings`);
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate content');
      }
    } catch (error) {
      console.error('Content generation error:', error);
      setError(error.message || 'Failed to generate content. Please try again.');
      toast.error('Failed to generate content');
    } finally {
      setIsGenerating(false);
    }
  };

  const handlePlatformToggle = (platformId) => {
    setSelectedPlatforms(prev =>
      prev.includes(platformId)
        ? prev.filter(id => id !== platformId)
        : [...prev, platformId]
    );
  };

  const copyToClipboard = (content) => {
    navigator.clipboard.writeText(content);
    // You could add a toast notification here
  };

  const handleAddToCalendar = async (post) => {
    await addPostToCalendar(post);
  };

  const handleAddAllToCalendar = async () => {
    if (generatedPosts.length > 0) {
      await addPostsToCalendar(generatedPosts);
    }
  };

  // Edit functionality
  const handleEditPost = (post) => {
    setEditingPost(post.id);
    setEditedContent(post.content);
  };

  const handleSaveEdit = (postId) => {
    setGeneratedPosts(prev =>
      prev.map(post =>
        post.id === postId
          ? { ...post, content: editedContent }
          : post
      )
    );
    setEditingPost(null);
    setEditedContent('');
  };

  const handleCancelEdit = () => {
    setEditingPost(null);
    setEditedContent('');
  };

  // Regenerate functionality
  const handleRegenerateText = async (postId) => {
    setRegeneratingPost(postId);

    // Simulate API call for text regeneration
    setTimeout(() => {
      const newTexts = [
        "🌟 Another beautiful day to showcase our amazing offerings! The weather is perfect for trying something new. Come experience what makes us special! ✨",
        "Perfect weather calls for perfect moments! 🌤️ Join us today and discover why our community loves what we do. Every day brings new opportunities!",
        "Today's forecast: 100% chance of amazing experiences! ☀️ Whether it's sunny or cloudy, we're here to brighten your day with exceptional service."
      ];

      const randomText = newTexts[Math.floor(Math.random() * newTexts.length)];

      setGeneratedPosts(prev =>
        prev.map(post =>
          post.id === postId
            ? { ...post, content: randomText }
            : post
        )
      );

      setRegeneratingPost(null);
    }, 2000);
  };

  const handleRegenerateImage = async (postId) => {
    setRegeneratingImage(postId);

    // Simulate API call for image regeneration
    setTimeout(() => {
      const newImageUrls = [
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=1200&h=630&fit=crop',
        'https://images.unsplash.com/photo-1504674900247-0877df9cc836?w=1200&h=630&fit=crop',
        'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=1200&h=630&fit=crop'
      ];

      const randomUrl = newImageUrls[Math.floor(Math.random() * newImageUrls.length)];

      setGeneratedPosts(prev =>
        prev.map(post =>
          post.id === postId
            ? {
              ...post,
              imageDesign: {
                ...post.imageDesign,
                mockUrl: randomUrl,
                description: 'Regenerated image with fresh perspective and updated visual elements'
              }
            }
            : post
        )
      );

      setRegeneratingImage(null);
    }, 2000);
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Generate Content</h1>
        <p className="text-gray-600">Create AI-powered social media posts tailored to your business and local conditions</p>
      </div>

      {/* Generation Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Content Settings</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Platforms */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Platforms</label>
            <div className="space-y-2">
              {platforms.map(platform => (
                <label key={platform.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedPlatforms.includes(platform.id)}
                    onChange={() => handlePlatformToggle(platform.id)}
                    className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  />
                  <span className={`ml-2 w-3 h-3 rounded-full ${platform.color}`}></span>
                  <span className="ml-2 text-sm text-gray-700">{platform.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Content Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Content Type</label>
            <select
              value={contentType}
              onChange={(e) => setContentType(e.target.value)}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              {contentTypes.map(type => (
                <option key={type.id} value={type.id}>
                  {type.name} - {type.description}
                </option>
              ))}
            </select>
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">Generate for</label>
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value)}
              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
            >
              <option value="3">Next 3 days (Weather-Aware)</option>
              <option value="7">Next 7 days (Weather-Aware)</option>
              <option value="14">Next 2 weeks (7 Weather + 7 Seasonal)</option>
              <option value="30">Next 30 days (7 Weather + 23 Seasonal)</option>
            </select>
            <p className="text-sm text-gray-600 mt-2">
              <span className="font-medium">Smart Content Mix:</span> Days 1-7 use real-time weather data. Days 8+ use seasonal themes and educational content.
            </p>
          </div>
        </div>

        {/* Business Context */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4 text-sm text-gray-600">
            <div className="flex items-center space-x-1">
              <Users size={16} />
              <span>{user?.businessType || 'Business'}</span>
            </div>
            <div className="flex items-center space-x-1">
              <MapPin size={16} />
              <span>{user?.location || 'Location'}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Cloud size={16} />
              <span>Weather-aware content</span>
            </div>
          </div>
        </div>

        {/* Generate Button */}
        <div className="mt-6">
          <button
            onClick={handleGenerate}
            disabled={isGenerating || selectedPlatforms.length === 0}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium flex items-center space-x-2"
          >
            {isGenerating ? (
              <>
                <RefreshCw size={20} className="animate-spin" />
                <span>Generating Content...</span>
              </>
            ) : (
              <>
                <Sparkles size={20} />
                <span>Generate Content</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Generated Content */}
      {generatedPosts.length > 0 && (
        <div className="space-y-4">
          <h2 className="text-lg font-semibold text-gray-900">Generated Posts</h2>

          {generatedPosts.map(post => {
            const platform = platforms.find(p => p.id === post.platform);
            return (
              <div key={post.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <span className={`w-4 h-4 rounded-full ${platform?.color}`}></span>
                    <span className="font-medium text-gray-900">{platform?.name}</span>
                    <span className="text-sm text-gray-500">{post.date}</span>
                    {/* Content Strategy Indicator */}
                    {post.contentStrategy && (
                      <span className={`text-xs px-2 py-1 rounded-full font-medium ${post.contentStrategy === 'weather-aware'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-green-100 text-green-800'
                        }`}>
                        {post.contentStrategy === 'weather-aware' ? '🌤️ Weather-Aware' : '🎯 Strategic'}
                      </span>
                    )}
                    {post.daysFromNow && (
                      <span className="text-xs text-gray-400">
                        (Day {post.daysFromNow})
                      </span>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => copyToClipboard(post.content)}
                      className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                      title="Copy content"
                    >
                      <Copy size={16} />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                      <Download size={16} />
                    </button>
                    <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
                      <Share2 size={16} />
                    </button>
                  </div>
                </div>

                {/* Content Section with Edit Functionality */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Post Content</span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditPost(post)}
                        disabled={editingPost === post.id}
                        className="flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 disabled:text-gray-400"
                      >
                        <Edit3 size={12} />
                        <span>Edit</span>
                      </button>
                      <button
                        onClick={() => handleRegenerateText(post.id)}
                        disabled={regeneratingPost === post.id}
                        className="flex items-center space-x-1 text-xs text-green-600 hover:text-green-800 disabled:text-gray-400"
                      >
                        {regeneratingPost === post.id ? (
                          <RefreshCw size={12} className="animate-spin" />
                        ) : (
                          <RotateCcw size={12} />
                        )}
                        <span>{regeneratingPost === post.id ? 'Regenerating...' : 'Regenerate'}</span>
                      </button>
                    </div>
                  </div>

                  {editingPost === post.id ? (
                    <div className="space-y-3">
                      <textarea
                        value={editedContent}
                        onChange={(e) => setEditedContent(e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                        rows={4}
                        placeholder="Edit your post content..."
                      />
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleSaveEdit(post.id)}
                          className="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
                        >
                          <Save size={12} />
                          <span>Save</span>
                        </button>
                        <button
                          onClick={handleCancelEdit}
                          className="flex items-center space-x-1 bg-gray-500 hover:bg-gray-600 text-white px-3 py-1 rounded text-sm"
                        >
                          <X size={12} />
                          <span>Cancel</span>
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-gray-800 whitespace-pre-line bg-gray-50 p-3 rounded-lg border">
                      {post.content}
                    </p>
                  )}
                </div>

                {/* Enhanced Platform-Specific Image Design Section */}
                {post.imageDesign && (
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg border">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <Image size={16} className="text-primary-600" />
                        <span className="font-medium text-gray-900">AI-Generated Image Design</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleRegenerateImage(post.id)}
                          disabled={regeneratingImage === post.id}
                          className="flex items-center space-x-1 text-xs text-purple-600 hover:text-purple-800 disabled:text-gray-400"
                        >
                          {regeneratingImage === post.id ? (
                            <RefreshCw size={12} className="animate-spin" />
                          ) : (
                            <RotateCcw size={12} />
                          )}
                          <span>{regeneratingImage === post.id ? 'Regenerating...' : 'Regenerate Image'}</span>
                        </button>
                        <span className={`text-xs px-2 py-1 rounded-full font-medium ${post.platform === 'facebook' ? 'bg-blue-100 text-blue-800' :
                          post.platform === 'instagram' ? 'bg-pink-100 text-pink-800' :
                            post.platform === 'linkedin' ? 'bg-blue-100 text-blue-900' :
                              'bg-sky-100 text-sky-800'
                          }`}>
                          {post.platform.charAt(0).toUpperCase() + post.platform.slice(1)} Optimized
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      {/* Image Preview */}
                      <div className="space-y-2">
                        <img
                          src={post.imageDesign.mockUrl}
                          alt={post.imageDesign.description}
                          className="w-full h-32 object-cover rounded-lg shadow-sm border"
                        />
                        <div className="text-xs text-gray-500 space-y-1">
                          <div className="font-medium">{post.imageDesign.dimensions}</div>
                          <div>{post.imageDesign.aspectRatio}</div>
                        </div>
                      </div>

                      {/* Design Details */}
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Palette size={12} className="text-gray-400" />
                            <span className="text-xs font-medium text-gray-700">Style & Approach</span>
                          </div>
                          <p className="text-sm text-gray-600">{post.imageDesign.style}</p>
                        </div>

                        <div>
                          <div className="flex items-center space-x-1 mb-1">
                            <Eye size={12} className="text-gray-400" />
                            <span className="text-xs font-medium text-gray-700">Visual Elements</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {post.imageDesign.elements.map((element, idx) => (
                              <span key={idx} className="text-xs bg-white px-2 py-1 rounded-full text-gray-600 border">
                                {element}
                              </span>
                            ))}
                          </div>
                        </div>
                      </div>

                      {/* Platform-Specific Features */}
                      <div className="space-y-3">
                        <div>
                          <div className="flex items-center space-x-1 mb-2">
                            <Zap size={12} className="text-primary-600" />
                            <span className="text-xs font-medium text-gray-700">Platform Features</span>
                          </div>
                          <div className="space-y-2">
                            {post.imageDesign.platformFeatures && Object.entries(post.imageDesign.platformFeatures).map(([key, value], idx) => (
                              <div key={idx} className="text-xs">
                                <span className="font-medium text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1')}: </span>
                                <span className="text-gray-500">{value}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        <div className="pt-2 border-t border-gray-200">
                          <div className="text-xs font-medium text-gray-700 mb-1">Optimization</div>
                          <p className="text-xs text-gray-500">{post.imageDesign.optimization}</p>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 pt-3 border-t border-gray-200">
                      <p className="text-sm text-gray-600 italic">{post.imageDesign.description}</p>
                    </div>
                  </div>
                )}

                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center space-x-4">
                    <span>🌤️ {post.weather}</span>
                    <span>📅 {post.localEvent}</span>
                  </div>

                  <button
                    onClick={() => handleAddToCalendar(post)}
                    disabled={postsLoading}
                    className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white px-3 py-1 rounded-md text-sm font-medium transition-colors"
                  >
                    <Calendar size={14} />
                    <span>Add to Calendar</span>
                  </button>
                </div>
              </div>
            );
          })}

          <div className="text-center pt-4">
            <button
              onClick={handleAddAllToCalendar}
              disabled={postsLoading || generatedPosts.length === 0}
              className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium flex items-center space-x-2 mx-auto transition-colors"
            >
              <Calendar size={16} />
              <span>
                {postsLoading ? 'Adding...' : `Add All ${generatedPosts.length} Posts to Calendar`}
              </span>
            </button>
          </div>
        </div>
      )}

      {/* Empty State */}
      {generatedPosts.length === 0 && !isGenerating && (
        <div className="text-center py-12">
          <Zap className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Generate Content</h3>
          <p className="text-gray-500 mb-4">
            Configure your settings above and click "Generate Content" to create AI-powered posts
          </p>
        </div>
      )}
    </div>
  );
};

export default GenerateContent;
