import React from 'react';
import { 
  Zap, 
  MapPin, 
  Cloud, 
  Calendar, 
  Users, 
  TrendingUp,
  Palette,
  BarChart3,
  Globe,
  Shield,
  Clock,
  Sparkles
} from 'lucide-react';

const Features = () => {
  const mainFeatures = [
    {
      icon: Zap,
      title: 'AI-Powered Content Generation',
      description: 'Advanced GPT-4 technology creates engaging, authentic content that matches your brand voice and resonates with your local audience.',
      benefits: [
        'Consistent brand voice across all posts',
        'Contextually relevant content',
        'Time-saving automation',
        'Professional quality writing'
      ]
    },
    {
      icon: MapPin,
      title: 'Hyper-Local Intelligence',
      description: 'Our AI understands your local market, incorporating community events, local landmarks, and regional preferences into your content.',
      benefits: [
        'Community event integration',
        'Local landmark references',
        'Regional language and culture',
        'Neighborhood-specific content'
      ]
    },
    {
      icon: Cloud,
      title: 'Weather-Aware Content',
      description: 'Real-time weather integration ensures your content is always relevant to current conditions and seasonal trends.',
      benefits: [
        'Weather-appropriate messaging',
        'Seasonal content suggestions',
        'Storm and weather alerts',
        'Climate-based promotions'
      ]
    },
    {
      icon: Calendar,
      title: 'Smart Content Calendar',
      description: 'Intelligent scheduling system that optimizes posting times and manages your entire content strategy.',
      benefits: [
        'Optimal posting time suggestions',
        '30-day content planning',
        'Holiday and event awareness',
        'Content gap identification'
      ]
    }
  ];

  const additionalFeatures = [
    {
      icon: Palette,
      title: 'Brand Voice Learning',
      description: 'AI analyzes your existing content to maintain consistent messaging'
    },
    {
      icon: Globe,
      title: 'Multi-Platform Optimization',
      description: 'Content tailored for Facebook, Instagram, LinkedIn, and Twitter'
    },
    {
      icon: BarChart3,
      title: 'Performance Analytics',
      description: 'Track engagement and optimize your content strategy'
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-level security with data encryption and privacy protection'
    },
    {
      icon: Clock,
      title: 'Time-Saving Automation',
      description: 'Generate 30 days of content in minutes, not hours'
    },
    {
      icon: Sparkles,
      title: 'Image Generation',
      description: 'AI-created visuals that match your brand style and message'
    }
  ];

  const useCases = [
    {
      title: 'Restaurants',
      description: 'Weather-based menu promotions, local event tie-ins, seasonal specials',
      example: '"Perfect rainy day for our hearty soup specials! Stay warm with our comfort food classics."'
    },
    {
      title: 'Retail Stores',
      description: 'Seasonal inventory promotion, local shopping events, weather-appropriate products',
      example: '"Spring cleaning season is here! Check out our organization solutions perfect for Denver\'s sunny weather."'
    },
    {
      title: 'Service Businesses',
      description: 'Weather-related service needs, local community involvement, seasonal maintenance',
      example: '"With tonight\'s freeze warning in Austin, now\'s the time to schedule your HVAC maintenance check."'
    },
    {
      title: 'Fitness Studios',
      description: 'Weather-appropriate workout suggestions, local fitness events, seasonal challenges',
      example: '"Beautiful 75° weather in Miami today! Perfect for our outdoor yoga class at 6 PM."'
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-16">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
          Powerful Features for Local Businesses
        </h1>
        <p className="text-xl text-gray-600 max-w-3xl mx-auto">
          LocalPost.ai combines cutting-edge AI technology with deep local intelligence 
          to create social media content that truly connects with your community.
        </p>
      </div>

      {/* Main Features */}
      <div className="space-y-16 mb-20">
        {mainFeatures.map((feature, index) => {
          const Icon = feature.icon;
          const isEven = index % 2 === 0;
          
          return (
            <div key={index} className={`flex flex-col ${isEven ? 'lg:flex-row' : 'lg:flex-row-reverse'} items-center gap-12`}>
              <div className="flex-1">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mr-4">
                    <Icon className="text-primary-600" size={24} />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">{feature.title}</h2>
                </div>
                
                <p className="text-gray-600 text-lg mb-6">
                  {feature.description}
                </p>
                
                <ul className="space-y-2">
                  {feature.benefits.map((benefit, benefitIndex) => (
                    <li key={benefitIndex} className="flex items-center text-gray-700">
                      <div className="w-2 h-2 bg-primary-600 rounded-full mr-3"></div>
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
              
              <div className="flex-1">
                <div className="bg-gradient-to-br from-primary-50 to-blue-50 rounded-2xl p-8 h-64 flex items-center justify-center">
                  <Icon className="text-primary-600" size={80} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Additional Features Grid */}
      <div className="mb-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Everything You Need to Succeed
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our comprehensive platform includes all the tools and features you need to create, manage, and optimize your social media presence.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {additionalFeatures.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="card text-center hover:shadow-lg transition-shadow">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Icon className="text-primary-600" size={24} />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>

      {/* Use Cases */}
      <div className="bg-gray-50 rounded-2xl p-8 mb-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Perfect for Every Local Business
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            See how LocalPost.ai creates tailored content for different types of businesses in your community.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {useCases.map((useCase, index) => (
            <div key={index} className="bg-white rounded-lg p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {useCase.title}
              </h3>
              <p className="text-gray-600 mb-4">
                {useCase.description}
              </p>
              <div className="bg-primary-50 border-l-4 border-primary-600 p-4 rounded">
                <p className="text-primary-800 italic">
                  {useCase.example}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center bg-primary-600 rounded-2xl p-12 text-white">
        <h2 className="text-3xl font-bold mb-4">
          Ready to Transform Your Social Media?
        </h2>
        <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
          Join hundreds of local businesses already using LocalPost.ai to grow their online presence and connect with their community.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button className="bg-white text-primary-600 hover:bg-gray-50 font-bold py-4 px-8 rounded-lg text-lg transition-colors">
            Start Free Trial
          </button>
          <button className="border-2 border-white text-white hover:bg-white hover:text-primary-600 font-bold py-4 px-8 rounded-lg text-lg transition-colors">
            View Pricing
          </button>
        </div>
      </div>
    </div>
  );
};

export default Features;
