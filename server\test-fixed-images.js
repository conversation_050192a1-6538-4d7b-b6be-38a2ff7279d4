const axios = require('axios');

async function testFixedImages() {
  console.log('🖼️  Testing Fixed Image Extraction\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    console.log('🧪 Testing Fixed Paya Finance Image Extraction...');
    
    const response = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      options: {
        limit: 25
      }
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Fixed Image Extraction Results:');
      console.log(`   📊 Total Posts: ${data.totalPosts}`);
      console.log(`   🔧 Scraping Method: ${data.scrapedWith}`);
      
      console.log('\n🖼️  Image URL Analysis:');
      data.posts.forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      Content: "${post.content.substring(0, 60)}..."`);
        console.log(`      Media URL: ${post.media?.url || 'NULL'}`);
        console.log(`      Media Type: ${post.media?.type || 'NULL'}`);
        console.log(`      Has Media: ${post.media?.hasMedia || 'NULL'}`);
        console.log(`      Thumbnail: ${post.media?.thumbnail || 'NULL'}`);
        
        if (post.media?.url) {
          console.log(`      🔍 URL Analysis:`);
          console.log(`         Is Direct Image: ${post.media.url.includes('fbcdn.net') ? 'YES ✅' : 'NO ❌'}`);
          console.log(`         Is Facebook CDN: ${post.media.url.includes('scontent') ? 'YES ✅' : 'NO ❌'}`);
          console.log(`         URL Length: ${post.media.url.length} characters`);
          console.log(`         URL Preview: ${post.media.url.substring(0, 80)}...`);
        }
      });

      // Test if the new URLs are accessible
      console.log('\n🌐 Testing New Image URL Accessibility:');
      for (let i = 0; i < Math.min(data.posts.length, 2); i++) {
        const post = data.posts[i];
        if (post.media?.url) {
          console.log(`\n   Testing Post ${i + 1} Image URL:`);
          console.log(`   URL: ${post.media.url.substring(0, 100)}...`);
          
          try {
            const urlTest = await axios.head(post.media.url, {
              timeout: 10000,
              headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
              }
            });
            console.log(`   ✅ Direct Image URL Accessible: ${urlTest.status}`);
            console.log(`   📏 Content Length: ${urlTest.headers['content-length'] || 'Unknown'} bytes`);
            console.log(`   🎨 Content Type: ${urlTest.headers['content-type'] || 'Unknown'}`);
          } catch (error) {
            console.log(`   ❌ URL Not Accessible: ${error.response?.status || error.message}`);
          }
        }
      }

      // Summary
      const postsWithImages = data.posts.filter(post => post.media?.hasMedia).length;
      const directImageUrls = data.posts.filter(post => post.media?.url?.includes('fbcdn.net')).length;
      
      console.log('\n📊 Image Extraction Summary:');
      console.log(`   Total Posts: ${data.totalPosts}`);
      console.log(`   Posts with Media: ${postsWithImages}/${data.totalPosts}`);
      console.log(`   Direct Image URLs: ${directImageUrls}/${data.totalPosts}`);
      console.log(`   Success Rate: ${Math.round((directImageUrls / data.totalPosts) * 100)}%`);

      if (directImageUrls === data.totalPosts) {
        console.log('\n🎉 SUCCESS: All posts now have direct image URLs!');
        console.log('   Images should now display properly in the frontend!');
      } else {
        console.log('\n⚠️  Some posts still missing direct image URLs');
      }

    } else {
      console.log('❌ Request failed:', response.data.error);
    }

  } catch (error) {
    console.error('❌ Test Error:', error.response?.data || error.message);
  }

  console.log('\n🎯 NEXT STEPS:');
  console.log('1. Open http://localhost:3000');
  console.log('2. Navigate to Business Profile → Social Media');
  console.log('3. Select Facebook and enter: https://www.facebook.com/PayaFinance/');
  console.log('4. Click "Pull Posts" to test the fixed image extraction');
  console.log('5. Images should now display properly in the UI!');
}

testFixedImages().catch(console.error);
