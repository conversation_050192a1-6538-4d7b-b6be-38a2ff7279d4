const Joi = require('joi');

// Common validation schemas
const commonSchemas = {
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required(),
  businessName: Joi.string().min(2).max(255).required(),
  businessType: Joi.string().min(2).max(100).required(),
  location: Joi.string().min(2).max(255).required(),
  url: Joi.string().uri().optional().allow(''),
  date: Joi.date().required(),
  platform: Joi.string().valid('facebook', 'instagram', 'linkedin', 'twitter').required(),
  uuid: Joi.string().uuid().required()
};

// User registration validation
const userRegistrationSchema = Joi.object({
  email: commonSchemas.email,
  password: commonSchemas.password,
  businessName: commonSchemas.businessName,
  businessType: commonSchemas.businessType,
  location: commonSchemas.location,
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(50).optional(),
  zipCode: Joi.string().max(20).optional()
});

// User login validation
const userLoginSchema = Joi.object({
  email: commonSchemas.email,
  password: Joi.string().required()
});

// Content generation validation
const contentGenerationSchema = Joi.object({
  startDate: commonSchemas.date,
  endDate: Joi.date().min(Joi.ref('startDate')).required(),
  platforms: Joi.array().items(commonSchemas.platform).min(1).required(),
  contentTypes: Joi.array().items(Joi.string()).optional(),
  regenerate: Joi.boolean().default(false)
});

// Post update validation
const postUpdateSchema = Joi.object({
  contentText: Joi.string().max(2000).optional(),
  hashtags: Joi.array().items(Joi.string().pattern(/^#/)).optional(),
  status: Joi.string().valid('generated', 'edited', 'approved', 'posted').optional()
});

// Profile update validation
const profileUpdateSchema = Joi.object({
  businessName: Joi.string().min(2).max(255).optional(),
  businessType: Joi.string().min(2).max(100).optional(),
  location: Joi.string().min(2).max(255).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(50).optional(),
  zipCode: Joi.string().max(20).optional()
});

// Business profile validation
const businessProfileSchema = Joi.object({
  websiteUrl: commonSchemas.url,
  socialHandles: Joi.object().optional(),
  hoursOperation: Joi.object().optional(),
  services: Joi.array().items(Joi.string()).optional(),
  brandColors: Joi.object().optional(),
  visualStyle: Joi.object().optional(),
  writingStyle: Joi.object().optional(),
  targetAudience: Joi.string().max(500).optional().allow(''),
  uniqueSellingPoints: Joi.array().items(Joi.string()).optional()
});

// Social media analysis validation
const socialAnalysisSchema = Joi.object({
  socialUrl: commonSchemas.url.required(),
  platform: commonSchemas.platform
});

// Subscription validation
const subscriptionSchema = Joi.object({
  planId: Joi.string().valid('starter', 'professional', 'agency').required(),
  paymentMethodId: Joi.string().optional()
});

// Validation middleware factory
const validateRequest = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        error: 'Validation failed',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    
    req.validatedData = value;
    next();
  };
};

// Validation helper functions
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validatePassword = (password) => {
  // At least 8 characters, 1 uppercase, 1 lowercase, 1 number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;
  return passwordRegex.test(password);
};

const validateUrl = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  // Remove potentially dangerous characters
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim();
};

const sanitizeObject = (obj) => {
  if (typeof obj !== 'object' || obj === null) return obj;
  
  const sanitized = {};
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value);
    } else if (typeof value === 'object') {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  return sanitized;
};

// Rate limiting helpers
const createRateLimitKey = (req, identifier = 'ip') => {
  switch (identifier) {
    case 'user':
      return `user:${req.userId}`;
    case 'email':
      return `email:${req.body.email}`;
    default:
      return `ip:${req.ip}`;
  }
};

// Error response helpers
const createErrorResponse = (message, code = 'VALIDATION_ERROR', details = null) => {
  const error = {
    error: message,
    code,
    timestamp: new Date().toISOString()
  };
  
  if (details) {
    error.details = details;
  }
  
  return error;
};

const createSuccessResponse = (data, message = 'Success') => {
  return {
    success: true,
    message,
    data,
    timestamp: new Date().toISOString()
  };
};

module.exports = {
  // Schemas
  userRegistrationSchema,
  userLoginSchema,
  contentGenerationSchema,
  postUpdateSchema,
  profileUpdateSchema,
  businessProfileSchema,
  socialAnalysisSchema,
  subscriptionSchema,
  
  // Middleware
  validateRequest,
  
  // Helpers
  validateEmail,
  validatePassword,
  validateUrl,
  sanitizeInput,
  sanitizeObject,
  createRateLimitKey,
  createErrorResponse,
  createSuccessResponse,
  
  // Common schemas for reuse
  commonSchemas
};
