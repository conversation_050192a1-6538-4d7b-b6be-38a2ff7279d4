import React from 'react';
import { HelpCircle, Mail, MessageCircle, Book, Video } from 'lucide-react';

const Support = () => {
  const supportOptions = [
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Get instant help from our support team',
      action: 'Start Chat',
      available: true
    },
    {
      icon: Mail,
      title: 'Email Support',
      description: 'Send us a detailed message and we\'ll get back to you',
      action: 'Send Email',
      available: true
    },
    {
      icon: Book,
      title: 'Knowledge Base',
      description: 'Browse our comprehensive help articles',
      action: 'Browse Articles',
      available: true
    },
    {
      icon: Video,
      title: 'Video Tutorials',
      description: 'Watch step-by-step guides and tutorials',
      action: 'Watch Videos',
      available: true
    }
  ];

  const faqs = [
    {
      question: 'How do I generate content for my business?',
      answer: 'Navigate to the Content Calendar and click "Generate Posts". Our AI will create personalized content based on your business profile and local events.'
    },
    {
      question: 'Can I customize the generated content?',
      answer: 'Yes! All generated content can be edited before posting. You can modify text, hashtags, and scheduling times to match your preferences.'
    },
    {
      question: 'How does the weather integration work?',
      answer: 'Our AI automatically incorporates local weather conditions into your content suggestions, making posts more relevant and engaging for your audience.'
    },
    {
      question: 'What social media platforms are supported?',
      answer: 'We currently support Facebook, Instagram, LinkedIn, and Twitter, with content optimized for each platform\'s unique requirements.'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Help & Support</h1>
        <p className="text-gray-600">Get the help you need to make the most of LocalPost.ai</p>
      </div>

      {/* Support Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {supportOptions.map((option, index) => {
          const Icon = option.icon;
          return (
            <div key={index} className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-start space-x-4">
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Icon size={24} className="text-primary-600" />
                </div>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{option.title}</h3>
                  <p className="text-gray-600 mb-4">{option.description}</p>
                  <button className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm">
                    {option.action}
                  </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* FAQ Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Frequently Asked Questions</h2>
        </div>
        
        <div className="divide-y divide-gray-200">
          {faqs.map((faq, index) => (
            <div key={index} className="p-6">
              <div className="flex items-start space-x-3">
                <HelpCircle size={20} className="text-primary-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-gray-900 mb-2">{faq.question}</h3>
                  <p className="text-sm text-gray-600">{faq.answer}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Contact Info */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Still need help?</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <p className="font-medium text-gray-900">Email Support</p>
            <p className="text-gray-600"><EMAIL></p>
            <p className="text-gray-500">Response within 24 hours</p>
          </div>
          <div>
            <p className="font-medium text-gray-900">Business Hours</p>
            <p className="text-gray-600">Monday - Friday: 9 AM - 6 PM EST</p>
            <p className="text-gray-500">Weekend support available</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Support;
