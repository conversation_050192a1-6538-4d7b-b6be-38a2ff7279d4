const axios = require('axios');

async function testBackendConnection() {
  console.log('🔍 Testing Backend Connection\n');

  const baseURL = 'http://localhost:5000';

  try {
    // Test 1: Basic server health check
    console.log('🧪 Test 1: Basic server health check...');
    try {
      const healthResponse = await axios.get(`${baseURL}/`);
      console.log('✅ Server is responding');
      console.log(`   Status: ${healthResponse.status}`);
      console.log(`   Response: ${healthResponse.data}`);
    } catch (error) {
      console.log('❌ Server health check failed:', error.message);
    }

    // Test 2: Test the exact API endpoint the frontend calls
    console.log('\n🧪 Test 2: Testing social media API endpoint...');
    try {
      const apiResponse = await axios.post(`${baseURL}/api/social-analysis/scrape-posts`, {
        platform: 'Facebook',
        url: 'https://www.facebook.com/PayaFinance/',
        options: { limit: 10 }
      }, {
        headers: {
          'Content-Type': 'application/json',
        }
      });

      console.log('✅ API endpoint is working');
      console.log(`   Status: ${apiResponse.status}`);
      console.log(`   Posts returned: ${apiResponse.data.data?.totalPosts || 0}`);
      console.log(`   Scraping method: ${apiResponse.data.data?.scrapedWith || 'Unknown'}`);
    } catch (error) {
      console.log('❌ API endpoint failed:', error.response?.status || error.message);
      if (error.response?.data) {
        console.log('   Error details:', error.response.data);
      }
    }

    // Test 3: Test CORS headers
    console.log('\n🧪 Test 3: Testing CORS configuration...');
    try {
      const corsResponse = await axios.options(`${baseURL}/api/social-analysis/scrape-posts`, {
        headers: {
          'Origin': 'http://localhost:3001',
          'Access-Control-Request-Method': 'POST',
          'Access-Control-Request-Headers': 'Content-Type'
        }
      });

      console.log('✅ CORS preflight successful');
      console.log(`   Status: ${corsResponse.status}`);
      console.log(`   CORS headers:`, corsResponse.headers['access-control-allow-origin']);
    } catch (error) {
      console.log('⚠️  CORS preflight info:', error.response?.status || error.message);
    }

  } catch (error) {
    console.error('❌ Connection test error:', error.message);
  }

  console.log('\n🎯 DIAGNOSIS:');
  console.log('If all tests pass, the backend is working correctly.');
  console.log('If tests fail, there might be:');
  console.log('1. Backend server not running properly');
  console.log('2. Port conflicts');
  console.log('3. CORS configuration issues');
  console.log('4. Network/firewall issues');

  console.log('\n💡 SOLUTIONS:');
  console.log('1. Make sure backend is running on port 5000');
  console.log('2. Check if frontend proxy is working');
  console.log('3. Try accessing http://localhost:3001 (new frontend port)');
  console.log('4. Clear browser cache and try again');
}

testBackendConnection().catch(console.error);
