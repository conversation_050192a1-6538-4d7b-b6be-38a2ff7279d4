// Clear require cache to force fresh load
delete require.cache[require.resolve('./config/database')];
delete require.cache[require.resolve('dotenv')];

// Load environment variables fresh
require('dotenv').config();

console.log('🔍 Testing Database Configuration...\n');

console.log('1. Environment Variables (fresh load):');
console.log('   SUPABASE_URL:', process.env.SUPABASE_URL || 'NOT SET');
console.log('   SUPABASE_ANON_KEY length:', process.env.SUPABASE_ANON_KEY?.length || 0);

// Now load the database config
console.log('\n2. Loading database configuration:');
try {
  const { supabase } = require('./config/database');
  console.log('✅ Database config loaded');
  
  // Test authentication
  console.log('\n3. Testing Supabase authentication:');
  supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'qwerty123456'
  })
  .then(({ data, error }) => {
    if (error) {
      console.log('❌ Authentication failed:', error.message);
    } else {
      console.log('✅ Authentication successful!');
      console.log('   User:', data.user?.email);
      console.log('   Session exists:', !!data.session);
    }
  })
  .catch(error => {
    console.log('❌ Authentication error:', error.message);
  });
  
} catch (error) {
  console.log('❌ Failed to load database config:', error.message);
}
