require('dotenv').config();
const { supabase } = require('./config/database');

async function testAPIIntegration() {
  console.log('🚀 Testing Full API Integration...\n');

  try {
    // Test 1: Check if tables exist
    console.log('1️⃣ Testing database tables...');
    
    const tables = ['users', 'business_profiles', 'generated_posts'];
    for (const table of tables) {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);
      
      if (error) {
        console.log(`❌ Table '${table}' error:`, error.message);
        if (error.message.includes('does not exist')) {
          console.log('📋 Database schema needs to be created');
          console.log('👉 Please run the SQL from ../database/schema.sql in your Supabase dashboard');
          return;
        }
      } else {
        console.log(`✅ Table '${table}' exists and accessible`);
      }
    }

    // Test 2: Create a test user
    console.log('\n2️⃣ Testing user creation...');
    
    const testUser = {
      id: '550e8400-e29b-41d4-a716-************', // UUID for testing
      email: '<EMAIL>',
      business_name: 'Test Local Business',
      business_type: 'restaurant',
      location: 'New York, NY',
      city: 'New York',
      state: 'NY',
      zip_code: '10001'
    };

    // First, try to delete existing test user
    await supabase.from('users').delete().eq('email', testUser.email);

    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert(testUser)
      .select()
      .single();

    if (userError) {
      console.log('❌ User creation failed:', userError.message);
    } else {
      console.log('✅ Test user created successfully');
      console.log('📧 Email:', userData.email);
      console.log('🏢 Business:', userData.business_name);
    }

    // Test 3: Create business profile
    console.log('\n3️⃣ Testing business profile creation...');
    
    const businessProfile = {
      user_id: testUser.id,
      website_url: 'https://testbusiness.com',
      brand_colors: { primary: '#FF6B35', secondary: '#004E89' },
      target_audience: 'Local food enthusiasts and families'
    };

    const { data: profileData, error: profileError } = await supabase
      .from('business_profiles')
      .insert(businessProfile)
      .select()
      .single();

    if (profileError) {
      console.log('❌ Business profile creation failed:', profileError.message);
    } else {
      console.log('✅ Business profile created successfully');
      console.log('🌐 Website:', profileData.website_url);
    }

    // Test 4: Create sample posts
    console.log('\n4️⃣ Testing post creation...');
    
    const samplePosts = [
      {
        user_id: testUser.id,
        date_scheduled: '2024-01-15',
        platform: 'facebook',
        content_text: 'Beautiful sunny day in New York! Perfect weather to try our new summer menu. 🌞🥗',
        hashtags: ['#LocalEats', '#NYC', '#SummerMenu'],
        status: 'generated'
      },
      {
        user_id: testUser.id,
        date_scheduled: '2024-01-16',
        platform: 'instagram',
        content_text: 'Tomorrow looks like another great day! Come join us for our special brunch menu. 🥞☕',
        hashtags: ['#Brunch', '#LocalFood', '#NYC'],
        status: 'generated'
      }
    ];

    for (const post of samplePosts) {
      const { data: postData, error: postError } = await supabase
        .from('generated_posts')
        .insert(post)
        .select()
        .single();

      if (postError) {
        console.log(`❌ Post creation failed for ${post.platform}:`, postError.message);
      } else {
        console.log(`✅ ${post.platform} post created for ${post.date_scheduled}`);
      }
    }

    // Test 5: Query posts
    console.log('\n5️⃣ Testing post retrieval...');
    
    const { data: posts, error: queryError } = await supabase
      .from('generated_posts')
      .select('*')
      .eq('user_id', testUser.id)
      .order('date_scheduled', { ascending: true });

    if (queryError) {
      console.log('❌ Post query failed:', queryError.message);
    } else {
      console.log(`✅ Retrieved ${posts.length} posts`);
      posts.forEach(post => {
        console.log(`   📅 ${post.date_scheduled} - ${post.platform}: ${post.content_text.substring(0, 50)}...`);
      });
    }

    console.log('\n🎉 API Integration Test Complete!');
    console.log('✅ Database is fully functional and ready for real data');
    
    // Cleanup
    console.log('\n🧹 Cleaning up test data...');
    await supabase.from('users').delete().eq('email', testUser.email);
    console.log('✅ Test data cleaned up');

  } catch (error) {
    console.log('❌ Integration test failed:', error.message);
  }
}

testAPIIntegration();
