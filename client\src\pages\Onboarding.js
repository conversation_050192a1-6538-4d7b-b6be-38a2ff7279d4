import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  CheckCircle, 
  ArrowRight, 
  ArrowLeft,
  Instagram,
  Facebook,
  Globe,
  Palette,
  Users,
  Target
} from 'lucide-react';
import toast from 'react-hot-toast';

const Onboarding = () => {
  const { user, apiCall } = useAuth();
  const navigate = useNavigate();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    // Step 1: Brand Discovery Method
    discoveryMethod: '',
    
    // Step 2: Social Media Analysis
    socialUrl: '',
    socialPlatform: 'instagram',
    
    // Step 3: Business Details
    websiteUrl: '',
    services: [],
    targetAudience: '',
    uniqueSellingPoints: [],
    
    // Step 4: Brand Preferences
    brandColors: [],
    visualStyle: '',
    writingTone: ''
  });

  const totalSteps = 4;

  const discoveryMethods = [
    {
      id: 'social',
      title: 'Analyze Social Media',
      description: 'Let us analyze your existing social media to learn your brand voice',
      icon: Instagram,
      recommended: true
    },
    {
      id: 'website',
      title: 'Analyze Website',
      description: 'We\'ll extract your brand information from your website',
      icon: Globe
    },
    {
      id: 'manual',
      title: 'Manual Setup',
      description: 'Manually configure your brand preferences and style',
      icon: Palette
    }
  ];

  const businessServices = [
    'Food & Dining', 'Health & Wellness', 'Beauty & Personal Care',
    'Professional Services', 'Home Services', 'Retail & Shopping',
    'Fitness & Recreation', 'Education & Training', 'Real Estate',
    'Automotive', 'Technology', 'Other'
  ];

  const visualStyles = [
    'Modern & Clean', 'Warm & Friendly', 'Professional & Corporate',
    'Creative & Artistic', 'Bold & Energetic', 'Elegant & Sophisticated'
  ];

  const writingTones = [
    'Professional', 'Friendly & Casual', 'Enthusiastic', 
    'Informative', 'Conversational', 'Authoritative'
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayInput = (field, value) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setFormData(prev => ({
      ...prev,
      [field]: items
    }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSocialAnalysis = async () => {
    if (!formData.socialUrl) {
      toast.error('Please enter your social media URL');
      return;
    }

    try {
      setLoading(true);
      
      const response = await apiCall('/api/profile/analyze-social', {
        method: 'POST',
        body: JSON.stringify({
          socialUrl: formData.socialUrl,
          platform: formData.socialPlatform
        })
      });

      if (response.ok) {
        const data = await response.json();
        toast.success('Social media analysis completed!');
        
        // Auto-fill some fields based on analysis
        const analysis = data.analysis.brandVoice;
        setFormData(prev => ({
          ...prev,
          writingTone: analysis.tone || '',
          visualStyle: analysis.personality_traits?.[0] || ''
        }));
        
        nextStep();
      } else {
        toast.error('Failed to analyze social media');
      }
    } catch (error) {
      console.error('Social analysis error:', error);
      toast.error('Failed to analyze social media');
    } finally {
      setLoading(false);
    }
  };

  const completeOnboarding = async () => {
    try {
      setLoading(true);

      // Update business profile
      const response = await apiCall('/api/profile/business', {
        method: 'PUT',
        body: JSON.stringify({
          websiteUrl: formData.websiteUrl,
          services: formData.services,
          targetAudience: formData.targetAudience,
          uniqueSellingPoints: formData.uniqueSellingPoints,
          brandColors: { primary: formData.brandColors },
          visualStyle: { style: formData.visualStyle },
          writingStyle: { tone: formData.writingTone }
        })
      });

      if (response.ok) {
        toast.success('Onboarding completed! Welcome to LocalPost.ai');
        navigate('/dashboard');
      } else {
        toast.error('Failed to save profile');
      }
    } catch (error) {
      console.error('Complete onboarding error:', error);
      toast.error('Failed to complete onboarding');
    } finally {
      setLoading(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                How would you like to set up your brand?
              </h2>
              <p className="text-gray-600">
                Choose the method that works best for you
              </p>
            </div>

            <div className="grid gap-4">
              {discoveryMethods.map((method) => {
                const Icon = method.icon;
                return (
                  <div
                    key={method.id}
                    onClick={() => handleInputChange('discoveryMethod', method.id)}
                    className={`relative p-6 border-2 rounded-lg cursor-pointer transition-all ${
                      formData.discoveryMethod === method.id
                        ? 'border-primary-500 bg-primary-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    {method.recommended && (
                      <div className="absolute -top-2 -right-2">
                        <span className="bg-primary-600 text-white text-xs px-2 py-1 rounded-full">
                          Recommended
                        </span>
                      </div>
                    )}
                    
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                        formData.discoveryMethod === method.id
                          ? 'bg-primary-600 text-white'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        <Icon size={24} />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {method.title}
                        </h3>
                        <p className="text-gray-600">
                          {method.description}
                        </p>
                      </div>
                      
                      {formData.discoveryMethod === method.id && (
                        <CheckCircle className="text-primary-600" size={24} />
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );

      case 2:
        if (formData.discoveryMethod === 'social') {
          return (
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Connect Your Social Media
                </h2>
                <p className="text-gray-600">
                  We'll analyze your posts to understand your brand voice
                </p>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="label">Platform</label>
                  <select
                    value={formData.socialPlatform}
                    onChange={(e) => handleInputChange('socialPlatform', e.target.value)}
                    className="input-field"
                  >
                    <option value="instagram">Instagram</option>
                    <option value="facebook">Facebook</option>
                    <option value="twitter">Twitter</option>
                    <option value="linkedin">LinkedIn</option>
                  </select>
                </div>

                <div>
                  <label className="label">Social Media URL</label>
                  <input
                    type="url"
                    value={formData.socialUrl}
                    onChange={(e) => handleInputChange('socialUrl', e.target.value)}
                    className="input-field"
                    placeholder="https://instagram.com/yourbusiness"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Enter the URL to your business social media profile
                  </p>
                </div>

                <button
                  onClick={handleSocialAnalysis}
                  disabled={loading || !formData.socialUrl}
                  className="w-full btn-primary py-3 disabled:opacity-50"
                >
                  {loading ? 'Analyzing...' : 'Analyze Social Media'}
                </button>
              </div>
            </div>
          );
        } else {
          // Skip to next step for other methods
          nextStep();
          return null;
        }

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Tell us about your business
              </h2>
              <p className="text-gray-600">
                Help us create more relevant content for your audience
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="label">Website URL (Optional)</label>
                <input
                  type="url"
                  value={formData.websiteUrl}
                  onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
                  className="input-field"
                  placeholder="https://yourbusiness.com"
                />
              </div>

              <div>
                <label className="label">Services/Products</label>
                <div className="grid grid-cols-2 gap-2 mb-2">
                  {businessServices.map((service) => (
                    <label key={service} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={formData.services.includes(service)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            handleInputChange('services', [...formData.services, service]);
                          } else {
                            handleInputChange('services', formData.services.filter(s => s !== service));
                          }
                        }}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="text-sm text-gray-700">{service}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="label">Target Audience</label>
                <textarea
                  value={formData.targetAudience}
                  onChange={(e) => handleInputChange('targetAudience', e.target.value)}
                  className="input-field h-20 resize-none"
                  placeholder="Describe your ideal customers (e.g., local families, young professionals, seniors...)"
                />
              </div>

              <div>
                <label className="label">What makes you unique?</label>
                <input
                  type="text"
                  value={formData.uniqueSellingPoints.join(', ')}
                  onChange={(e) => handleArrayInput('uniqueSellingPoints', e.target.value)}
                  className="input-field"
                  placeholder="Enter unique selling points separated by commas"
                />
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">
                Define your brand style
              </h2>
              <p className="text-gray-600">
                These preferences will guide our AI in creating your content
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="label">Visual Style</label>
                <select
                  value={formData.visualStyle}
                  onChange={(e) => handleInputChange('visualStyle', e.target.value)}
                  className="input-field"
                >
                  <option value="">Select a visual style</option>
                  {visualStyles.map((style) => (
                    <option key={style} value={style}>{style}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="label">Writing Tone</label>
                <select
                  value={formData.writingTone}
                  onChange={(e) => handleInputChange('writingTone', e.target.value)}
                  className="input-field"
                >
                  <option value="">Select a writing tone</option>
                  {writingTones.map((tone) => (
                    <option key={tone} value={tone}>{tone}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="label">Brand Colors (Optional)</label>
                <input
                  type="text"
                  value={formData.brandColors.join(', ')}
                  onChange={(e) => handleArrayInput('brandColors', e.target.value)}
                  className="input-field"
                  placeholder="Enter colors separated by commas (e.g., blue, white, gold)"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Progress Bar */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-600">
              Step {currentStep} of {totalSteps}
            </span>
            <span className="text-sm text-gray-500">
              Welcome, {user?.businessName}!
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-primary-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(currentStep / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className="card">
          {renderStep()}
        </div>

        {/* Navigation */}
        <div className="flex items-center justify-between mt-8">
          <button
            onClick={prevStep}
            disabled={currentStep === 1}
            className="btn-secondary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowLeft size={16} />
            <span>Previous</span>
          </button>

          {currentStep === totalSteps ? (
            <button
              onClick={completeOnboarding}
              disabled={loading}
              className="btn-primary flex items-center space-x-2 disabled:opacity-50"
            >
              <span>{loading ? 'Completing...' : 'Complete Setup'}</span>
              <CheckCircle size={16} />
            </button>
          ) : (
            <button
              onClick={nextStep}
              disabled={
                (currentStep === 1 && !formData.discoveryMethod) ||
                (currentStep === 2 && formData.discoveryMethod === 'social' && !formData.socialUrl)
              }
              className="btn-primary flex items-center space-x-2 disabled:opacity-50"
            >
              <span>Next</span>
              <ArrowRight size={16} />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Onboarding;
