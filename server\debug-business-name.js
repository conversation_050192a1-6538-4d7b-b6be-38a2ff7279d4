const axios = require('axios');
const { JSDOM } = require('jsdom');

async function debugBusinessNameExtraction() {
  console.log('🔍 Debugging Business Name Extraction\n');

  const testUrls = [
    'https://www.starbucks.com',
    'https://www.microsoft.com',
    'https://www.apple.com',
    'https://example.com'
  ];

  for (const url of testUrls) {
    console.log(`\n🌐 Analyzing: ${url}`);
    console.log('─'.repeat(60));

    try {
      // Fetch the website content
      const response = await axios.get(url, {
        timeout: 15000,
        maxContentLength: 500000,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      const html = response.data;
      const dom = new JSDOM(html);
      const document = dom.window.document;

      // Extract various elements that could contain business name
      console.log('📊 Available Name Sources:');
      
      // Title tag
      const title = document.querySelector('title');
      console.log(`   📄 Title: "${title ? title.textContent.trim() : 'None'}"`);
      
      // Meta tags
      const ogTitle = document.querySelector('meta[property="og:title"]');
      console.log(`   🏷️  OG Title: "${ogTitle ? ogTitle.getAttribute('content') : 'None'}"`);
      
      const ogSiteName = document.querySelector('meta[property="og:site_name"]');
      console.log(`   🌐 OG Site Name: "${ogSiteName ? ogSiteName.getAttribute('content') : 'None'}"`);
      
      const twitterTitle = document.querySelector('meta[name="twitter:title"]');
      console.log(`   🐦 Twitter Title: "${twitterTitle ? twitterTitle.getAttribute('content') : 'None'}"`);
      
      // H1 tags
      const h1Elements = document.querySelectorAll('h1');
      console.log(`   📰 H1 Tags (${h1Elements.length} found):`);
      h1Elements.forEach((h1, index) => {
        if (index < 3) { // Show first 3
          console.log(`      ${index + 1}. "${h1.textContent.trim().substring(0, 50)}..."`);
        }
      });
      
      // Logo/brand related elements
      const logoSelectors = [
        '.logo', '.brand', '.site-title', '.site-name',
        '[class*="logo"]', '[class*="brand"]', '[class*="site"]',
        'header h1', 'nav .brand', '.navbar-brand'
      ];
      
      console.log(`   🎨 Logo/Brand Elements:`);
      logoSelectors.forEach(selector => {
        const element = document.querySelector(selector);
        if (element && element.textContent.trim()) {
          console.log(`      ${selector}: "${element.textContent.trim().substring(0, 40)}..."`);
        }
      });
      
      // Domain extraction
      const domain = new URL(url).hostname.replace('www.', '');
      const domainName = domain.split('.')[0];
      console.log(`   🌍 Domain Name: "${domainName}"`);
      
      // Current extraction result
      const websiteExtractionService = require('./services/websiteExtraction');
      const currentResult = websiteExtractionService.extractBusinessName(document, title?.textContent, url);
      console.log(`   🤖 Current Extraction: "${currentResult}"`);
      
      // Suggest better extraction
      let betterName = '';
      
      // Priority order for better extraction
      if (ogSiteName && ogSiteName.getAttribute('content').trim()) {
        betterName = ogSiteName.getAttribute('content').trim();
        console.log(`   ✨ Suggested (OG Site Name): "${betterName}"`);
      } else if (ogTitle && ogTitle.getAttribute('content').trim()) {
        const ogTitleText = ogTitle.getAttribute('content').trim();
        betterName = ogTitleText.split('|')[0].split('-')[0].split('–')[0].trim();
        console.log(`   ✨ Suggested (OG Title): "${betterName}"`);
      } else if (title && title.textContent.trim()) {
        const titleText = title.textContent.trim();
        betterName = titleText.split('|')[0].split('-')[0].split('–')[0].split(':')[0].trim();
        console.log(`   ✨ Suggested (Title): "${betterName}"`);
      } else {
        betterName = domainName.charAt(0).toUpperCase() + domainName.slice(1);
        console.log(`   ✨ Suggested (Domain): "${betterName}"`);
      }

    } catch (error) {
      console.log(`   ❌ Error: ${error.message}`);
    }
  }

  console.log('\n🎯 BUSINESS NAME EXTRACTION ISSUES IDENTIFIED:');
  console.log('❌ Limited selector coverage');
  console.log('❌ Missing meta tag extraction (og:site_name, og:title)');
  console.log('❌ Poor title parsing logic');
  console.log('❌ No priority system for name sources');
  console.log('❌ No cleaning of extracted names');
  
  console.log('\n💡 RECOMMENDED IMPROVEMENTS:');
  console.log('✅ Add meta tag extraction (og:site_name, og:title, twitter:title)');
  console.log('✅ Improve title parsing with multiple delimiters');
  console.log('✅ Add logo/brand element detection');
  console.log('✅ Implement priority system for name sources');
  console.log('✅ Add name cleaning and validation');
  console.log('✅ Better fallback logic');
}

debugBusinessNameExtraction().catch(console.error);
