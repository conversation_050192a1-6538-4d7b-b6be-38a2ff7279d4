#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function setupRealAPIs() {
  console.log('🚀 LocalPost.ai Real API Integration Setup\n');
  console.log('This script will help you configure real external APIs for:');
  console.log('• 🤖 OpenAI (GPT-4 + DALL-E) - AI content generation');
  console.log('• 🌤️  OpenWeatherMap - Weather-aware content');
  console.log('• 🎉 Eventbrite - Local events integration\n');

  const envPath = path.join(__dirname, 'server', '.env');
  
  // Read current .env file
  let envContent = '';
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }

  console.log('📋 Current API Configuration Status:');
  console.log('OpenAI:', envContent.includes('OPENAI_API_KEY=sk-') ? '✅ Configured' : '❌ Not configured');
  console.log('Weather:', envContent.includes('OPENWEATHER_API_KEY=') && !envContent.includes('your_openweather') ? '✅ Configured' : '❌ Not configured');
  console.log('Events:', envContent.includes('EVENTBRITE_API_KEY=') && !envContent.includes('your_eventbrite') ? '✅ Configured' : '❌ Not configured');
  console.log('');

  const setupChoice = await question('Would you like to:\n1. Set up API keys interactively\n2. View setup instructions only\n3. Test current configuration\nEnter choice (1-3): ');

  if (setupChoice === '2') {
    showSetupInstructions();
    rl.close();
    return;
  }

  if (setupChoice === '3') {
    console.log('\n🧪 Testing current API configuration...');
    rl.close();
    
    // Run the test script
    const { spawn } = require('child_process');
    const testProcess = spawn('node', ['server/test-external-apis.js'], { stdio: 'inherit' });
    return;
  }

  if (setupChoice !== '1') {
    console.log('Invalid choice. Exiting.');
    rl.close();
    return;
  }

  console.log('\n🔧 Interactive API Setup\n');

  // OpenAI Setup
  console.log('🤖 OpenAI API Setup (REQUIRED)');
  console.log('OpenAI powers the AI content generation and image creation.');
  console.log('Cost: ~$0.002-0.06 per post, ~$0.04 per image');
  
  const setupOpenAI = await question('Set up OpenAI API? (y/n): ');
  let openaiKey = '';
  
  if (setupOpenAI.toLowerCase() === 'y') {
    console.log('\n📝 To get your OpenAI API key:');
    console.log('1. Go to https://platform.openai.com/');
    console.log('2. Sign up or log in');
    console.log('3. Go to API Keys section');
    console.log('4. Create new secret key');
    console.log('5. Copy the key (starts with sk-)');
    
    openaiKey = await question('\nEnter your OpenAI API key (or press Enter to skip): ');
    
    if (openaiKey && !openaiKey.startsWith('sk-')) {
      console.log('⚠️  Warning: OpenAI keys should start with "sk-"');
      const confirm = await question('Continue anyway? (y/n): ');
      if (confirm.toLowerCase() !== 'y') {
        openaiKey = '';
      }
    }
  }

  // Weather API Setup
  console.log('\n🌤️  Weather API Setup (RECOMMENDED)');
  console.log('Weather API enables weather-aware content generation.');
  console.log('Cost: Free tier (1000 calls/day)');
  
  const setupWeather = await question('Set up Weather API? (y/n): ');
  let weatherKey = '';
  
  if (setupWeather.toLowerCase() === 'y') {
    console.log('\n📝 To get your OpenWeatherMap API key:');
    console.log('1. Go to https://openweathermap.org/api');
    console.log('2. Sign up for free account');
    console.log('3. Go to API Keys section');
    console.log('4. Copy your default API key');
    
    weatherKey = await question('\nEnter your OpenWeatherMap API key (or press Enter to skip): ');
  }

  // Events API Setup
  console.log('\n🎉 Events API Setup (OPTIONAL)');
  console.log('Events API enables local community event integration.');
  console.log('Cost: Free for public events');
  
  const setupEvents = await question('Set up Events API? (y/n): ');
  let eventsKey = '';
  
  if (setupEvents.toLowerCase() === 'y') {
    console.log('\n📝 To get your Eventbrite API key:');
    console.log('1. Go to https://www.eventbrite.com/platform/api');
    console.log('2. Create developer account');
    console.log('3. Create new app');
    console.log('4. Copy your API key');
    
    eventsKey = await question('\nEnter your Eventbrite API key (or press Enter to skip): ');
  }

  // Update .env file
  console.log('\n💾 Updating configuration...');
  
  let newEnvContent = envContent;
  
  if (openaiKey) {
    newEnvContent = newEnvContent.replace(
      /OPENAI_API_KEY=.*/,
      `OPENAI_API_KEY=${openaiKey}`
    );
    console.log('✅ OpenAI API key updated');
  }
  
  if (weatherKey) {
    newEnvContent = newEnvContent.replace(
      /OPENWEATHER_API_KEY=.*/,
      `OPENWEATHER_API_KEY=${weatherKey}`
    );
    console.log('✅ Weather API key updated');
  }
  
  if (eventsKey) {
    newEnvContent = newEnvContent.replace(
      /EVENTBRITE_API_KEY=.*/,
      `EVENTBRITE_API_KEY=${eventsKey}`
    );
    console.log('✅ Events API key updated');
  }

  // Write updated .env file
  fs.writeFileSync(envPath, newEnvContent);
  
  console.log('\n🎉 Configuration updated successfully!');
  
  // Test the configuration
  const testNow = await question('\nWould you like to test the API configuration now? (y/n): ');
  
  if (testNow.toLowerCase() === 'y') {
    console.log('\n🧪 Testing API configuration...');
    rl.close();
    
    const { spawn } = require('child_process');
    const testProcess = spawn('node', ['server/test-external-apis.js'], { stdio: 'inherit' });
  } else {
    console.log('\n✅ Setup complete! To test your configuration later, run:');
    console.log('   node server/test-external-apis.js');
    console.log('\n🔄 Restart your server to use the new API keys:');
    console.log('   npm run server:dev');
    rl.close();
  }
}

function showSetupInstructions() {
  console.log('\n📖 API Setup Instructions\n');
  
  console.log('🤖 OpenAI API (REQUIRED):');
  console.log('1. Visit: https://platform.openai.com/');
  console.log('2. Create account and add billing method');
  console.log('3. Generate API key in API Keys section');
  console.log('4. Add to server/.env: OPENAI_API_KEY=sk-your-key-here');
  console.log('');
  
  console.log('🌤️  OpenWeatherMap API (RECOMMENDED):');
  console.log('1. Visit: https://openweathermap.org/api');
  console.log('2. Sign up for free account');
  console.log('3. Get API key from dashboard');
  console.log('4. Add to server/.env: OPENWEATHER_API_KEY=your-key-here');
  console.log('');
  
  console.log('🎉 Eventbrite API (OPTIONAL):');
  console.log('1. Visit: https://www.eventbrite.com/platform/api');
  console.log('2. Create developer account');
  console.log('3. Create app and get API key');
  console.log('4. Add to server/.env: EVENTBRITE_API_KEY=your-key-here');
  console.log('');
  
  console.log('🧪 Test your setup:');
  console.log('   node server/test-external-apis.js');
  console.log('');
  
  console.log('🔄 Restart server after adding keys:');
  console.log('   npm run server:dev');
}

// Run the setup
setupRealAPIs().catch(console.error);
