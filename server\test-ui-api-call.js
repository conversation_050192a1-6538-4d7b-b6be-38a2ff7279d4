const axios = require('axios');

async function testUIApiCall() {
  console.log('🔍 Testing Exact UI API Call\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    console.log('🧪 Testing exact Facebook API call that UI makes...');
    console.log('📱 Platform: Facebook');
    console.log('🔗 URL: https://www.facebook.com/PayaFinance/');
    console.log('📊 Limit: 25 (same as UI)');
    
    // This is the exact call the UI makes
    const response = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      options: {
        limit: 25 // Same as UI
      }
    }, {
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ UI API Call Results:');
      console.log(`   📊 Total Posts: ${data.totalPosts}`);
      console.log(`   🔧 Scraping Method: ${data.scrapedWith}`);
      console.log(`   📈 Analysis: ${data.analysis ? 'Available' : 'None'}`);
      console.log(`   🎭 Is Demo: ${data.isDemo || 'Not specified'}`);
      
      console.log('\n📝 First 4 Posts (what UI shows):');
      data.posts.slice(0, 4).forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      Content: "${post.content.substring(0, 80)}..."`);
        console.log(`      Engagement: 👍 ${post.engagement.likes} | 💬 ${post.engagement.comments} | 🔄 ${post.engagement.shares}`);
        console.log(`      Performance: ${post.performance}`);
        console.log(`      Scraped With: ${post.scrapedWith || 'Not specified'}`);
        console.log(`      Has Media: ${post.media?.hasMedia ? 'Yes' : 'No'}`);
        console.log(`      Media Type: ${post.media?.type || 'None'}`);
        
        if (post.media?.url) {
          console.log(`      Image URL: ${post.media.url.substring(0, 80)}...`);
        }
        
        // Check if this looks like demo content
        const isDemoContent = post.content.includes('Excited to share our latest services') ||
                             post.content.includes('Behind the scenes at our business') ||
                             post.content.includes('Thank you to all our amazing customers');
        
        console.log(`      Looks Like Demo: ${isDemoContent ? 'YES ❌' : 'NO ✅'}`);
        
        // Check if this is real Paya content
        const isRealPaya = post.content.includes('Paya') ||
                          post.content.includes('Buy Now Pay Later') ||
                          post.content.includes('Financial Companion');
        
        console.log(`      Real Paya Content: ${isRealPaya ? 'YES ✅' : 'NO ❌'}`);
      });

      // Overall assessment
      const hasAnyDemoContent = data.posts.some(post => 
        post.content.includes('Excited to share our latest services') ||
        post.content.includes('Behind the scenes at our business') ||
        post.content.includes('Thank you to all our amazing customers')
      );

      const hasRealPayaContent = data.posts.some(post => 
        post.content.includes('Paya') ||
        post.content.includes('Buy Now Pay Later') ||
        post.content.includes('Financial Companion')
      );

      console.log('\n🎯 OVERALL ASSESSMENT:');
      if (hasRealPayaContent && !hasAnyDemoContent) {
        console.log('   ✅ PERFECT: All real Paya Finance content, no demo data');
      } else if (hasRealPayaContent && hasAnyDemoContent) {
        console.log('   ⚠️  MIXED: Some real Paya content + some demo content');
      } else if (hasAnyDemoContent && !hasRealPayaContent) {
        console.log('   ❌ PROBLEM: Only demo content, no real Paya data');
      } else {
        console.log('   ❓ UNCLEAR: Neither demo nor recognizable Paya content');
      }

    } else {
      console.log('❌ API call failed:', response.data.error);
    }

  } catch (error) {
    console.error('❌ Test Error:', error.response?.data || error.message);
  }

  console.log('\n💡 TROUBLESHOOTING STEPS:');
  console.log('1. If you see real Paya content here but demo in UI:');
  console.log('   → Clear browser cache and refresh');
  console.log('   → Check browser network tab for API responses');
  console.log('   → Restart frontend development server');
  
  console.log('\n2. If you see demo content here:');
  console.log('   → Check server logs for Apify errors');
  console.log('   → Verify Apify API token is working');
  console.log('   → Check if fallback logic is triggering incorrectly');
  
  console.log('\n3. If mixed content:');
  console.log('   → Some posts may be generic business content');
  console.log('   → Check if Apify is returning partial results');
}

testUIApiCall().catch(console.error);
