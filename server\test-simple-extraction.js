const axios = require('axios');

async function testSimpleExtraction() {
  console.log('🌐 Testing Simple Website Extraction\n');

  try {
    console.log('Testing with a simple website...');
    
    const response = await axios.post('http://localhost:5000/api/profile/extract-website', {
      url: 'https://example.com'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 15000
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Website extraction working!');
      console.log('\n📊 Extracted Data:');
      console.log(`   Business Name: ${data.businessName}`);
      console.log(`   Industry: ${data.industry}`);
      console.log(`   Description: ${data.description}`);
      console.log(`   Location: ${data.location || 'Not found'}`);
      console.log(`   Services: ${data.services?.length || 0} found`);
      console.log(`   Brand Colors: ${data.brandColors?.length || 0} found`);
      console.log(`   AI Enhanced: ${data.enhanced ? 'Yes' : 'No'}`);
      console.log(`   Website: ${data.website}`);
      
      console.log('\n🎉 SUCCESS! Website extraction is working correctly.');
      console.log('💡 You can now test it in the UI:');
      console.log('   1. Go to http://localhost:3000');
      console.log('   2. Navigate to Business Profile');
      console.log('   3. Click on "Website Extract" tab');
      console.log('   4. Enter a website URL and click "Extract Data"');
      
    } else {
      console.log('❌ Extraction failed:', response.data.error);
    }

  } catch (error) {
    console.log('❌ Test failed:', error.message);
    if (error.response?.data) {
      console.log('Error details:', error.response.data);
    }
  }
}

testSimpleExtraction();
