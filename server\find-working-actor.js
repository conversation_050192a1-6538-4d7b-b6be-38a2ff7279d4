const { ApifyClient } = require('apify-client');
require('dotenv').config();

async function findWorkingActor() {
  console.log('🔍 Finding Working Facebook Actors\n');

  const client = new ApifyClient({
    token: process.env.APIFY_API_TOKEN,
  });

  // List of potential Facebook actors to try
  const potentialActors = [
    'apify/facebook-posts-scraper',
    'apify/facebook-page-scraper',
    'drobnikj/facebook-page-scraper',
    'lukaskrivka/facebook-scraper',
    'apify/facebook-scraper',
    'zuzka/facebook-page-scraper'
  ];

  console.log('🧪 Testing Facebook Actors...\n');

  for (const actorId of potentialActors) {
    try {
      console.log(`Testing: ${actorId}`);
      
      // Try to get actor info
      const actor = await client.actor(actorId).get();
      
      if (actor) {
        console.log(`✅ FOUND: ${actorId}`);
        console.log(`   Name: ${actor.name}`);
        console.log(`   Description: ${actor.description?.substring(0, 100)}...`);
        console.log(`   Stats: ${actor.stats?.totalRuns || 0} total runs`);
        console.log(`   Modified: ${actor.modifiedAt}`);
        
        // Try a quick test run with minimal input
        console.log(`   🧪 Testing with minimal input...`);
        
        try {
          const run = await client.actor(actorId).call({
            startUrls: [{ url: 'https://www.facebook.com/Microsoft' }],
            maxPosts: 3
          }, {
            timeout: 60, // 1 minute timeout for test
            memory: 512
          });
          
          console.log(`   ✅ Test run successful! Status: ${run.status}`);
          
          if (run.status === 'SUCCEEDED') {
            const { items } = await client.dataset(run.defaultDatasetId).listItems();
            console.log(`   📊 Returned ${items.length} items`);
            
            if (items.length > 0) {
              console.log(`   🎉 WORKING ACTOR FOUND: ${actorId}`);
              console.log(`   Sample data keys:`, Object.keys(items[0]));
              break; // Found a working actor
            }
          }
          
        } catch (runError) {
          console.log(`   ❌ Test run failed: ${runError.message}`);
        }
        
      } else {
        console.log(`❌ Actor not found: ${actorId}`);
      }
      
    } catch (error) {
      console.log(`❌ Error with ${actorId}: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  console.log('🎯 RECOMMENDATION:');
  console.log('Use the first working actor found above in your apifyIntegration.js file');
}

findWorkingActor().catch(console.error);
