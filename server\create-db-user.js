require('dotenv').config();
const { supabaseAdmin } = require('./config/database');

async function createDbUser() {
  console.log('👤 Creating database user record\n');

  const userId = '550e8400-e29b-41d4-a716-446655440000';
  const demoEmail = '<EMAIL>';

  try {
    // Check if user already exists
    const { data: existingUser } = await supabaseAdmin
      .from('users')
      .select('*')
      .eq('id', userId)
      .single();

    if (existingUser) {
      console.log('✅ Database user already exists!');
      console.log(`   👤 ID: ${existingUser.id}`);
      console.log(`   📧 Email: ${existingUser.email}`);
      console.log(`   🏢 Business: ${existingUser.business_name}`);
      return existingUser;
    }

    // Create user record using admin client (bypasses RLS)
    console.log('1️⃣ Creating database user record...');
    
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .insert({
        id: userId,
        email: demoEmail,
        business_name: 'Demo Restaurant',
        business_type: 'restaurant',
        location: 'New York, NY',
        subscription_status: 'active',
        subscription_tier: 'starter',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (userError) {
      console.log('❌ User creation failed:', userError.message);
      return null;
    }

    console.log('✅ Database user created successfully!');
    console.log(`   👤 ID: ${userData.id}`);
    console.log(`   📧 Email: ${userData.email}`);
    console.log(`   🏢 Business: ${userData.business_name}`);

    // Create business profile
    console.log('\n2️⃣ Creating business profile...');
    
    const { data: businessData, error: businessError } = await supabaseAdmin
      .from('business_profiles')
      .insert({
        user_id: userId,
        business_description: 'A cozy restaurant serving delicious food in the heart of New York City.',
        target_audience: 'Local food lovers, families, young professionals',
        brand_voice: 'friendly, welcoming, professional',
        primary_color: '#3b82f6',
        secondary_color: '#1e40af',
        visual_style: {
          tone: 'warm and inviting',
          imagery: 'food photography, restaurant ambiance',
          colors: ['#3b82f6', '#1e40af', '#f59e0b']
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (businessError) {
      console.log('⚠️  Business profile creation failed:', businessError.message);
    } else {
      console.log('✅ Business profile created successfully!');
    }

    console.log('\n🎉 Database user setup complete!');
    return userData;

  } catch (error) {
    console.log('❌ Database user creation failed:', error.message);
    return null;
  }
}

// Run creation
createDbUser().then(user => {
  if (user) {
    console.log('\n🚀 Database user is ready for content generation!');
    console.log('✅ Foreign key constraint should be resolved');
  }
}).catch(console.error);
