const axios = require('axios');

async function testOAuthEndpoint() {
  try {
    console.log('🔍 Testing OAuth endpoint...');

    // Test 1: Check if OAuth endpoint exists (should return 401 without auth)
    console.log('\n1. Testing OAuth endpoint without authentication:');
    try {
      const response = await axios.get('http://localhost:5000/api/oauth/facebook/auth');
      console.log('❌ Unexpected success:', response.data);
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ OAuth endpoint exists and requires authentication (expected)');
        console.log('   Response:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.response?.data || error.message);
      }
    }

    // Test 2: Check if environment variables are loaded
    console.log('\n2. Checking environment variables:');
    console.log('   FACEBOOK_APP_ID:', process.env.FACEBOOK_APP_ID ? '✅ Set' : '❌ Missing');
    console.log('   FACEBOOK_APP_SECRET:', process.env.FACEBOOK_APP_SECRET ? '✅ Set' : '❌ Missing');
    console.log('   FACEBOOK_REDIRECT_URI:', process.env.FACEBOOK_REDIRECT_URI || '❌ Missing');

    // Test 3: Test OAuth service directly
    console.log('\n3. Testing OAuth service directly:');
    const oauthService = require('./services/oauthService');

    const testUserId = 'test-user-123';
    const facebookAuthUrl = oauthService.getFacebookAuthUrl(testUserId);

    console.log('✅ Generated Facebook Auth URL:');
    console.log('   URL:', facebookAuthUrl);
    console.log('   Contains App ID:', facebookAuthUrl.includes(process.env.FACEBOOK_APP_ID) ? '✅ Yes' : '❌ No');
    console.log('   Contains Redirect URI:', facebookAuthUrl.includes(process.env.FACEBOOK_REDIRECT_URI) ? '✅ Yes' : '❌ No');

    // Test 4: Check if server is responding
    console.log('\n4. Testing server health:');
    try {
      const healthResponse = await axios.get('http://localhost:5000/api/auth/health');
      console.log('✅ Server health check passed');
    } catch (error) {
      console.log('⚠️ Server health check failed, but server is running');
    }

    console.log('\n🎯 OAuth Debug Complete!');
    console.log('\nNext steps:');
    console.log('1. Make sure you are logged into LocalPost.ai first');
    console.log('2. Check browser console for errors when clicking Connect');
    console.log('3. Check for popup blockers');
    console.log('4. Verify Facebook app redirect URI matches:', process.env.FACEBOOK_REDIRECT_URI);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Load environment variables
require('dotenv').config();

testOAuthEndpoint();
