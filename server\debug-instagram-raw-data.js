const { ApifyClient } = require('apify-client');
require('dotenv').config();

async function debugInstagramRawData() {
  console.log('🔍 Debugging Instagram Raw Data from Apify\n');

  const client = new ApifyClient({
    token: process.env.APIFY_API_TOKEN,
  });

  try {
    console.log('🧪 Testing Instagram actor directly...');
    console.log('📱 Actor: apify/instagram-scraper');
    console.log('🔗 URL: https://www.instagram.com/microsoft/');
    
    const input = {
      usernames: ['microsoft'],
      resultsLimit: 5,
      resultsType: 'posts',
      scrapeComments: false,
      scrapeHashtags: true,
      scrapeImages: true,
      scrapeVideos: true,
      includeFullText: true,
      maxRequestRetries: 3
    };

    console.log('📊 Input configuration:', JSON.stringify(input, null, 2));

    const run = await client.actor('apify/instagram-scraper').call(input, {
      timeout: 300,
      memory: 1024,
    });

    console.log(`\n✅ Apify run completed with status: ${run.status}`);
    console.log('📊 Run stats:', {
      runId: run.id,
      status: run.status,
      durationMillis: run.stats?.durationMillis,
      memMaxBytes: run.stats?.memMaxBytes
    });

    if (run.status === 'SUCCEEDED') {
      const { items } = await client.dataset(run.defaultDatasetId).listItems();
      console.log(`\n📊 Raw data returned: ${items.length} items`);
      
      items.forEach((item, index) => {
        console.log(`\n📝 Item ${index + 1} Raw Data:`);
        console.log('   Keys:', Object.keys(item));
        
        // Check for error fields
        if (item.error) {
          console.log(`   ❌ ERROR: ${item.error}`);
        }
        if (item.errorDescription) {
          console.log(`   📝 Error Description: ${item.errorDescription}`);
        }
        
        // Check for content fields
        console.log(`   Text: "${item.text || 'NULL'}"`);
        console.log(`   Caption: "${item.caption || 'NULL'}"`);
        console.log(`   Description: "${item.description || 'NULL'}"`);
        console.log(`   Content: "${item.content || 'NULL'}"`);
        
        // Check for media fields
        console.log(`   Display URL: "${item.displayUrl || 'NULL'}"`);
        console.log(`   Image URL: "${item.imageUrl || 'NULL'}"`);
        console.log(`   Video URL: "${item.videoUrl || 'NULL'}"`);
        console.log(`   URL: "${item.url || 'NULL'}"`);
        
        // Check engagement
        console.log(`   Likes: ${item.likesCount || item.likes || 0}`);
        console.log(`   Comments: ${item.commentsCount || item.comments || 0}`);
        
        // Show full raw data structure
        console.log(`   Full Raw Data:`, JSON.stringify(item, null, 2));
      });

      if (items.length === 0) {
        console.log('\n❌ No items returned from Instagram actor');
        console.log('🔧 This suggests the actor failed to scrape any content');
      } else if (items[0]?.error) {
        console.log('\n❌ Instagram actor returned error data');
        console.log('🔧 This explains why we\'re getting fallback content');
      } else {
        console.log('\n✅ Instagram actor returned data successfully');
      }

    } else {
      console.log(`\n❌ Apify run failed with status: ${run.status}`);
    }

  } catch (error) {
    console.error('❌ Instagram raw data test failed:', error.message);
  }

  console.log('\n🎯 INSTAGRAM ACTOR ANALYSIS:');
  console.log('Based on the raw data, we can determine:');
  console.log('1. Whether the actor is working correctly');
  console.log('2. What fields contain the actual content');
  console.log('3. Why our extraction is failing');
  console.log('4. How to fix the content extraction logic');
}

debugInstagramRawData().catch(console.error);
