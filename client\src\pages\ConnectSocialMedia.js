import React, { useState, useEffect } from 'react';
import {
  Plug,
  Facebook,
  Instagram,
  Twitter,
  Linkedin,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  Loader
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';

const ConnectSocialMedia = () => {
  const { apiCall } = useAuth();
  const [connections, setConnections] = useState({
    facebook: { connected: false, user: null, pages: [], connectedAt: null },
    instagram: { connected: false, user: null, connectedAt: null },
    twitter: { connected: false },
    linkedin: { connected: false }
  });
  const [loading, setLoading] = useState({
    facebook: false,
    instagram: false,
    initial: true
  });

  // Load existing connections on component mount
  useEffect(() => {
    loadConnections();
    handleOAuthCallback();
  }, []);

  const loadConnections = async () => {
    try {
      const response = await apiCall('/api/oauth/connections');
      if (response.success) {
        setConnections(response.connections);
      }
    } catch (error) {
      console.error('Failed to load connections:', error);
    } finally {
      setLoading(prev => ({ ...prev, initial: false }));
    }
  };

  // Handle OAuth callback from URL parameters
  const handleOAuthCallback = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const facebookCode = urlParams.get('facebook_code');
    const instagramCode = urlParams.get('instagram_code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');

    if (error) {
      const errorMessages = {
        facebook_denied: 'Facebook connection was cancelled',
        facebook_invalid: 'Invalid Facebook authorization',
        facebook_error: 'Facebook connection failed',
        instagram_denied: 'Instagram connection was cancelled',
        instagram_invalid: 'Invalid Instagram authorization',
        instagram_error: 'Instagram connection failed'
      };
      toast.error(errorMessages[error] || 'Connection failed');
      // Clean URL
      window.history.replaceState({}, document.title, window.location.pathname);
      return;
    }

    if (facebookCode && state) {
      await completeFacebookConnection(facebookCode, state);
    }

    if (instagramCode && state) {
      await completeInstagramConnection(instagramCode, state);
    }
  };

  const handleConnect = async (platform) => {
    setLoading(prev => ({ ...prev, [platform]: true }));

    try {
      const response = await apiCall(`/api/oauth/${platform}/auth`);
      if (response.success) {
        // Open OAuth URL in popup
        const popup = window.open(
          response.authUrl,
          `${platform}_oauth`,
          'width=600,height=600,scrollbars=yes,resizable=yes'
        );

        // Monitor popup for completion
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            clearInterval(checkClosed);
            setLoading(prev => ({ ...prev, [platform]: false }));
            // Reload connections after popup closes
            setTimeout(loadConnections, 1000);
          }
        }, 1000);
      }
    } catch (error) {
      console.error(`${platform} connection error:`, error);
      toast.error(`Failed to connect ${platform}`);
      setLoading(prev => ({ ...prev, [platform]: false }));
    }
  };

  const completeFacebookConnection = async (code, state) => {
    try {
      const response = await apiCall('/api/oauth/facebook/complete', 'POST', { code, state });
      if (response.success) {
        toast.success('Facebook connected successfully!');
        await loadConnections();
      }
    } catch (error) {
      console.error('Facebook completion error:', error);
      toast.error('Failed to complete Facebook connection');
    }
    // Clean URL
    window.history.replaceState({}, document.title, window.location.pathname);
  };

  const completeInstagramConnection = async (code, state) => {
    try {
      const response = await apiCall('/api/oauth/instagram/complete', 'POST', { code, state });
      if (response.success) {
        toast.success('Instagram connected successfully!');
        await loadConnections();
      }
    } catch (error) {
      console.error('Instagram completion error:', error);
      toast.error('Failed to complete Instagram connection');
    }
    // Clean URL
    window.history.replaceState({}, document.title, window.location.pathname);
  };

  const handleDisconnect = async (platform) => {
    setLoading(prev => ({ ...prev, [platform]: true }));

    try {
      const response = await apiCall(`/api/oauth/${platform}/disconnect`, 'DELETE');
      if (response.success) {
        toast.success(`${platform} disconnected successfully`);
        await loadConnections();
      }
    } catch (error) {
      console.error(`${platform} disconnect error:`, error);
      toast.error(`Failed to disconnect ${platform}`);
    } finally {
      setLoading(prev => ({ ...prev, [platform]: false }));
    }
  };

  if (loading.initial) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Loader size={32} className="animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading social media connections...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
              <Plug className="text-white" size={24} />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Connect Social Media</h1>
              <p className="text-gray-600">Connect your social media accounts via official APIs for reliable data extraction</p>
            </div>
          </div>

          {/* Benefits Banner */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <CheckCircle className="text-blue-600 mt-0.5" size={20} />
              <div>
                <h3 className="font-medium text-blue-900">Why Connect APIs?</h3>
                <p className="text-sm text-blue-700 mt-1">
                  Official APIs provide more reliable data, higher rate limits, and access to private content compared to web scraping.
                </p>
                <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-blue-700">
                  <div>✅ More posts (25+ vs 3)</div>
                  <div>✅ Better reliability (99.9% uptime)</div>
                  <div>✅ Higher quality data</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Social Media Platforms Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">

          {/* Facebook Connection */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <Facebook className="text-white" size={20} />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Facebook</h3>
                <p className="text-sm text-gray-500">Connect via Facebook Graph API</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${connections.facebook.connected
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
                  }`}>
                  {connections.facebook.connected ? 'Connected' : 'Not Connected'}
                </span>
              </div>
            </div>

            {!connections.facebook.connected ? (
              <div className="space-y-3">
                <div className="space-y-3">
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-700">
                      <strong>Secure Connection:</strong> You'll be redirected to Facebook's official login page.
                      We never see your password - only Facebook does.
                    </p>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CheckCircle size={16} className="text-green-500" />
                    <span>Your password stays with Facebook</span>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CheckCircle size={16} className="text-green-500" />
                    <span>You control what data we can access</span>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CheckCircle size={16} className="text-green-500" />
                    <span>You can revoke access anytime</span>
                  </div>
                </div>

                <button
                  onClick={() => handleConnect('facebook')}
                  disabled={loading.facebook}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 disabled:bg-blue-400 disabled:cursor-not-allowed"
                >
                  {loading.facebook ? (
                    <>
                      <Loader size={18} className="animate-spin" />
                      <span>Connecting...</span>
                    </>
                  ) : (
                    <>
                      <Facebook size={18} />
                      <span>Connect with Facebook</span>
                    </>
                  )}
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-700">✅ Facebook successfully connected!</p>
                  {connections.facebook.user && (
                    <p className="text-xs text-green-600 mt-1">
                      Connected as: {connections.facebook.user.name}
                    </p>
                  )}
                  {connections.facebook.pages && connections.facebook.pages.length > 0 && (
                    <p className="text-xs text-green-600">
                      Pages: {connections.facebook.pages.length} available
                    </p>
                  )}
                </div>
                <button
                  onClick={() => handleDisconnect('facebook')}
                  disabled={loading.facebook}
                  className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors disabled:bg-red-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {loading.facebook ? (
                    <>
                      <Loader size={18} className="animate-spin" />
                      <span>Disconnecting...</span>
                    </>
                  ) : (
                    <span>Disconnect Facebook</span>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Instagram Connection */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <Instagram className="text-white" size={20} />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Instagram</h3>
                <p className="text-sm text-gray-500">Connect via Instagram Business API</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${connections.instagram.connected
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
                  }`}>
                  {connections.instagram.connected ? 'Connected' : 'Not Connected'}
                </span>
              </div>
            </div>

            {!connections.instagram.connected ? (
              <div className="space-y-3">
                <div className="space-y-3">
                  <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
                    <p className="text-sm text-purple-700">
                      <strong>Secure Connection:</strong> You'll be redirected to Instagram's official login page.
                      We never see your password - only Instagram does.
                    </p>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CheckCircle size={16} className="text-green-500" />
                    <span>Your password stays with Instagram</span>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CheckCircle size={16} className="text-green-500" />
                    <span>You control what data we can access</span>
                  </div>

                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CheckCircle size={16} className="text-green-500" />
                    <span>You can revoke access anytime</span>
                  </div>
                </div>

                <button
                  onClick={() => handleConnect('instagram')}
                  disabled={loading.instagram}
                  className="w-full bg-gradient-to-r from-purple-500 to-pink-500 text-white py-2 px-4 rounded-md hover:from-purple-600 hover:to-pink-600 transition-colors flex items-center justify-center space-x-2 disabled:from-purple-400 disabled:to-pink-400 disabled:cursor-not-allowed"
                >
                  {loading.instagram ? (
                    <>
                      <Loader size={18} className="animate-spin" />
                      <span>Connecting...</span>
                    </>
                  ) : (
                    <>
                      <Instagram size={18} />
                      <span>Connect with Instagram</span>
                    </>
                  )}
                </button>
              </div>
            ) : (
              <div className="space-y-3">
                <div className="p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-700">✅ Instagram successfully connected!</p>
                  {connections.instagram.user && (
                    <p className="text-xs text-green-600 mt-1">
                      Connected as: @{connections.instagram.user.username}
                    </p>
                  )}
                </div>
                <button
                  onClick={() => handleDisconnect('instagram')}
                  disabled={loading.instagram}
                  className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors disabled:bg-red-400 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                >
                  {loading.instagram ? (
                    <>
                      <Loader size={18} className="animate-spin" />
                      <span>Disconnecting...</span>
                    </>
                  ) : (
                    <span>Disconnect Instagram</span>
                  )}
                </button>
              </div>
            )}
          </div>

          {/* Twitter Connection */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 opacity-60">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-black rounded-lg flex items-center justify-center">
                <Twitter className="text-white" size={20} />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">Twitter / X</h3>
                <p className="text-sm text-gray-500">Connect via Twitter API v2</p>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Coming Soon
              </span>
            </div>

            <div className="space-y-3">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Bearer Token</label>
                <input
                  type="password"
                  placeholder="Enter Twitter Bearer Token"
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                />
              </div>

              <button disabled className="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md cursor-not-allowed">
                Coming Soon
              </button>
            </div>
          </div>

          {/* LinkedIn Connection */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 opacity-60">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-blue-700 rounded-lg flex items-center justify-center">
                <Linkedin className="text-white" size={20} />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-900">LinkedIn</h3>
                <p className="text-sm text-gray-500">Connect via LinkedIn API</p>
              </div>
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                Coming Soon
              </span>
            </div>

            <div className="space-y-3">
              <div className="space-y-2">
                <label className="block text-sm font-medium text-gray-700">Client ID</label>
                <input
                  type="text"
                  placeholder="Enter LinkedIn Client ID"
                  disabled
                  className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                />
              </div>

              <button disabled className="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md cursor-not-allowed">
                Coming Soon
              </button>
            </div>
          </div>
        </div>

        {/* Setup Instructions */}
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="text-amber-600 mt-0.5" size={20} />
            <div className="flex-1">
              <h3 className="font-medium text-amber-900 mb-3">How Social Media Connection Works</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-amber-800 mb-2">🔐 For Your Users (Simple)</h4>
                  <ol className="text-sm text-amber-700 space-y-1 list-decimal list-inside">
                    <li>User clicks "Connect with Facebook"</li>
                    <li>Redirected to Facebook's official login page</li>
                    <li>User logs in with their Facebook credentials</li>
                    <li>Facebook asks: "Allow LocalPost.ai to access your posts?"</li>
                    <li>User clicks "Allow" - connection complete!</li>
                  </ol>
                  <div className="mt-2 p-2 bg-green-100 rounded text-xs text-green-800">
                    ✅ <strong>User never enters password on your site</strong>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-amber-800 mb-2">⚙️ For You (One-time Setup)</h4>
                  <ol className="text-sm text-amber-700 space-y-1 list-decimal list-inside">
                    <li>Create Facebook Developer account</li>
                    <li>Create a business app at <a href="https://developers.facebook.com" target="_blank" rel="noopener noreferrer" className="underline inline-flex items-center">developers.facebook.com <ExternalLink size={12} className="ml-1" /></a></li>
                    <li>Add Facebook Login and Instagram products</li>
                    <li>Configure OAuth redirect URLs</li>
                    <li>Submit for app review (if needed)</li>
                  </ol>
                  <div className="mt-2 p-2 bg-blue-100 rounded text-xs text-blue-800">
                    ℹ️ <strong>You set this up once for all users</strong>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-3 bg-amber-100 rounded-md">
                <p className="text-sm text-amber-800">
                  <strong>Security & Privacy:</strong> OAuth is the industry standard used by all major platforms. Users' passwords never leave Facebook/Instagram, and they can revoke access anytime from their account settings.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConnectSocialMedia;
