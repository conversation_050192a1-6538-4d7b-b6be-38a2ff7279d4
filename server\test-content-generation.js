const axios = require('axios');
require('dotenv').config();

async function testContentGeneration() {
  try {
    console.log('🎯 Testing Content Generation Endpoint...\n');

    // First, let's test if we can access the endpoint without auth
    console.log('1. Testing endpoint accessibility:');
    try {
      const response = await axios.post('http://localhost:5000/api/content/generate', {
        startDate: '2024-01-01',
        endDate: '2024-01-07',
        platforms: ['facebook', 'instagram']
      });
      console.log('❌ Unexpected success (should require auth)');
    } catch (error) {
      if (error.response?.status === 401) {
        console.log('✅ Endpoint requires authentication (expected)');
        console.log('   Error:', error.response.data);
      } else {
        console.log('❌ Unexpected error:', error.response?.status, error.response?.data);
      }
    }

    // Test with a mock JWT token (this will likely fail but show us the error)
    console.log('\n2. Testing with mock authentication:');
    try {
      const mockToken = 'Bearer mock-jwt-token';
      const response = await axios.post('http://localhost:5000/api/content/generate', {
        startDate: '2024-01-01',
        endDate: '2024-01-07',
        platforms: ['facebook', 'instagram']
      }, {
        headers: {
          'Authorization': mockToken,
          'Content-Type': 'application/json'
        }
      });
      console.log('✅ Content generation successful:', response.data);
    } catch (error) {
      console.log('❌ Content generation failed:');
      console.log('   Status:', error.response?.status);
      console.log('   Error:', error.response?.data);
      
      if (error.response?.status === 401) {
        console.log('   🔑 Authentication issue - user needs to be logged in');
      } else if (error.response?.status === 400) {
        console.log('   📝 Validation error - check request format');
      } else if (error.response?.status === 500) {
        console.log('   🔧 Server error - check backend logs');
      }
    }

    // Test the OpenAI service directly (should work with mock data)
    console.log('\n3. Testing OpenAI service directly:');
    try {
      const openaiService = require('./services/openai');
      const testContext = {
        businessName: 'Test Business',
        businessType: 'restaurant',
        location: 'Downtown',
        tone: 'friendly',
        contentType: 'social_post',
        platform: 'facebook'
      };
      
      const result = await openaiService.generateContent(testContext);
      console.log('✅ OpenAI service working:');
      console.log('   Generated text:', result.text?.substring(0, 100) + '...');
      console.log('   Hashtags:', result.hashtags);
    } catch (error) {
      console.log('❌ OpenAI service error:', error.message);
    }

    console.log('\n🎯 Content Generation Test Complete!');
    console.log('\nTo fix "Failed to generate content":');
    console.log('1. Make sure you are logged into LocalPost.ai');
    console.log('2. Check browser console for specific error messages');
    console.log('3. Verify the request format matches the API expectations');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testContentGeneration();
