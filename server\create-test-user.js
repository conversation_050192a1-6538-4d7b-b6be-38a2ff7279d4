require('dotenv').config();
const { supabaseAdmin } = require('./config/database');

async function createTestUser() {
  console.log('🔧 Creating test user with admin privileges...\n');

  try {
    const testUserData = {
      email: '<EMAIL>',
      password: 'demo123456',
      email_confirm: true, // Skip email confirmation
      user_metadata: {
        business_name: 'Demo Restaurant',
        business_type: 'restaurant'
      }
    };

    // Create user with admin client (bypasses email confirmation)
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: testUserData.email,
      password: testUserData.password,
      email_confirm: true,
      user_metadata: testUserData.user_metadata
    });

    if (authError) {
      if (authError.message.includes('already registered')) {
        console.log('ℹ️  User already exists, updating...');
        
        // Get existing user
        const { data: existingUsers } = await supabaseAdmin.auth.admin.listUsers();
        const existingUser = existingUsers.users.find(u => u.email === testUserData.email);
        
        if (existingUser) {
          console.log('✅ Found existing user:', existingUser.email);
          console.log('🆔 User ID:', existingUser.id);
          
          // Check if user profile exists
          const { data: profile } = await supabaseAdmin
            .from('users')
            .select('*')
            .eq('id', existingUser.id)
            .single();
            
          if (profile) {
            console.log('✅ User profile exists in database');
            console.log('🏢 Business:', profile.business_name);
            console.log('📍 Location:', profile.location);
            return;
          }
        }
      } else {
        console.log('❌ Auth user creation failed:', authError.message);
        return;
      }
    } else {
      console.log('✅ Auth user created successfully');
      console.log('📧 Email:', authData.user.email);
      console.log('🆔 User ID:', authData.user.id);
    }

    const userId = authData?.user?.id || existingUser?.id;
    if (!userId) {
      console.log('❌ No user ID available');
      return;
    }

    // Create user profile in database
    const profileData = {
      id: userId,
      email: testUserData.email,
      business_name: 'Demo Restaurant',
      business_type: 'restaurant',
      location: 'New York, NY',
      city: 'New York',
      state: 'NY',
      zip_code: '10001',
      subscription_tier: 'starter',
      subscription_status: 'active' // Set to active for testing
    };

    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .upsert(profileData)
      .select()
      .single();

    if (profileError) {
      console.log('❌ Profile creation failed:', profileError.message);
    } else {
      console.log('✅ User profile created/updated successfully');
      console.log('🏢 Business:', profile.business_name);
      console.log('📍 Location:', profile.location);
      console.log('💳 Subscription:', profile.subscription_tier, '-', profile.subscription_status);
    }

    // Create business profile
    const businessProfileData = {
      user_id: userId,
      website_url: 'https://demorestaurant.com',
      social_handles: {},
      hours_operation: {},
      services: [],
      brand_colors: { primary: '#FF6B35', secondary: '#004E89' },
      visual_style: {},
      writing_style: {},
      target_audience: 'Local food enthusiasts and families'
    };

    const { error: businessError } = await supabaseAdmin
      .from('business_profiles')
      .upsert(businessProfileData);

    if (businessError) {
      console.log('⚠️  Business profile creation failed:', businessError.message);
    } else {
      console.log('✅ Business profile created/updated successfully');
    }

    console.log('\n🎉 Test user setup complete!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: demo123456');
    console.log('✅ Ready for API testing and frontend integration');

  } catch (error) {
    console.log('❌ Test user creation failed:', error.message);
  }
}

createTestUser();
