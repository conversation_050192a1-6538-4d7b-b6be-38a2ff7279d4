const axios = require('axios');

async function debugInstagramIssue() {
  console.log('📱 Debugging Instagram Issue\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    console.log('🧪 Testing Instagram with Microsoft account...');
    console.log('📱 URL: https://www.instagram.com/microsoft/');
    
    const response = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Instagram',
      url: 'https://www.instagram.com/microsoft/',
      options: { limit: 10 }
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log('✅ Instagram API Response:');
      console.log(`   📊 Total Posts: ${data.totalPosts}`);
      console.log(`   🔧 Scraping Method: ${data.scrapedWith}`);
      console.log(`   📈 Analysis: ${data.analysis ? 'Available' : 'None'}`);
      console.log(`   🎭 Is Demo: ${data.isDemo || 'Not specified'}`);
      
      console.log('\n📝 Instagram Posts Analysis:');
      data.posts.forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      Content: "${post.content || 'EMPTY CONTENT'}"`);
        console.log(`      Content Length: ${(post.content || '').length} characters`);
        console.log(`      Engagement: 👍 ${post.engagement.likes} | 💬 ${post.engagement.comments}`);
        console.log(`      Has Media: ${post.media?.hasMedia ? 'Yes' : 'No'}`);
        console.log(`      Media Type: ${post.media?.type || 'None'}`);
        console.log(`      Media URL: ${post.media?.url || 'None'}`);
        console.log(`      Platform Note: ${post.platformNote || 'None'}`);
        
        // Check if this is the enhanced empty content we added
        if (post.content === 'Instagram post content (limited by platform restrictions)') {
          console.log(`      🔧 This is enhanced fallback content`);
        }
        
        // Check raw data if available
        if (post.rawData) {
          console.log(`      Raw Data Keys: ${Object.keys(post.rawData).join(', ')}`);
          console.log(`      Raw Text: "${post.rawData.text || post.rawData.caption || 'No text in raw data'}"`);
        }
      });

      // Overall assessment
      const hasEmptyContent = data.posts.some(post => !post.content || post.content.trim() === '');
      const hasEnhancedContent = data.posts.some(post => 
        post.content === 'Instagram post content (limited by platform restrictions)'
      );

      console.log('\n🎯 Instagram Issue Analysis:');
      if (hasEmptyContent) {
        console.log('   ❌ ISSUE: Posts have empty or missing content');
        console.log('   🔧 Problem: Instagram actor not extracting text properly');
      }
      
      if (hasEnhancedContent) {
        console.log('   ⚠️  FALLBACK: Using enhanced content for empty posts');
        console.log('   🔧 Reason: Original Instagram data was empty');
      }
      
      if (data.totalPosts === 1 && hasEmptyContent) {
        console.log('   🎯 ROOT CAUSE: Instagram severely restricts scraping');
        console.log('   💡 SOLUTION: Need better Instagram content extraction');
      }

    } else {
      console.log('❌ Instagram API call failed:', response.data.error);
    }

    // Test with a different Instagram account
    console.log('\n🧪 Testing Instagram with Nike account...');
    console.log('📱 URL: https://www.instagram.com/nike/');
    
    const nikeResponse = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Instagram',
      url: 'https://www.instagram.com/nike/',
      options: { limit: 10 }
    });

    if (nikeResponse.data.success) {
      const nikeData = nikeResponse.data.data;
      console.log('✅ Nike Instagram Results:');
      console.log(`   📊 Posts: ${nikeData.totalPosts}`);
      console.log(`   Content Sample: "${(nikeData.posts[0]?.content || 'EMPTY').substring(0, 50)}..."`);
      
      if (nikeData.posts[0]?.content && nikeData.posts[0].content.length > 10) {
        console.log('   🎉 SUCCESS: Nike has actual content - Instagram actor can work!');
      } else {
        console.log('   ❌ ISSUE: Nike also has empty content - Instagram actor problem');
      }
    }

  } catch (error) {
    console.error('❌ Instagram Debug Error:', error.response?.data || error.message);
  }

  console.log('\n🔧 INSTAGRAM DIAGNOSIS:');
  console.log('Instagram issues are typically caused by:');
  console.log('1. 📱 Instagram heavily restricts automated scraping');
  console.log('2. 🔒 Most content requires authentication to access');
  console.log('3. 🤖 Apify Instagram actor may have limitations');
  console.log('4. 📊 Instagram returns minimal data compared to Facebook');
  
  console.log('\n💡 POTENTIAL SOLUTIONS:');
  console.log('1. 🔧 Improve Instagram content extraction logic');
  console.log('2. 📱 Add Instagram API integration (requires app approval)');
  console.log('3. 🎭 Enhance fallback content for Instagram');
  console.log('4. 📊 Focus on Facebook for comprehensive analysis');
  console.log('5. 🔄 Add manual post upload option for Instagram');
}

debugInstagramIssue().catch(console.error);
