// Clear all require cache
Object.keys(require.cache).forEach(key => {
  delete require.cache[key];
});

// Load environment variables fresh
require('dotenv').config();

console.log('🔍 Fresh Environment Variable Test...\n');

console.log('OpenAI API Key:');
const apiKey = process.env.OPENAI_API_KEY;
console.log('  Exists:', !!apiKey);
console.log('  Length:', apiKey?.length || 0);
console.log('  Starts with:', apiKey?.substring(0, 15) || 'N/A');
console.log('  Ends with:', apiKey ? '...' + apiKey.substring(apiKey.length - 10) : 'N/A');

// Test direct API call
const axios = require('axios');

async function testAPI() {
  try {
    console.log('\n🤖 Testing OpenAI API directly...');
    
    const response = await axios.post(
      'https://api.openai.com/v1/chat/completions',
      {
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Say hello' }],
        max_tokens: 10
      },
      {
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      }
    );

    console.log('✅ API call successful!');
    console.log('Response:', response.data.choices[0].message.content);
    
  } catch (error) {
    console.log('❌ API call failed:');
    console.log('Status:', error.response?.status);
    console.log('Error:', error.response?.data?.error?.message || error.message);
  }
}

testAPI();
