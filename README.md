# LocalPost.ai

AI-powered hyper-local social media content generation platform for small businesses.

## Features

- **Hyper-local Content**: Weather-aware, community-relevant social media posts
- **Brand Voice Learning**: Analyzes existing social media to match your brand
- **Multi-platform Optimization**: Facebook, Instagram, LinkedIn, Twitter
- **Visual Consistency**: AI-generated images matching your brand style
- **30-day Content Calendar**: Complete monthly content planning

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account
- OpenAI API key
- Stripe account (for payments)
- OpenWeatherMap API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd LocalPost
```

2. Install dependencies:
```bash
npm run install:all
```

3. Set up environment variables:
```bash
cp server/.env.example server/.env
# Edit server/.env with your API keys
```

4. Start the development servers:
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:5000

## Project Structure

```
LocalPost/
├── client/          # React frontend application
├── server/          # Node.js backend API
├── database/        # Database schema and migrations
├── shared/          # Shared types and utilities
└── docs/           # Documentation
```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Content Generation Endpoints
- `POST /api/content/generate` - Generate content for date range
- `GET /api/content/posts` - Get user's generated posts
- `PUT /api/content/posts/:id` - Update a specific post
- `DELETE /api/content/posts/:id` - Delete a specific post

### Business Profile Endpoints
- `GET /api/profile` - Get user's business profile
- `PUT /api/profile` - Update business profile
- `POST /api/profile/analyze-social` - Analyze social media for brand voice

### Subscription Endpoints
- `POST /api/subscriptions/create` - Create subscription
- `GET /api/subscriptions/status` - Get subscription status
- `POST /api/subscriptions/cancel` - Cancel subscription

## Environment Variables

See `server/.env.example` for required environment variables.

## Deployment

### Vercel Deployment

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

1. Build the client:
```bash
cd client && npm run build
```

2. Start the production server:
```bash
cd server && npm start
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
