const express = require('express');
const Joi = require('joi');
const { supabase } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');
const openaiService = require('../services/openai');
const websiteExtractionService = require('../services/websiteExtraction');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateUser);

// Validation schemas
const updateProfileSchema = Joi.object({
  businessName: Joi.string().min(2).max(255).optional(),
  businessType: Joi.string().min(2).max(100).optional(),
  location: Joi.string().min(2).max(255).optional(),
  city: Joi.string().max(100).optional(),
  state: Joi.string().max(50).optional(),
  zipCode: Joi.string().max(20).optional()
});

const updateBusinessProfileSchema = Joi.object({
  websiteUrl: Joi.string().uri().optional().allow(''),
  socialHandles: Joi.object().optional(),
  hoursOperation: Joi.object().optional(),
  services: Joi.array().items(Joi.string()).optional(),
  brandColors: Joi.object().optional(),
  visualStyle: Joi.object().optional(),
  writingStyle: Joi.object().optional(),
  targetAudience: Joi.string().max(500).optional().allow(''),
  uniqueSellingPoints: Joi.array().items(Joi.string()).optional()
});

const analyzeSocialMediaSchema = Joi.object({
  socialUrl: Joi.string().uri().required(),
  platform: Joi.string().valid('instagram', 'facebook', 'twitter', 'linkedin').required()
});

// Get user profile
router.get('/', async (req, res) => {
  try {
    // Get user profile
    const { data: userProfile, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', req.userId)
      .single();

    if (userError) {
      return res.status(500).json({ error: 'Failed to fetch user profile' });
    }

    // Get business profile
    const { data: businessProfile, error: businessError } = await supabase
      .from('business_profiles')
      .select('*')
      .eq('user_id', req.userId)
      .single();

    if (businessError && businessError.code !== 'PGRST116') { // Not found is OK
      return res.status(500).json({ error: 'Failed to fetch business profile' });
    }

    // Get latest brand analysis
    const { data: brandAnalysis } = await supabase
      .from('brand_analysis')
      .select('*')
      .eq('user_id', req.userId)
      .order('analyzed_at', { ascending: false })
      .limit(1);

    res.json({
      user: {
        id: userProfile.id,
        email: userProfile.email,
        businessName: userProfile.business_name,
        businessType: userProfile.business_type,
        location: userProfile.location,
        city: userProfile.city,
        state: userProfile.state,
        zipCode: userProfile.zip_code,
        subscriptionTier: userProfile.subscription_tier,
        subscriptionStatus: userProfile.subscription_status,
        createdAt: userProfile.created_at
      },
      businessProfile: businessProfile ? {
        websiteUrl: businessProfile.website_url,
        socialHandles: businessProfile.social_handles,
        hoursOperation: businessProfile.hours_operation,
        services: businessProfile.services,
        brandColors: businessProfile.brand_colors,
        visualStyle: businessProfile.visual_style,
        writingStyle: businessProfile.writing_style,
        targetAudience: businessProfile.target_audience,
        uniqueSellingPoints: businessProfile.unique_selling_points
      } : null,
      brandAnalysis: brandAnalysis?.[0] || null
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ error: 'Failed to get profile data' });
  }
});

// Update user profile
router.put('/', async (req, res) => {
  try {
    // Validate input
    const { error, value } = updateProfileSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Update user profile
    const updateData = {};
    if (value.businessName) updateData.business_name = value.businessName;
    if (value.businessType) updateData.business_type = value.businessType;
    if (value.location) updateData.location = value.location;
    if (value.city) updateData.city = value.city;
    if (value.state) updateData.state = value.state;
    if (value.zipCode) updateData.zip_code = value.zipCode;

    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', req.userId)
      .select()
      .single();

    if (updateError) {
      return res.status(500).json({ error: 'Failed to update profile' });
    }

    res.json({
      message: 'Profile updated successfully',
      user: {
        id: updatedUser.id,
        email: updatedUser.email,
        businessName: updatedUser.business_name,
        businessType: updatedUser.business_type,
        location: updatedUser.location,
        city: updatedUser.city,
        state: updatedUser.state,
        zipCode: updatedUser.zip_code,
        subscriptionTier: updatedUser.subscription_tier,
        subscriptionStatus: updatedUser.subscription_status
      }
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({ error: 'Failed to update profile' });
  }
});

// Update business profile
router.put('/business', async (req, res) => {
  try {
    // Validate input
    const { error, value } = updateBusinessProfileSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    // Update business profile
    const updateData = {};
    if (value.websiteUrl !== undefined) updateData.website_url = value.websiteUrl;
    if (value.socialHandles) updateData.social_handles = value.socialHandles;
    if (value.hoursOperation) updateData.hours_operation = value.hoursOperation;
    if (value.services) updateData.services = value.services;
    if (value.brandColors) updateData.brand_colors = value.brandColors;
    if (value.visualStyle) updateData.visual_style = value.visualStyle;
    if (value.writingStyle) updateData.writing_style = value.writingStyle;
    if (value.targetAudience !== undefined) updateData.target_audience = value.targetAudience;
    if (value.uniqueSellingPoints) updateData.unique_selling_points = value.uniqueSellingPoints;

    const { data: updatedProfile, error: updateError } = await supabase
      .from('business_profiles')
      .upsert({
        user_id: req.userId,
        ...updateData
      })
      .select()
      .single();

    if (updateError) {
      return res.status(500).json({ error: 'Failed to update business profile' });
    }

    res.json({
      message: 'Business profile updated successfully',
      businessProfile: {
        websiteUrl: updatedProfile.website_url,
        socialHandles: updatedProfile.social_handles,
        hoursOperation: updatedProfile.hours_operation,
        services: updatedProfile.services,
        brandColors: updatedProfile.brand_colors,
        visualStyle: updatedProfile.visual_style,
        writingStyle: updatedProfile.writing_style,
        targetAudience: updatedProfile.target_audience,
        uniqueSellingPoints: updatedProfile.unique_selling_points
      }
    });

  } catch (error) {
    console.error('Update business profile error:', error);
    res.status(500).json({ error: 'Failed to update business profile' });
  }
});

// Analyze social media for brand voice
router.post('/analyze-social', async (req, res) => {
  try {
    // Validate input
    const { error, value } = analyzeSocialMediaSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ error: error.details[0].message });
    }

    const { socialUrl, platform } = value;

    // TODO: Implement actual social media scraping
    // For now, we'll simulate with mock data
    const mockPosts = [
      "🌟 Great service today! Thanks to our amazing team for making every customer feel special. #CustomerService #LocalBusiness",
      "Beautiful weather today! Perfect for our outdoor seating. Come join us for lunch! ☀️ #SunnyDay #Lunch #LocalEats",
      "We're excited to announce our new menu items! Fresh, local ingredients make all the difference. #NewMenu #LocalIngredients",
      "Thank you to everyone who visited us this week! Your support means everything to our small business. ❤️ #ThankYou #Community",
      "Weekend special: 20% off all services! Book now and treat yourself. #WeekendSpecial #TreatYourself"
    ];

    // Analyze brand voice using OpenAI
    const brandVoiceAnalysis = await openaiService.analyzeBrandVoice(mockPosts);

    // Save brand analysis
    const { data: savedAnalysis, error: saveError } = await supabase
      .from('brand_analysis')
      .insert({
        user_id: req.userId,
        analysis_type: 'social_media',
        source_url: socialUrl,
        brand_voice_analysis: brandVoiceAnalysis,
        content_themes: brandVoiceAnalysis.common_themes || [],
        posting_patterns: {
          platform: platform,
          analyzed_posts: mockPosts.length,
          analysis_date: new Date().toISOString()
        }
      })
      .select()
      .single();

    if (saveError) {
      console.error('Save analysis error:', saveError);
      return res.status(500).json({ error: 'Failed to save brand analysis' });
    }

    res.json({
      message: 'Social media analysis completed successfully',
      analysis: {
        brandVoice: brandVoiceAnalysis,
        analyzedPosts: mockPosts.length,
        platform: platform,
        sourceUrl: socialUrl
      }
    });

  } catch (error) {
    console.error('Social media analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze social media' });
  }
});

// Get brand analysis history
router.get('/brand-analysis', async (req, res) => {
  try {
    const { data: analyses, error } = await supabase
      .from('brand_analysis')
      .select('*')
      .eq('user_id', req.userId)
      .order('analyzed_at', { ascending: false });

    if (error) {
      return res.status(500).json({ error: 'Failed to fetch brand analyses' });
    }

    res.json({ analyses });

  } catch (error) {
    console.error('Get brand analysis error:', error);
    res.status(500).json({ error: 'Failed to get brand analysis' });
  }
});

// Website extraction endpoint
router.post('/extract-website', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url) {
      return res.status(400).json({ error: 'Website URL is required' });
    }

    // Validate URL format
    try {
      new URL(url.startsWith('http') ? url : `https://${url}`);
    } catch (error) {
      return res.status(400).json({ error: 'Invalid URL format' });
    }

    console.log(`Extracting data from website: ${url}`);

    // Extract website data
    const result = await websiteExtractionService.extractBusinessData(url);

    if (!result.success) {
      return res.status(400).json({
        error: result.error,
        details: 'Failed to extract data from the website. Please check the URL and try again.'
      });
    }

    // Format the response for the frontend
    const extractedData = {
      businessName: result.data.businessName || result.data.title || 'Business',
      description: result.data.description || '',
      industry: result.data.businessType || result.data.industry || 'Business',
      location: result.data.location || '',
      services: result.data.services || [],
      contact: result.data.contact || {},
      socialLinks: result.data.socialLinks || {},
      targetAudience: result.data.targetAudience || '',
      brandVoice: result.data.brandVoice || 'professional',
      keyFeatures: result.data.keyFeatures || [],
      brandColors: result.data.colors || [],
      website: result.sourceUrl,
      extractionSuccess: true,
      enhanced: result.data.enhanced || false
    };

    console.log('Website extraction successful:', {
      businessName: extractedData.businessName,
      industry: extractedData.industry,
      servicesCount: extractedData.services.length,
      enhanced: extractedData.enhanced
    });

    res.json({
      success: true,
      data: extractedData,
      message: 'Website data extracted successfully'
    });

  } catch (error) {
    console.error('Website extraction error:', error);
    res.status(500).json({
      error: 'Failed to extract website data',
      details: error.message
    });
  }
});

module.exports = router;
