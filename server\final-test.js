const axios = require('axios');

async function finalTest() {
  console.log('🎯 Final Content Generation Test\n');

  try {
    console.log('1️⃣ Testing server connection...');
    
    // Simple ping test
    const pingResponse = await axios.get('http://localhost:5000/api/content/posts', {
      headers: {
        'Authorization': 'Bearer test-token'
      },
      timeout: 5000
    });
    
    console.log('✅ Server is responding');
    console.log(`📊 Found ${pingResponse.data.posts?.length || 0} existing posts`);

    console.log('\n2️⃣ Testing content generation...');
    
    const generateData = {
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
      platforms: ['facebook'],
      regenerate: true
    };

    const response = await axios.post('http://localhost:5000/api/content/generate', generateData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 25000
    });

    console.log('✅ Content generation completed!');
    console.log('📊 Response status:', response.status);
    
    // Check the response
    if (response.data.generatedPosts?.length > 0) {
      console.log(`\n🎉 Generated ${response.data.generatedPosts.length} posts!`);
      
      const post = response.data.generatedPosts[0];
      console.log('\n📱 Generated Post:');
      console.log(`   🎯 Platform: ${post.platform}`);
      console.log(`   📄 Content: ${post.content_text}`);
      console.log(`   🏷️  Hashtags: ${post.hashtags?.join(', ') || 'None'}`);
      console.log(`   📅 Date: ${post.date_scheduled}`);
      console.log(`   🌤️  Weather: ${post.weather_context || 'None'}`);
      console.log(`   🖼️  Image: ${post.image_url ? 'Generated' : 'None'}`);
      
      // Analyze content quality
      const isAIGenerated = post.content_text && 
                           post.content_text.length > 50 && 
                           !post.content_text.includes('Demo Restaurant, we\'re passionate') &&
                           !post.content_text.includes('excellent restaurant services');
      
      console.log(`\n🤖 Content Analysis:`);
      console.log(`   Type: ${isAIGenerated ? '✅ Real AI Generated!' : '⚠️  Mock/Fallback Data'}`);
      console.log(`   Length: ${post.content_text?.length || 0} characters`);
      console.log(`   Weather-aware: ${post.weather_context ? '✅ Yes' : '❌ No'}`);
      console.log(`   Hashtags: ${post.hashtags?.length || 0} tags`);
      
      if (isAIGenerated) {
        console.log('\n🎉 SUCCESS! Real AI content generation is working!');
        console.log('🚀 Your "Failed to generate content" issue is COMPLETELY RESOLVED!');
      } else {
        console.log('\n⚠️  Using fallback content - AI generation may have issues');
      }
    } else {
      console.log('\n⚠️  No posts generated');
    }
    
    // Check for errors
    if (response.data.errors?.length > 0) {
      console.log('\n⚠️  Generation Errors:');
      response.data.errors.forEach((error, index) => {
        const errorMsg = typeof error === 'object' ? JSON.stringify(error) : error;
        console.log(`   ${index + 1}. ${errorMsg}`);
        
        if (errorMsg.includes('foreign key constraint')) {
          console.log('      💡 Database user issue - posts generated but not saved');
        } else if (errorMsg.includes('OpenAI')) {
          console.log('      💡 OpenAI API issue');
        }
      });
    } else {
      console.log('\n✅ No errors! Perfect execution!');
    }

    return true;

  } catch (error) {
    console.log('\n❌ Test failed!');
    console.log('📊 Status:', error.response?.status || 'Network Error');
    console.log('📝 Error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔧 Server is not running on port 5000');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏱️  Request timed out - content generation is working but slow');
    }
    
    return false;
  }
}

// Run final test
finalTest().then(success => {
  console.log('\n🎯 FINAL RESULT:');
  if (success) {
    console.log('✅ Content generation system is working!');
    console.log('🎉 Your LocalPost.ai is ready for real users!');
    console.log('\n🚀 Next Steps:');
    console.log('1. Test in the browser UI');
    console.log('2. Create proper user accounts');
    console.log('3. Deploy to production');
  } else {
    console.log('❌ Content generation needs more work');
  }
}).catch(console.error);
