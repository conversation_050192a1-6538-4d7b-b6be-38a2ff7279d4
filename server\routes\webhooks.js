const express = require('express');
const Stripe = require('stripe');
const { supabaseAdmin } = require('../config/database');

const router = express.Router();
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);

const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

// Stripe webhook handler
router.post('/stripe', express.raw({ type: 'application/json' }), async (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
  } catch (err) {
    console.error('Webhook signature verification failed:', err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  console.log('Received Stripe webhook:', event.type);

  try {
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;

      case 'customer.subscription.trial_will_end':
        await handleTrialWillEnd(event.data.object);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    res.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    res.status(500).json({ error: 'Webhook handler failed' });
  }
});

// Handle subscription created
async function handleSubscriptionCreated(subscription) {
  console.log('Subscription created:', subscription.id);

  const customer = await stripe.customers.retrieve(subscription.customer);
  const userId = customer.metadata.userId;

  if (!userId) {
    console.error('No userId found in customer metadata');
    return;
  }

  // Determine plan from subscription
  const planId = getPlanFromSubscription(subscription);
  const status = subscription.status === 'trialing' ? 'trial' : 'active';

  // Update user subscription status
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      subscription_tier: planId,
      subscription_status: status
    })
    .eq('id', userId);

  if (updateError) {
    console.error('Error updating user subscription:', updateError);
  }

  // Save subscription history
  const { error: historyError } = await supabaseAdmin
    .from('subscription_history')
    .insert({
      user_id: userId,
      stripe_subscription_id: subscription.id,
      plan_name: planId,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
    });

  if (historyError) {
    console.error('Error saving subscription history:', historyError);
  }
}

// Handle subscription updated
async function handleSubscriptionUpdated(subscription) {
  console.log('Subscription updated:', subscription.id);

  const customer = await stripe.customers.retrieve(subscription.customer);
  const userId = customer.metadata.userId;

  if (!userId) {
    console.error('No userId found in customer metadata');
    return;
  }

  const planId = getPlanFromSubscription(subscription);
  let status = 'active';

  // Determine status
  if (subscription.status === 'trialing') {
    status = 'trial';
  } else if (subscription.status === 'canceled' || subscription.status === 'unpaid') {
    status = 'canceled';
  } else if (subscription.cancel_at_period_end) {
    status = 'canceling';
  }

  // Update user subscription status
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      subscription_tier: planId,
      subscription_status: status
    })
    .eq('id', userId);

  if (updateError) {
    console.error('Error updating user subscription:', updateError);
  }

  // Save subscription history
  const { error: historyError } = await supabaseAdmin
    .from('subscription_history')
    .insert({
      user_id: userId,
      stripe_subscription_id: subscription.id,
      plan_name: planId,
      status: subscription.status,
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
    });

  if (historyError) {
    console.error('Error saving subscription history:', historyError);
  }
}

// Handle subscription deleted
async function handleSubscriptionDeleted(subscription) {
  console.log('Subscription deleted:', subscription.id);

  const customer = await stripe.customers.retrieve(subscription.customer);
  const userId = customer.metadata.userId;

  if (!userId) {
    console.error('No userId found in customer metadata');
    return;
  }

  // Update user subscription status to canceled
  const { error: updateError } = await supabaseAdmin
    .from('users')
    .update({
      subscription_status: 'canceled'
    })
    .eq('id', userId);

  if (updateError) {
    console.error('Error updating user subscription:', updateError);
  }

  // Save subscription history
  const { error: historyError } = await supabaseAdmin
    .from('subscription_history')
    .insert({
      user_id: userId,
      stripe_subscription_id: subscription.id,
      plan_name: getPlanFromSubscription(subscription),
      status: 'canceled',
      current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
      current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
    });

  if (historyError) {
    console.error('Error saving subscription history:', historyError);
  }
}

// Handle successful payment
async function handlePaymentSucceeded(invoice) {
  console.log('Payment succeeded for invoice:', invoice.id);

  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
    const customer = await stripe.customers.retrieve(subscription.customer);
    const userId = customer.metadata.userId;

    if (userId) {
      // Update subscription status to active
      const { error } = await supabaseAdmin
        .from('users')
        .update({
          subscription_status: 'active'
        })
        .eq('id', userId);

      if (error) {
        console.error('Error updating subscription status after payment:', error);
      }
    }
  }
}

// Handle failed payment
async function handlePaymentFailed(invoice) {
  console.log('Payment failed for invoice:', invoice.id);

  if (invoice.subscription) {
    const subscription = await stripe.subscriptions.retrieve(invoice.subscription);
    const customer = await stripe.customers.retrieve(subscription.customer);
    const userId = customer.metadata.userId;

    if (userId) {
      // Update subscription status to past_due
      const { error } = await supabaseAdmin
        .from('users')
        .update({
          subscription_status: 'past_due'
        })
        .eq('id', userId);

      if (error) {
        console.error('Error updating subscription status after failed payment:', error);
      }

      // TODO: Send email notification about failed payment
    }
  }
}

// Handle trial ending soon
async function handleTrialWillEnd(subscription) {
  console.log('Trial will end for subscription:', subscription.id);

  const customer = await stripe.customers.retrieve(subscription.customer);
  const userId = customer.metadata.userId;

  if (userId) {
    // TODO: Send email notification about trial ending
    console.log(`Trial ending soon for user: ${userId}`);
  }
}

// Helper function to determine plan from subscription
function getPlanFromSubscription(subscription) {
  // This would need to be updated with actual Stripe price IDs
  const priceId = subscription.items.data[0].price.id;
  
  // Map price IDs to plan names
  const priceToPlans = {
    'price_starter': 'starter',
    'price_professional': 'professional',
    'price_agency': 'agency'
  };

  return priceToPlans[priceId] || 'starter';
}

module.exports = router;
