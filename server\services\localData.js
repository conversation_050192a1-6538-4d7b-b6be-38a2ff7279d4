const { supabaseAdmin } = require('../config/database');
const weatherService = require('./weather');
const eventsService = require('./events');

class LocalDataService {
  constructor() {
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
  }

  // Get comprehensive local data for content generation
  async getLocalDataForContent(location, targetDate) {
    try {
      const locationKey = this.generateLocationKey(location);
      const dateStr = targetDate.toISOString().split('T')[0];

      // Check cache first
      const cachedData = await this.getCachedData(locationKey, dateStr);
      if (cachedData && !this.isCacheExpired(cachedData.cached_at)) {
        return {
          weather: cachedData.weather_data,
          events: cachedData.events_data,
          holidays: cachedData.holidays,
          cached: true
        };
      }

      // Fetch fresh data
      const [weatherData, eventsData, holidaysData] = await Promise.all([
        this.getWeatherData(location, targetDate),
        this.getEventsData(location, targetDate),
        this.getHolidaysData(location, targetDate)
      ]);

      const localData = {
        weather: weatherData,
        events: eventsData,
        holidays: holidaysData,
        cached: false
      };

      // Cache the data
      await this.cacheLocalData(locationKey, dateStr, localData);

      return localData;

    } catch (error) {
      console.error('Error fetching local data:', error);
      return this.getFallbackData();
    }
  }

  // Get weather data with context
  async getWeatherData(location, targetDate) {
    try {
      const currentWeather = await weatherService.getCurrentWeather(location);
      const forecast = await weatherService.getWeatherForecast(location, 7);
      const alerts = await weatherService.getWeatherAlerts(location);

      // Find forecast for target date
      const targetForecast = forecast.find(f => 
        f.date.toDateString() === targetDate.toDateString()
      );

      return {
        current: currentWeather,
        forecast: targetForecast || currentWeather,
        alerts: alerts,
        suggestions: weatherService.getWeatherContentSuggestions(
          targetForecast || currentWeather, 
          'general'
        )
      };

    } catch (error) {
      console.error('Weather data error:', error);
      return weatherService.getMockWeatherData();
    }
  }

  // Get events data with context
  async getEventsData(location, targetDate) {
    try {
      const events = await eventsService.getEventsForContent(location, targetDate);
      const seasonalEvents = eventsService.getSeasonalEvents(targetDate);

      return {
        localEvents: events,
        seasonalEvents: seasonalEvents,
        suggestions: eventsService.getEventContentSuggestions(events, 'general')
      };

    } catch (error) {
      console.error('Events data error:', error);
      return {
        localEvents: [],
        seasonalEvents: [],
        suggestions: []
      };
    }
  }

  // Get holidays and observances
  async getHolidaysData(location, targetDate) {
    try {
      const holidays = await eventsService.getHolidays(location, targetDate.getFullYear());
      
      // Find holidays within 7 days of target date
      const relevantHolidays = holidays.filter(holiday => {
        const daysDiff = Math.abs(holiday.date - targetDate) / (1000 * 60 * 60 * 24);
        return daysDiff <= 7;
      });

      return {
        upcoming: relevantHolidays,
        seasonal: eventsService.getSeasonalEvents(targetDate)
      };

    } catch (error) {
      console.error('Holidays data error:', error);
      return { upcoming: [], seasonal: [] };
    }
  }

  // Generate content context from local data
  generateContentContext(localData, businessType) {
    const context = {
      weather: null,
      events: [],
      holidays: [],
      suggestions: []
    };

    // Weather context
    if (localData.weather && localData.weather.forecast) {
      context.weather = {
        condition: localData.weather.forecast.condition,
        temperature: localData.weather.forecast.temperature,
        description: localData.weather.forecast.description,
        alerts: localData.weather.alerts || []
      };

      // Add weather-specific suggestions
      if (localData.weather.suggestions) {
        context.suggestions.push(...localData.weather.suggestions);
      }
    }

    // Events context
    if (localData.events && localData.events.localEvents) {
      context.events = localData.events.localEvents.map(event => ({
        name: event.name,
        date: event.startDate,
        category: event.category,
        venue: event.venue?.name
      }));

      // Add event-specific suggestions
      if (localData.events.suggestions) {
        context.suggestions.push(...localData.events.suggestions);
      }
    }

    // Holidays context
    if (localData.holidays && localData.holidays.upcoming) {
      context.holidays = localData.holidays.upcoming.map(holiday => ({
        name: holiday.name,
        date: holiday.date,
        type: holiday.type
      }));
    }

    return context;
  }

  // Cache management
  async getCachedData(locationKey, dateStr) {
    try {
      const { data, error } = await supabaseAdmin
        .from('local_data_cache')
        .select('*')
        .eq('location_key', locationKey)
        .eq('date', dateStr)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found is OK
        console.error('Cache fetch error:', error);
        return null;
      }

      return data;

    } catch (error) {
      console.error('Cache fetch error:', error);
      return null;
    }
  }

  async cacheLocalData(locationKey, dateStr, localData) {
    try {
      const expiresAt = new Date(Date.now() + this.cacheExpiry);

      const { error } = await supabaseAdmin
        .from('local_data_cache')
        .upsert({
          location_key: locationKey,
          date: dateStr,
          weather_data: localData.weather,
          events_data: localData.events,
          holidays: localData.holidays,
          expires_at: expiresAt.toISOString()
        });

      if (error) {
        console.error('Cache save error:', error);
      }

    } catch (error) {
      console.error('Cache save error:', error);
    }
  }

  // Clean expired cache entries
  async cleanExpiredCache() {
    try {
      const { error } = await supabaseAdmin
        .from('local_data_cache')
        .delete()
        .lt('expires_at', new Date().toISOString());

      if (error) {
        console.error('Cache cleanup error:', error);
      } else {
        console.log('Expired cache entries cleaned');
      }

    } catch (error) {
      console.error('Cache cleanup error:', error);
    }
  }

  // Helper methods
  generateLocationKey(location) {
    return location.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  isCacheExpired(cachedAt) {
    const cacheAge = Date.now() - new Date(cachedAt).getTime();
    return cacheAge > this.cacheExpiry;
  }

  getFallbackData() {
    return {
      weather: {
        current: weatherService.getMockWeatherData(),
        forecast: weatherService.getMockWeatherData(),
        alerts: [],
        suggestions: []
      },
      events: {
        localEvents: [],
        seasonalEvents: [],
        suggestions: []
      },
      holidays: {
        upcoming: [],
        seasonal: []
      },
      cached: false
    };
  }

  // Business-specific data filtering
  filterDataForBusiness(localData, businessType, businessProfile) {
    const filteredData = { ...localData };

    // Filter events based on business relevance
    if (filteredData.events && filteredData.events.localEvents) {
      filteredData.events.localEvents = filteredData.events.localEvents.filter(event => {
        return this.isEventRelevantToBusiness(event, businessType, businessProfile);
      });
    }

    // Add business-specific weather suggestions
    if (filteredData.weather && filteredData.weather.forecast) {
      const businessWeatherSuggestions = weatherService.getWeatherContentSuggestions(
        filteredData.weather.forecast,
        businessType
      );
      filteredData.weather.suggestions = businessWeatherSuggestions;
    }

    return filteredData;
  }

  isEventRelevantToBusiness(event, businessType, businessProfile) {
    const eventCategory = event.category.toLowerCase();
    const eventName = event.name.toLowerCase();

    // Business type relevance mapping
    const relevanceMap = {
      'restaurant': ['food', 'music', 'festival', 'community', 'arts'],
      'retail': ['shopping', 'arts', 'community', 'festival', 'fashion'],
      'fitness': ['sports', 'health', 'wellness', 'community', 'outdoor'],
      'service': ['business', 'community', 'professional', 'networking'],
      'medical': ['health', 'wellness', 'community', 'education'],
      'beauty': ['fashion', 'wellness', 'community', 'arts']
    };

    const relevantCategories = relevanceMap[businessType] || ['community'];
    
    return relevantCategories.some(category => 
      eventCategory.includes(category) || eventName.includes(category)
    );
  }

  // Batch processing for multiple locations/dates
  async batchGetLocalData(requests) {
    const results = [];

    for (const request of requests) {
      try {
        const data = await this.getLocalDataForContent(request.location, request.date);
        results.push({
          ...request,
          data,
          success: true
        });
      } catch (error) {
        results.push({
          ...request,
          error: error.message,
          success: false
        });
      }
    }

    return results;
  }
}

module.exports = new LocalDataService();
