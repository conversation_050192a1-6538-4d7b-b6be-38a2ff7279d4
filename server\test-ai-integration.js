require('dotenv').config();
const OpenAI = require('openai');

async function testOpenAIIntegration() {
  console.log('🤖 Testing OpenAI API Integration...\n');

  // Check if API key is configured
  const apiKey = process.env.OPENAI_API_KEY;
  
  if (!apiKey || apiKey === 'your_openai_api_key' || !apiKey.startsWith('sk-')) {
    console.log('❌ OpenAI API key not configured properly');
    console.log('📝 Please update OPENAI_API_KEY in server/.env');
    console.log('🔗 Get your key at: https://platform.openai.com/api-keys');
    console.log('💡 Key should start with "sk-" and be about 50+ characters long');
    return;
  }

  console.log('✅ OpenAI API key found');
  console.log('🔑 Key format:', `${apiKey.substring(0, 7)}...${apiKey.substring(apiKey.length - 4)}`);

  const openai = new OpenAI({ apiKey });

  try {
    // Test 1: Text Generation
    console.log('\n1️⃣ Testing GPT-4 Text Generation...');
    
    const textCompletion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert social media content creator for local businesses."
        },
        {
          role: "user",
          content: `Create a Facebook post for a restaurant called "Demo Restaurant" in New York, NY. 
                   The weather is sunny and 75°F. Make it engaging and include relevant hashtags.
                   Format: Return only JSON with {text: "post content", hashtags: ["tag1", "tag2"], imagePrompt: "description for image"}`
        }
      ],
      max_tokens: 300,
      temperature: 0.7,
    });

    const textResult = textCompletion.choices[0].message.content;
    console.log('✅ Text generation successful!');
    console.log('📝 Generated content:', textResult.substring(0, 200) + '...');

    // Try to parse the JSON response
    try {
      const parsedContent = JSON.parse(textResult);
      console.log('✅ JSON parsing successful');
      console.log('📄 Text:', parsedContent.text?.substring(0, 100) + '...');
      console.log('🏷️  Hashtags:', parsedContent.hashtags?.join(', '));
      console.log('🖼️  Image prompt:', parsedContent.imagePrompt?.substring(0, 50) + '...');
    } catch (parseError) {
      console.log('⚠️  JSON parsing failed - content needs formatting adjustment');
    }

    // Test 2: Image Generation
    console.log('\n2️⃣ Testing DALL-E 3 Image Generation...');
    
    const imageResponse = await openai.images.generate({
      model: "dall-e-3",
      prompt: "A cozy restaurant interior with warm lighting, wooden tables, and a welcoming atmosphere. Professional food photography style, high quality, appetizing.",
      size: "1024x1024",
      quality: "standard",
      n: 1,
    });

    const imageUrl = imageResponse.data[0].url;
    console.log('✅ Image generation successful!');
    console.log('🖼️  Image URL:', imageUrl);
    console.log('💡 Image will be available for download for 1 hour');

    // Test 3: Account Usage Check
    console.log('\n3️⃣ Testing API Account Status...');
    
    // Note: OpenAI doesn't have a direct usage endpoint in the API
    // But we can test with a minimal request to verify account status
    const testCompletion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Hello" }],
      max_tokens: 5,
    });

    console.log('✅ Account status: Active');
    console.log('💳 API calls working properly');

    console.log('\n🎉 OpenAI Integration Test Complete!');
    console.log('✅ Text generation: Working');
    console.log('✅ Image generation: Working');
    console.log('✅ Account status: Active');
    console.log('\n🚀 Your LocalPost.ai is ready for AI-powered content generation!');

  } catch (error) {
    console.log('\n❌ OpenAI API Error:', error.message);
    
    if (error.status === 401) {
      console.log('🔑 Authentication failed - check your API key');
    } else if (error.status === 429) {
      console.log('⏱️  Rate limit exceeded - try again in a moment');
    } else if (error.status === 402) {
      console.log('💳 Billing issue - check your OpenAI account billing');
    } else {
      console.log('🔍 Full error details:', error);
    }
  }
}

// Run the test
testOpenAIIntegration().catch(console.error);
