const express = require('express');
const Stripe = require('stripe');
const { supabase } = require('../config/database');
const { authenticateUser } = require('../middleware/auth');

const router = express.Router();
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);

// Apply authentication to all routes
router.use(authenticateUser);

// Subscription plans configuration
const SUBSCRIPTION_PLANS = {
  starter: {
    name: 'Starter',
    price: 4900, // $49.00 in cents
    priceId: process.env.STRIPE_STARTER_PRICE_ID || 'price_starter',
    features: ['1 business location', '30 posts per month', '2 social platforms']
  },
  professional: {
    name: 'Professional',
    price: 9900, // $99.00 in cents
    priceId: process.env.STRIPE_PROFESSIONAL_PRICE_ID || 'price_professional',
    features: ['3 business locations', '90 posts per month', 'All social platforms']
  },
  agency: {
    name: 'Agency',
    price: 19900, // $199.00 in cents
    priceId: process.env.STRIPE_AGENCY_PRICE_ID || 'price_agency',
    features: ['10 business locations', '300 posts per month', 'White-label option']
  }
};

// Get subscription status
router.get('/status', async (req, res) => {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('subscription_tier, subscription_status, stripe_customer_id')
      .eq('id', req.userId)
      .single();

    if (error) {
      return res.status(500).json({ error: 'Failed to fetch subscription status' });
    }

    let stripeSubscription = null;
    if (user.stripe_customer_id) {
      try {
        const subscriptions = await stripe.subscriptions.list({
          customer: user.stripe_customer_id,
          status: 'active',
          limit: 1
        });

        if (subscriptions.data.length > 0) {
          stripeSubscription = subscriptions.data[0];
        }
      } catch (stripeError) {
        console.error('Stripe error:', stripeError);
      }
    }

    res.json({
      tier: user.subscription_tier,
      status: user.subscription_status,
      stripeCustomerId: user.stripe_customer_id,
      currentPeriodEnd: stripeSubscription?.current_period_end 
        ? new Date(stripeSubscription.current_period_end * 1000).toISOString()
        : null,
      cancelAtPeriodEnd: stripeSubscription?.cancel_at_period_end || false,
      plans: SUBSCRIPTION_PLANS
    });

  } catch (error) {
    console.error('Get subscription status error:', error);
    res.status(500).json({ error: 'Failed to get subscription status' });
  }
});

// Create subscription
router.post('/create', async (req, res) => {
  try {
    const { planId, paymentMethodId } = req.body;

    if (!planId || !SUBSCRIPTION_PLANS[planId]) {
      return res.status(400).json({ error: 'Invalid plan selected' });
    }

    // Get user data
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('id', req.userId)
      .single();

    if (userError) {
      return res.status(500).json({ error: 'Failed to fetch user data' });
    }

    let customerId = user.stripe_customer_id;

    // Create Stripe customer if doesn't exist
    if (!customerId) {
      const customer = await stripe.customers.create({
        email: user.email,
        metadata: {
          userId: req.userId,
          businessName: user.business_name
        }
      });

      customerId = customer.id;

      // Update user with customer ID
      await supabase
        .from('users')
        .update({ stripe_customer_id: customerId })
        .eq('id', req.userId);
    }

    // Attach payment method to customer
    if (paymentMethodId) {
      await stripe.paymentMethods.attach(paymentMethodId, {
        customer: customerId,
      });

      // Set as default payment method
      await stripe.customers.update(customerId, {
        invoice_settings: {
          default_payment_method: paymentMethodId,
        },
      });
    }

    // Create subscription
    const subscription = await stripe.subscriptions.create({
      customer: customerId,
      items: [{
        price: SUBSCRIPTION_PLANS[planId].priceId,
      }],
      payment_behavior: 'default_incomplete',
      payment_settings: { save_default_payment_method: 'on_subscription' },
      expand: ['latest_invoice.payment_intent'],
      trial_period_days: user.subscription_status === 'trial' ? 0 : 7, // 7-day trial for new users
    });

    // Update user subscription status
    await supabase
      .from('users')
      .update({
        subscription_tier: planId,
        subscription_status: subscription.status === 'trialing' ? 'trial' : 'active'
      })
      .eq('id', req.userId);

    // Save subscription history
    await supabase
      .from('subscription_history')
      .insert({
        user_id: req.userId,
        stripe_subscription_id: subscription.id,
        plan_name: planId,
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
        current_period_end: new Date(subscription.current_period_end * 1000).toISOString()
      });

    res.json({
      subscriptionId: subscription.id,
      clientSecret: subscription.latest_invoice.payment_intent.client_secret,
      status: subscription.status
    });

  } catch (error) {
    console.error('Create subscription error:', error);
    res.status(500).json({ error: 'Failed to create subscription' });
  }
});

// Cancel subscription
router.post('/cancel', async (req, res) => {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', req.userId)
      .single();

    if (error || !user.stripe_customer_id) {
      return res.status(400).json({ error: 'No active subscription found' });
    }

    // Get active subscription
    const subscriptions = await stripe.subscriptions.list({
      customer: user.stripe_customer_id,
      status: 'active',
      limit: 1
    });

    if (subscriptions.data.length === 0) {
      return res.status(400).json({ error: 'No active subscription found' });
    }

    const subscription = subscriptions.data[0];

    // Cancel at period end
    const canceledSubscription = await stripe.subscriptions.update(subscription.id, {
      cancel_at_period_end: true
    });

    res.json({
      message: 'Subscription will be canceled at the end of the current period',
      cancelAt: new Date(canceledSubscription.current_period_end * 1000).toISOString()
    });

  } catch (error) {
    console.error('Cancel subscription error:', error);
    res.status(500).json({ error: 'Failed to cancel subscription' });
  }
});

// Reactivate subscription
router.post('/reactivate', async (req, res) => {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', req.userId)
      .single();

    if (error || !user.stripe_customer_id) {
      return res.status(400).json({ error: 'No subscription found' });
    }

    // Get subscription
    const subscriptions = await stripe.subscriptions.list({
      customer: user.stripe_customer_id,
      limit: 1
    });

    if (subscriptions.data.length === 0) {
      return res.status(400).json({ error: 'No subscription found' });
    }

    const subscription = subscriptions.data[0];

    // Reactivate subscription
    const reactivatedSubscription = await stripe.subscriptions.update(subscription.id, {
      cancel_at_period_end: false
    });

    res.json({
      message: 'Subscription reactivated successfully',
      status: reactivatedSubscription.status
    });

  } catch (error) {
    console.error('Reactivate subscription error:', error);
    res.status(500).json({ error: 'Failed to reactivate subscription' });
  }
});

// Update payment method
router.post('/update-payment-method', async (req, res) => {
  try {
    const { paymentMethodId } = req.body;

    if (!paymentMethodId) {
      return res.status(400).json({ error: 'Payment method ID is required' });
    }

    const { data: user, error } = await supabase
      .from('users')
      .select('stripe_customer_id')
      .eq('id', req.userId)
      .single();

    if (error || !user.stripe_customer_id) {
      return res.status(400).json({ error: 'No customer found' });
    }

    // Attach new payment method
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: user.stripe_customer_id,
    });

    // Set as default payment method
    await stripe.customers.update(user.stripe_customer_id, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    });

    res.json({ message: 'Payment method updated successfully' });

  } catch (error) {
    console.error('Update payment method error:', error);
    res.status(500).json({ error: 'Failed to update payment method' });
  }
});

// Get subscription history
router.get('/history', async (req, res) => {
  try {
    const { data: history, error } = await supabase
      .from('subscription_history')
      .select('*')
      .eq('user_id', req.userId)
      .order('created_at', { ascending: false });

    if (error) {
      return res.status(500).json({ error: 'Failed to fetch subscription history' });
    }

    res.json({ history });

  } catch (error) {
    console.error('Get subscription history error:', error);
    res.status(500).json({ error: 'Failed to get subscription history' });
  }
});

module.exports = router;
