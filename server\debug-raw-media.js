const axios = require('axios');

async function debugRawMedia() {
  console.log('🔍 Debugging Raw Media Data from Apify\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    const response = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      options: { limit: 25 }
    });

    if (response.data.success) {
      const data = response.data.data;
      
      console.log('🔍 Examining Raw Media Data Structure:');
      data.posts.forEach((post, index) => {
        console.log(`\n📝 Post ${index + 1} Raw Media Data:`);
        
        if (post.rawData?.media) {
          console.log('   Media object exists:');
          console.log('   Media keys:', Object.keys(post.rawData.media));
          console.log('   Media content:', JSON.stringify(post.rawData.media, null, 2));
        } else {
          console.log('   ❌ No media object in raw data');
        }

        // Check all possible image fields in raw data
        const rawData = post.rawData;
        console.log('\n   🔍 All possible image fields:');
        console.log(`      image: ${rawData.image || 'NULL'}`);
        console.log(`      photo: ${rawData.photo || 'NULL'}`);
        console.log(`      picture: ${rawData.picture || 'NULL'}`);
        console.log(`      full_picture: ${rawData.full_picture || 'NULL'}`);
        console.log(`      imageUrl: ${rawData.imageUrl || 'NULL'}`);
        console.log(`      media: ${rawData.media ? 'Object present' : 'NULL'}`);
        
        if (rawData.media && Array.isArray(rawData.media)) {
          console.log(`      media array length: ${rawData.media.length}`);
          rawData.media.forEach((mediaItem, i) => {
            console.log(`      media[${i}]:`, JSON.stringify(mediaItem, null, 2));
          });
        } else if (rawData.media && typeof rawData.media === 'object') {
          console.log(`      media object:`, JSON.stringify(rawData.media, null, 2));
        }
      });

    } else {
      console.log('❌ Request failed:', response.data.error);
    }

  } catch (error) {
    console.error('❌ Debug Error:', error.response?.data || error.message);
  }
}

debugRawMedia().catch(console.error);
