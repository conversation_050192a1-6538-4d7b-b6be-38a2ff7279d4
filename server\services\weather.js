const axios = require('axios');

class WeatherService {
  constructor() {
    this.apiKey = process.env.OPENWEATHER_API_KEY;
    this.baseUrl = 'https://api.openweathermap.org/data/2.5';
    
    if (!this.apiKey) {
      console.warn('OpenWeatherMap API key not provided. Weather features will be disabled.');
    }
  }

  // Get current weather for a location
  async getCurrentWeather(location) {
    if (!this.apiKey) {
      return this.getMockWeatherData();
    }

    try {
      const response = await axios.get(`${this.baseUrl}/weather`, {
        params: {
          q: location,
          appid: this.apiKey,
          units: 'imperial'
        }
      });

      const data = response.data;
      
      return {
        condition: data.weather[0].main,
        description: data.weather[0].description,
        temperature: Math.round(data.main.temp),
        feelsLike: Math.round(data.main.feels_like),
        humidity: data.main.humidity,
        windSpeed: data.wind?.speed || 0,
        alerts: [] // Current weather endpoint doesn't include alerts
      };

    } catch (error) {
      console.error('Weather API error:', error.message);
      return this.getMockWeatherData();
    }
  }

  // Get weather forecast for multiple days
  async getWeatherForecast(location, days = 5) {
    if (!this.apiKey) {
      return this.getMockForecastData(days);
    }

    try {
      const response = await axios.get(`${this.baseUrl}/forecast`, {
        params: {
          q: location,
          appid: this.apiKey,
          units: 'imperial',
          cnt: days * 8 // 8 forecasts per day (every 3 hours)
        }
      });

      const forecasts = response.data.list;
      const dailyForecasts = [];

      // Group forecasts by day
      const groupedByDay = {};
      forecasts.forEach(forecast => {
        const date = new Date(forecast.dt * 1000).toDateString();
        if (!groupedByDay[date]) {
          groupedByDay[date] = [];
        }
        groupedByDay[date].push(forecast);
      });

      // Create daily summaries
      Object.keys(groupedByDay).forEach(date => {
        const dayForecasts = groupedByDay[date];
        const midDayForecast = dayForecasts[Math.floor(dayForecasts.length / 2)];
        
        dailyForecasts.push({
          date: new Date(date),
          condition: midDayForecast.weather[0].main,
          description: midDayForecast.weather[0].description,
          temperature: Math.round(midDayForecast.main.temp),
          tempMin: Math.round(Math.min(...dayForecasts.map(f => f.main.temp_min))),
          tempMax: Math.round(Math.max(...dayForecasts.map(f => f.main.temp_max))),
          humidity: midDayForecast.main.humidity,
          windSpeed: midDayForecast.wind?.speed || 0
        });
      });

      return dailyForecasts.slice(0, days);

    } catch (error) {
      console.error('Weather forecast API error:', error.message);
      return this.getMockForecastData(days);
    }
  }

  // Get weather alerts for a location
  async getWeatherAlerts(location) {
    if (!this.apiKey) {
      return [];
    }

    try {
      // First get coordinates
      const geoResponse = await axios.get(`http://api.openweathermap.org/geo/1.0/direct`, {
        params: {
          q: location,
          limit: 1,
          appid: this.apiKey
        }
      });

      if (!geoResponse.data.length) {
        return [];
      }

      const { lat, lon } = geoResponse.data[0];

      // Get alerts using One Call API
      const alertsResponse = await axios.get(`${this.baseUrl}/onecall`, {
        params: {
          lat,
          lon,
          appid: this.apiKey,
          exclude: 'minutely,hourly,daily'
        }
      });

      return alertsResponse.data.alerts || [];

    } catch (error) {
      console.error('Weather alerts API error:', error.message);
      return [];
    }
  }

  // Generate weather-based content suggestions
  getWeatherContentSuggestions(weatherData, businessType) {
    const suggestions = [];
    const { condition, temperature } = weatherData;

    // Temperature-based suggestions
    if (temperature > 85) {
      suggestions.push({
        type: 'hot_weather',
        message: 'Hot weather content opportunity',
        suggestions: this.getHotWeatherSuggestions(businessType)
      });
    } else if (temperature < 40) {
      suggestions.push({
        type: 'cold_weather',
        message: 'Cold weather content opportunity',
        suggestions: this.getColdWeatherSuggestions(businessType)
      });
    }

    // Condition-based suggestions
    if (condition.toLowerCase().includes('rain')) {
      suggestions.push({
        type: 'rainy_weather',
        message: 'Rainy weather content opportunity',
        suggestions: this.getRainyWeatherSuggestions(businessType)
      });
    } else if (condition.toLowerCase().includes('snow')) {
      suggestions.push({
        type: 'snowy_weather',
        message: 'Snowy weather content opportunity',
        suggestions: this.getSnowyWeatherSuggestions(businessType)
      });
    }

    return suggestions;
  }

  // Business-specific weather content suggestions
  getHotWeatherSuggestions(businessType) {
    const suggestions = {
      'restaurant': ['Cold drinks specials', 'Ice cream promotions', 'Outdoor seating comfort'],
      'hvac': ['AC maintenance reminders', 'Energy saving tips', 'Emergency repair services'],
      'retail': ['Summer clothing sales', 'Cooling products', 'Sun protection items'],
      'fitness': ['Early morning workout tips', 'Hydration reminders', 'Indoor class promotions'],
      'default': ['Stay cool tips', 'Summer safety', 'Beat the heat advice']
    };

    return suggestions[businessType] || suggestions['default'];
  }

  getColdWeatherSuggestions(businessType) {
    const suggestions = {
      'restaurant': ['Hot beverage specials', 'Comfort food menu', 'Warm indoor dining'],
      'hvac': ['Heating system checks', 'Winterization tips', 'Energy efficiency'],
      'retail': ['Winter clothing', 'Heating products', 'Cold weather gear'],
      'fitness': ['Indoor workout motivation', 'Winter fitness tips', 'Warm-up importance'],
      'default': ['Stay warm tips', 'Winter safety', 'Cold weather preparation']
    };

    return suggestions[businessType] || suggestions['default'];
  }

  getRainyWeatherSuggestions(businessType) {
    const suggestions = {
      'restaurant': ['Cozy indoor dining', 'Delivery specials', 'Rainy day comfort food'],
      'retail': ['Umbrellas and rain gear', 'Indoor activities', 'Rainy day sales'],
      'service': ['Indoor services available', 'Weather protection', 'Emergency services'],
      'default': ['Rainy day activities', 'Stay dry tips', 'Indoor alternatives']
    };

    return suggestions[businessType] || suggestions['default'];
  }

  getSnowyWeatherSuggestions(businessType) {
    const suggestions = {
      'restaurant': ['Hot drinks and soup', 'Delivery during snow', 'Warm atmosphere'],
      'service': ['Snow removal', 'Winter maintenance', 'Emergency services'],
      'retail': ['Winter supplies', 'Snow gear', 'Indoor entertainment'],
      'default': ['Snow safety tips', 'Winter preparedness', 'Stay warm indoors']
    };

    return suggestions[businessType] || suggestions['default'];
  }

  // Mock data for development/fallback
  getMockWeatherData() {
    return {
      condition: 'Clear',
      description: 'clear sky',
      temperature: 72,
      feelsLike: 75,
      humidity: 45,
      windSpeed: 5,
      alerts: []
    };
  }

  getMockForecastData(days) {
    const forecasts = [];
    const baseTemp = 70;
    const conditions = ['Clear', 'Clouds', 'Rain', 'Sunny'];

    for (let i = 0; i < days; i++) {
      const date = new Date();
      date.setDate(date.getDate() + i);
      
      forecasts.push({
        date,
        condition: conditions[i % conditions.length],
        description: 'mock weather data',
        temperature: baseTemp + (Math.random() * 20 - 10),
        tempMin: baseTemp - 5,
        tempMax: baseTemp + 5,
        humidity: 50,
        windSpeed: 5
      });
    }

    return forecasts;
  }
}

module.exports = new WeatherService();
