import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  CheckCircle, 
  Star,
  Zap,
  Users,
  Calendar,
  TrendingUp
} from 'lucide-react';

const Pricing = () => {
  const { user } = useAuth();

  const pricingPlans = [
    {
      name: 'Starter',
      price: '$49',
      period: '/month',
      description: 'Perfect for single location businesses',
      features: [
        '1 business location',
        '30 posts per month',
        '2 social platforms (Facebook & Instagram)',
        'Basic weather integration',
        'Email support',
        'Content calendar',
        'Basic analytics'
      ],
      popular: false,
      color: 'blue'
    },
    {
      name: 'Professional',
      price: '$99',
      period: '/month',
      description: 'Ideal for growing businesses',
      features: [
        '3 business locations',
        '90 posts per month',
        'All social platforms',
        'Advanced weather & events integration',
        'Priority support',
        'Custom brand templates',
        'Advanced analytics',
        'Brand voice analysis',
        'Image generation'
      ],
      popular: true,
      color: 'primary'
    },
    {
      name: 'Agency',
      price: '$199',
      period: '/month',
      description: 'For agencies and multi-location businesses',
      features: [
        '10 business locations',
        '300 posts per month',
        'White-label option',
        'API access',
        'Dedicated account manager',
        'Custom integrations',
        'Advanced reporting',
        'Team collaboration',
        'Priority AI processing'
      ],
      popular: false,
      color: 'purple'
    }
  ];

  const features = [
    {
      icon: Zap,
      title: 'AI-Powered Content',
      description: 'Generate engaging posts using advanced AI that understands your brand voice'
    },
    {
      icon: Calendar,
      title: 'Smart Scheduling',
      description: 'Automatically schedule posts at optimal times for maximum engagement'
    },
    {
      icon: TrendingUp,
      title: 'Local Intelligence',
      description: 'Weather-aware and event-conscious content that resonates with your community'
    },
    {
      icon: Users,
      title: 'Multi-Platform',
      description: 'Optimized content for Facebook, Instagram, LinkedIn, and Twitter'
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">
          Choose Your Plan
        </h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          Scale your social media presence with AI-powered local content generation
        </p>
        {user && (
          <div className="mt-4 p-4 bg-blue-50 rounded-lg inline-block">
            <p className="text-blue-800">
              <strong>Current Plan:</strong> {user.subscriptionTier} ({user.subscriptionStatus})
            </p>
          </div>
        )}
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-8 mb-16">
        {pricingPlans.map((plan, index) => (
          <div 
            key={index} 
            className={`card relative ${plan.popular ? 'ring-2 ring-primary-500 scale-105' : ''}`}
          >
            {plan.popular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                  <Star size={14} />
                  <span>Most Popular</span>
                </span>
              </div>
            )}
            
            <div className="text-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <div className="flex items-baseline justify-center mb-2">
                <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                <span className="text-gray-600 ml-1">{plan.period}</span>
              </div>
              <p className="text-gray-600">{plan.description}</p>
            </div>
            
            <ul className="space-y-3 mb-8">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-start">
                  <CheckCircle className="text-green-500 mr-3 mt-0.5 flex-shrink-0" size={16} />
                  <span className="text-gray-700">{feature}</span>
                </li>
              ))}
            </ul>
            
            <button
              className={`w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${
                plan.popular
                  ? 'bg-primary-600 hover:bg-primary-700 text-white'
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
              }`}
            >
              {user?.subscriptionTier === plan.name.toLowerCase() ? 'Current Plan' : 'Get Started'}
            </button>
          </div>
        ))}
      </div>

      {/* Features Section */}
      <div className="bg-gray-50 rounded-2xl p-8 mb-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Why Choose LocalPost.ai?
          </h2>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Our platform combines AI technology with local intelligence to create content that truly connects with your community.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => {
            const Icon = feature.icon;
            return (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <Icon className="text-primary-600" size={24} />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm">
                  {feature.description}
                </p>
              </div>
            );
          })}
        </div>
      </div>

      {/* FAQ Section */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Frequently Asked Questions
        </h2>
        
        <div className="grid md:grid-cols-2 gap-6 text-left max-w-4xl mx-auto">
          <div className="card">
            <h3 className="font-semibold text-gray-900 mb-2">Can I change plans anytime?</h3>
            <p className="text-gray-600 text-sm">Yes, you can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle.</p>
          </div>
          
          <div className="card">
            <h3 className="font-semibold text-gray-900 mb-2">Do you offer a free trial?</h3>
            <p className="text-gray-600 text-sm">Yes! All new accounts get a 7-day free trial with full access to Professional features.</p>
          </div>
          
          <div className="card">
            <h3 className="font-semibold text-gray-900 mb-2">What social platforms do you support?</h3>
            <p className="text-gray-600 text-sm">We support Facebook, Instagram, LinkedIn, and Twitter, with content optimized for each platform.</p>
          </div>
          
          <div className="card">
            <h3 className="font-semibold text-gray-900 mb-2">How does the AI understand my brand?</h3>
            <p className="text-gray-600 text-sm">Our AI analyzes your existing social media content and learns your brand voice, tone, and style preferences.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
