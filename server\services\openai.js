const OpenAI = require('openai');

// Function to check if we have a valid OpenAI key
function hasValidOpenAIKey() {
  return process.env.OPENAI_API_KEY &&
    process.env.OPENAI_API_KEY.startsWith('sk-') &&
    !process.env.OPENAI_API_KEY.includes('your_openai');
}

// Function to get OpenAI client (creates new instance each time to pick up env changes)
function getOpenAIClient() {
  if (hasValidOpenAIKey()) {
    return new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });
  }
  return null;
}

class OpenAIService {
  // Generate social media content
  async generateContent(context) {
    // Get fresh OpenAI client to pick up any env changes
    const openai = getOpenAIClient();

    // Use mock data if OpenAI is not configured
    if (!openai) {
      console.warn('⚠️  OpenAI not configured - using mock AI responses');
      return this.getMockContent(context);
    }

    try {
      const prompt = this.buildContentPrompt(context);

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are an expert social media content creator specializing in local business marketing. Create engaging, authentic content that resonates with local communities."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
      });

      const content = completion.choices[0].message.content;
      return this.parseGeneratedContent(content);

    } catch (error) {
      console.error('OpenAI content generation error:', error);
      // Fallback to mock content on error
      return this.getMockContent(context);
    }
  }

  // Generate image using DALL-E 3
  async generateImage(imagePrompt, brandStyle = {}) {
    // Get fresh OpenAI client to pick up any env changes
    const openai = getOpenAIClient();

    // Use mock image if OpenAI is not configured
    if (!openai) {
      console.warn('⚠️  DALL-E not configured - using placeholder image');
      return 'https://via.placeholder.com/1024x1024/3b82f6/ffffff?text=Demo+Image';
    }

    try {
      const enhancedPrompt = this.buildImagePrompt(imagePrompt, brandStyle);

      const response = await openai.images.generate({
        model: "dall-e-3",
        prompt: enhancedPrompt,
        size: "1024x1024",
        quality: "standard",
        n: 1,
      });

      return response.data[0].url;

    } catch (error) {
      console.error('DALL-E image generation error:', error);
      // Fallback to placeholder image
      return 'https://via.placeholder.com/1024x1024/3b82f6/ffffff?text=Image+Generation+Error';
    }
  }

  // Analyze brand voice from social media content
  async analyzeBrandVoice(socialMediaPosts) {
    // Use mock analysis if OpenAI is not configured
    if (!hasValidOpenAIKey) {
      return this.getMockBrandAnalysis();
    }

    try {
      const prompt = `
        Analyze the following social media posts and extract the brand voice characteristics:

        Posts:
        ${socialMediaPosts.map((post, index) => `${index + 1}. ${post}`).join('\n')}

        Please provide a JSON response with the following structure:
        {
          "tone": "professional/casual/friendly/authoritative/etc",
          "personality_traits": ["trait1", "trait2", "trait3"],
          "writing_style": "descriptive text about writing style",
          "common_themes": ["theme1", "theme2", "theme3"],
          "hashtag_style": "description of hashtag usage",
          "content_types": ["type1", "type2", "type3"]
        }
      `;

      const completion = await openai.chat.completions.create({
        model: "gpt-4",
        messages: [
          {
            role: "system",
            content: "You are a brand voice analysis expert. Analyze social media content and return only valid JSON."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        max_tokens: 800,
        temperature: 0.3,
      });

      return JSON.parse(completion.choices[0].message.content);

    } catch (error) {
      console.error('Brand voice analysis error:', error);
      // Fallback to mock analysis
      return this.getMockBrandAnalysis();
    }
  }

  // Mock brand analysis for demo mode
  getMockBrandAnalysis() {
    return {
      tone: "friendly",
      personality_traits: ["welcoming", "professional", "community-focused"],
      writing_style: "Warm and approachable with a focus on community connection and quality service",
      common_themes: ["community", "quality", "customer service", "local pride"],
      hashtag_style: "Mix of location-based and service-related hashtags with community focus",
      content_types: ["promotional", "community engagement", "behind-the-scenes"]
    };
  }

  // Build content generation prompt
  buildContentPrompt(context) {
    const {
      businessType,
      businessName,
      location,
      brandVoice,
      weather,
      events,
      holidays,
      dayOfWeek,
      platform,
      contentType = 'general'
    } = context;

    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    const currentDay = dayNames[dayOfWeek];

    let prompt = `
Create a ${platform} post for ${businessName}, a ${businessType} business in ${location}.

Business Context:
- Business Type: ${businessType}
- Location: ${location}
- Day: ${currentDay}
- Content Type: ${contentType}
`;

    if (brandVoice) {
      prompt += `\nBrand Voice: ${JSON.stringify(brandVoice)}`;
    }

    if (weather) {
      prompt += `\nWeather Context: ${weather.condition}, ${weather.temperature}°F`;
      if (weather.alerts && weather.alerts.length > 0) {
        prompt += `\nWeather Alerts: ${weather.alerts.join(', ')}`;
      }
    }

    if (events && events.length > 0) {
      prompt += `\nLocal Events: ${events.map(e => e.name).join(', ')}`;
    }

    if (holidays && holidays.length > 0) {
      prompt += `\nHolidays/Observances: ${holidays.map(h => h.name).join(', ')}`;
    }

    prompt += `

Requirements:
1. Make it relevant to the local community and current context
2. Include weather or seasonal references when appropriate
3. Mention local events if relevant to the business
4. Keep it authentic and engaging
5. Include 3-5 relevant hashtags
6. Optimize for ${platform} (character limits, style, etc.)
7. Match the brand voice and tone

Format your response as:
TEXT: [the main post content]
HASHTAGS: [hashtags separated by spaces]
IMAGE_PROMPT: [brief description for image generation]
`;

    return prompt;
  }

  // Build image generation prompt
  buildImagePrompt(basePrompt, brandStyle) {
    let enhancedPrompt = basePrompt;

    if (brandStyle.colorPalette && brandStyle.colorPalette.length > 0) {
      enhancedPrompt += ` Use colors: ${brandStyle.colorPalette.join(', ')}.`;
    }

    if (brandStyle.visualStyle) {
      enhancedPrompt += ` Style: ${brandStyle.visualStyle}.`;
    }

    enhancedPrompt += ' Professional quality, social media optimized, clean composition, good lighting.';

    return enhancedPrompt;
  }

  // Parse generated content into structured format
  parseGeneratedContent(content) {
    const lines = content.split('\n').filter(line => line.trim());
    const result = {
      text: '',
      hashtags: [],
      imagePrompt: ''
    };

    for (const line of lines) {
      if (line.startsWith('TEXT:')) {
        result.text = line.substring(5).trim();
      } else if (line.startsWith('HASHTAGS:')) {
        const hashtagsText = line.substring(9).trim();
        result.hashtags = hashtagsText.split(' ').filter(tag => tag.startsWith('#'));
      } else if (line.startsWith('IMAGE_PROMPT:')) {
        result.imagePrompt = line.substring(13).trim();
      }
    }

    // Fallback if parsing fails
    if (!result.text) {
      result.text = content;
    }

    return result;
  }

  // Generate content for multiple days
  async generateBulkContent(userContext, dateRange, platforms = ['facebook', 'instagram']) {
    const results = [];

    for (const date of dateRange) {
      for (const platform of platforms) {
        try {
          const context = {
            ...userContext,
            platform,
            dayOfWeek: date.getDay()
          };

          const content = await this.generateContent(context);

          results.push({
            date,
            platform,
            content,
            success: true
          });

          // Add delay to avoid rate limiting
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          console.error(`Failed to generate content for ${date} on ${platform}:`, error);
          results.push({
            date,
            platform,
            error: error.message,
            success: false
          });
        }
      }
    }

    return results;
  }

  // Mock content generation for demo mode
  getMockContent(context) {
    const { businessType, businessName, location, platform, weather } = context;

    const mockContents = {
      facebook: [
        `🌟 Great day here in ${location}! ${businessName} is ready to serve you with our amazing ${businessType.toLowerCase()} services. ${weather?.condition ? `Perfect ${weather.condition.toLowerCase()} weather for` : 'Perfect day for'} visiting us!`,
        `📍 Located in beautiful ${location}, ${businessName} brings you the best ${businessType.toLowerCase()} experience. Come see what makes us special!`,
        `✨ At ${businessName}, we're passionate about providing excellent ${businessType.toLowerCase()} services to our ${location} community. Visit us today!`
      ],
      instagram: [
        `✨ ${businessName} in ${location} ✨\n\nYour go-to spot for ${businessType.toLowerCase()} services! ${weather?.condition ? `Loving this ${weather.condition.toLowerCase()} weather` : 'Beautiful day'} 📸`,
        `📍 ${location} | ${businessName}\n\nWhere quality meets excellence in ${businessType.toLowerCase()} services 🌟`,
        `🏪 ${businessName} | ${location}\n\nServing our community with pride! Come experience the difference ✨`
      ],
      linkedin: [
        `${businessName} continues to serve the ${location} community with professional ${businessType.toLowerCase()} services. We're committed to excellence and customer satisfaction.`,
        `Proud to be part of the ${location} business community. ${businessName} delivers quality ${businessType.toLowerCase()} services with integrity and expertise.`,
        `At ${businessName}, we understand the unique needs of ${location} residents. Our ${businessType.toLowerCase()} services are tailored to serve our local community.`
      ],
      twitter: [
        `🌟 ${businessName} in ${location} - your trusted ${businessType.toLowerCase()} partner! ${weather?.condition ? `Great ${weather.condition.toLowerCase()} day` : 'Perfect day'} to visit us!`,
        `📍 ${location} | Quality ${businessType.toLowerCase()} services at ${businessName}. Come see the difference! ✨`,
        `Serving ${location} with excellence! ${businessName} - where ${businessType.toLowerCase()} meets quality 🏪`
      ]
    };

    const platformContent = mockContents[platform] || mockContents.facebook;
    const randomContent = platformContent[Math.floor(Math.random() * platformContent.length)];

    const mockHashtags = [
      `#${location.replace(/[^a-zA-Z0-9]/g, '')}`,
      `#${businessType.replace(/[^a-zA-Z0-9]/g, '')}`,
      '#LocalBusiness',
      '#Community',
      '#Quality'
    ];

    return {
      text: randomContent,
      hashtags: mockHashtags.slice(0, 3 + Math.floor(Math.random() * 2)), // 3-4 hashtags
      imagePrompt: `Professional ${businessType.toLowerCase()} business in ${location}, modern and welcoming atmosphere`
    };
  }
}

module.exports = new OpenAIService();
