import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { 
  X, 
  Edit, 
  Trash2, 
  RefreshCw, 
  Save,
  Calendar,
  Hash,
  Image as ImageIcon
} from 'lucide-react';
import toast from 'react-hot-toast';

const PostModal = ({ post, isOpen, onClose, onUpdate, onDelete, onRegenerate }) => {
  const { apiCall } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState(post.content_text);
  const [editedHashtags, setEditedHashtags] = useState(post.hashtags?.join(' ') || '');
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const handleSave = async () => {
    try {
      setLoading(true);
      
      const hashtags = editedHashtags
        .split(' ')
        .filter(tag => tag.trim())
        .map(tag => tag.startsWith('#') ? tag : `#${tag}`);

      const response = await apiCall(`/api/content/posts/${post.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          contentText: editedContent,
          hashtags: hashtags,
          status: 'edited'
        })
      });

      if (response.ok) {
        const data = await response.json();
        onUpdate(data.post);
        setIsEditing(false);
        toast.success('Post updated successfully');
      } else {
        toast.error('Failed to update post');
      }
    } catch (error) {
      console.error('Error updating post:', error);
      toast.error('Failed to update post');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      try {
        await onDelete(post.id);
        onClose();
        toast.success('Post deleted successfully');
      } catch (error) {
        toast.error('Failed to delete post');
      }
    }
  };

  const handleRegenerate = async () => {
    if (window.confirm('Are you sure you want to regenerate this post? This will replace the current content.')) {
      try {
        setLoading(true);
        await onRegenerate(post.id);
        toast.success('Post regenerated successfully');
      } catch (error) {
        toast.error('Failed to regenerate post');
      } finally {
        setLoading(false);
      }
    }
  };

  const platformColors = {
    facebook: 'bg-blue-500',
    instagram: 'bg-pink-500',
    linkedin: 'bg-blue-700',
    twitter: 'bg-blue-400'
  };

  const statusColors = {
    generated: 'bg-blue-100 text-blue-800',
    edited: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    posted: 'bg-purple-100 text-purple-800'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 ${platformColors[post.platform] || 'bg-gray-500'} rounded-lg flex items-center justify-center`}>
              <span className="text-white text-sm font-bold capitalize">
                {post.platform[0]}
              </span>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900 capitalize">
                {post.platform} Post
              </h2>
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <Calendar size={14} />
                <span>{new Date(post.date_scheduled).toLocaleDateString()}</span>
                <span className={`px-2 py-1 rounded-full text-xs ${statusColors[post.status] || 'bg-gray-100 text-gray-800'}`}>
                  {post.status}
                </span>
              </div>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Post Content */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Post Content
            </label>
            {isEditing ? (
              <textarea
                value={editedContent}
                onChange={(e) => setEditedContent(e.target.value)}
                className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent resize-none"
                placeholder="Enter your post content..."
              />
            ) : (
              <div className="p-3 bg-gray-50 rounded-lg border">
                <p className="text-gray-900 whitespace-pre-wrap">{post.content_text}</p>
              </div>
            )}
          </div>

          {/* Hashtags */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Hash size={16} className="inline mr-1" />
              Hashtags
            </label>
            {isEditing ? (
              <input
                type="text"
                value={editedHashtags}
                onChange={(e) => setEditedHashtags(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                placeholder="Enter hashtags separated by spaces..."
              />
            ) : (
              <div className="p-3 bg-gray-50 rounded-lg border">
                {post.hashtags && post.hashtags.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {post.hashtags.map((tag, index) => (
                      <span key={index} className="text-primary-600 text-sm">
                        {tag}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">No hashtags</p>
                )}
              </div>
            )}
          </div>

          {/* Image */}
          {post.image_url && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <ImageIcon size={16} className="inline mr-1" />
                Generated Image
              </label>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <img
                  src={post.image_url}
                  alt="Generated content"
                  className="w-full h-64 object-cover"
                />
              </div>
            </div>
          )}

          {/* Weather Context */}
          {post.weather_context && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Weather Context
              </label>
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800">
                  {post.weather_context.condition} - {post.weather_context.temperature}°F
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-2">
            {isEditing ? (
              <>
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="btn-primary flex items-center space-x-2 disabled:opacity-50"
                >
                  <Save size={16} />
                  <span>{loading ? 'Saving...' : 'Save Changes'}</span>
                </button>
                <button
                  onClick={() => {
                    setIsEditing(false);
                    setEditedContent(post.content_text);
                    setEditedHashtags(post.hashtags?.join(' ') || '');
                  }}
                  className="btn-secondary"
                >
                  Cancel
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="btn-secondary flex items-center space-x-2"
                >
                  <Edit size={16} />
                  <span>Edit</span>
                </button>
                <button
                  onClick={handleRegenerate}
                  disabled={loading}
                  className="btn-secondary flex items-center space-x-2 disabled:opacity-50"
                >
                  <RefreshCw size={16} />
                  <span>{loading ? 'Regenerating...' : 'Regenerate'}</span>
                </button>
              </>
            )}
          </div>
          
          <button
            onClick={handleDelete}
            className="text-red-600 hover:text-red-700 flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-red-50"
          >
            <Trash2 size={16} />
            <span>Delete</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default PostModal;
