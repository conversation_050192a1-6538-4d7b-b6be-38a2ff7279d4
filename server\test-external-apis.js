require('dotenv').config();
const OpenAI = require('openai');
const axios = require('axios');

async function testAllExternalAPIs() {
  console.log('🚀 Testing All External API Integrations...\n');

  const results = {
    openai: { status: 'pending', features: [] },
    weather: { status: 'pending', features: [] },
    events: { status: 'pending', features: [] }
  };

  // Test OpenAI
  console.log('🤖 Testing OpenAI Integration...');
  try {
    const openaiKey = process.env.OPENAI_API_KEY;
    if (!openaiKey || !openaiKey.startsWith('sk-')) {
      throw new Error('API key not configured');
    }

    const openai = new OpenAI({ apiKey: openaiKey });
    
    // Quick text generation test
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [{ role: "user", content: "Say 'API test successful'" }],
      max_tokens: 10,
    });

    results.openai.status = 'working';
    results.openai.features.push('Text Generation', 'GPT-4 Available');
    
    // Test image generation
    try {
      await openai.images.generate({
        model: "dall-e-3",
        prompt: "A simple test image",
        size: "1024x1024",
        n: 1,
      });
      results.openai.features.push('DALL-E 3 Images');
    } catch (imgError) {
      console.log('   ⚠️  DALL-E not available:', imgError.message);
    }

    console.log('   ✅ OpenAI: Working');
  } catch (error) {
    results.openai.status = 'failed';
    results.openai.error = error.message;
    console.log('   ❌ OpenAI: Failed -', error.message);
  }

  // Test Weather API
  console.log('\n🌤️  Testing Weather API...');
  try {
    const weatherKey = process.env.OPENWEATHER_API_KEY;
    if (!weatherKey || weatherKey === 'your_openweather_api_key') {
      throw new Error('API key not configured');
    }

    const response = await axios.get('https://api.openweathermap.org/data/2.5/weather', {
      params: {
        q: 'New York,NY',
        appid: weatherKey,
        units: 'imperial'
      }
    });

    results.weather.status = 'working';
    results.weather.features.push('Current Weather', 'Location Data');
    
    // Test forecast
    try {
      await axios.get('https://api.openweathermap.org/data/2.5/forecast', {
        params: {
          q: 'New York,NY',
          appid: weatherKey,
          units: 'imperial'
        }
      });
      results.weather.features.push('5-Day Forecast');
    } catch (forecastError) {
      console.log('   ⚠️  Forecast not available');
    }

    console.log('   ✅ Weather API: Working');
  } catch (error) {
    results.weather.status = 'failed';
    results.weather.error = error.message;
    console.log('   ❌ Weather API: Failed -', error.message);
  }

  // Test Events API (Eventbrite)
  console.log('\n🎉 Testing Events API...');
  try {
    const eventsKey = process.env.EVENTBRITE_API_KEY;
    if (!eventsKey || eventsKey === 'your_eventbrite_api_key') {
      throw new Error('API key not configured');
    }

    const response = await axios.get('https://www.eventbriteapi.com/v3/events/search/', {
      headers: {
        'Authorization': `Bearer ${eventsKey}`
      },
      params: {
        'location.address': 'New York, NY',
        'location.within': '10mi',
        'start_date.range_start': new Date().toISOString(),
        'expand': 'venue'
      }
    });

    results.events.status = 'working';
    results.events.features.push('Event Search', 'Location-based Events');
    console.log('   ✅ Events API: Working');
  } catch (error) {
    results.events.status = 'failed';
    results.events.error = error.message;
    console.log('   ❌ Events API: Failed -', error.message);
  }

  // Generate Summary Report
  console.log('\n📊 Integration Summary Report');
  console.log('=' .repeat(50));

  const workingAPIs = Object.values(results).filter(r => r.status === 'working').length;
  const totalAPIs = Object.keys(results).length;

  console.log(`\n🎯 Overall Status: ${workingAPIs}/${totalAPIs} APIs Working`);

  // OpenAI Status
  console.log('\n🤖 OpenAI (REQUIRED for AI content):');
  if (results.openai.status === 'working') {
    console.log('   ✅ Status: Working');
    console.log('   🎯 Features:', results.openai.features.join(', '));
  } else {
    console.log('   ❌ Status: Failed');
    console.log('   💡 Fix: Get API key from https://platform.openai.com/');
    console.log('   🔑 Add to .env: OPENAI_API_KEY=sk-your-key-here');
  }

  // Weather Status
  console.log('\n🌤️  Weather API (RECOMMENDED for weather-aware content):');
  if (results.weather.status === 'working') {
    console.log('   ✅ Status: Working');
    console.log('   🎯 Features:', results.weather.features.join(', '));
  } else {
    console.log('   ⚠️  Status: Not configured (will use mock data)');
    console.log('   💡 Fix: Get free key from https://openweathermap.org/api');
    console.log('   🔑 Add to .env: OPENWEATHER_API_KEY=your-key-here');
  }

  // Events Status
  console.log('\n🎉 Events API (OPTIONAL for local events):');
  if (results.events.status === 'working') {
    console.log('   ✅ Status: Working');
    console.log('   🎯 Features:', results.events.features.join(', '));
  } else {
    console.log('   ⚠️  Status: Not configured (will use mock data)');
    console.log('   💡 Fix: Get key from https://www.eventbrite.com/platform/api');
    console.log('   🔑 Add to .env: EVENTBRITE_API_KEY=your-key-here');
  }

  // Recommendations
  console.log('\n🎯 Recommendations:');
  if (results.openai.status !== 'working') {
    console.log('   🚨 CRITICAL: Set up OpenAI API for AI content generation');
  }
  if (results.weather.status !== 'working') {
    console.log('   📈 RECOMMENDED: Set up Weather API for better content');
  }
  if (results.events.status !== 'working') {
    console.log('   💡 OPTIONAL: Set up Events API for local community content');
  }

  if (workingAPIs === totalAPIs) {
    console.log('\n🎉 All APIs configured! Your LocalPost.ai is fully powered! 🚀');
  } else if (results.openai.status === 'working') {
    console.log('\n✅ Core functionality ready! OpenAI is working for AI content generation.');
  } else {
    console.log('\n⚠️  Setup required: OpenAI API key needed for content generation.');
  }

  console.log('\n🔄 To restart server with new API keys: npm run server:dev');
}

// Run the comprehensive test
testAllExternalAPIs().catch(console.error);
