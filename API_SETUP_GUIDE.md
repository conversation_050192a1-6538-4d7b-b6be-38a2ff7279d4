# 🚀 Real API Integration Setup Guide

This guide will help you set up all the external APIs needed for full LocalPost.ai functionality.

## 🔑 Required API Keys

### 1. OpenAI API (Required for AI Content Generation)
**Purpose**: Generate AI-powered social media content and images
**Cost**: Pay-per-use (typically $0.002-0.06 per request)

**Setup Steps**:
1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create an account or sign in
3. Navigate to API Keys section
4. Click "Create new secret key"
5. Copy the key (starts with `sk-`)
6. Add to `.env`: `OPENAI_API_KEY=sk-your-actual-key-here`

**Models Used**:
- GPT-4 for text generation
- DALL-E 3 for image generation

### 2. OpenWeatherMap API (Optional but Recommended)
**Purpose**: Weather-aware content generation
**Cost**: Free tier (1000 calls/day), paid plans available

**Setup Steps**:
1. Go to [OpenWeatherMap](https://openweathermap.org/api)
2. Sign up for free account
3. Navigate to API Keys section
4. Copy your API key
5. Add to `.env`: `OPENWEATHER_API_KEY=your-weather-api-key`

**Features Enabled**:
- Current weather conditions
- 5-day weather forecast
- Weather alerts
- Weather-based content suggestions

### 3. Eventbrite API (Optional)
**Purpose**: Local events integration for community-based content
**Cost**: Free for public events

**Setup Steps**:
1. Go to [Eventbrite API](https://www.eventbrite.com/platform/api)
2. Create developer account
3. Create new app
4. Get your API key
5. Add to `.env`: `EVENTBRITE_API_KEY=your-eventbrite-key`

**Features Enabled**:
- Local event discovery
- Event-based content suggestions
- Community engagement content

## 🛠️ Quick Setup Commands

### Option 1: Manual Setup
1. Get your API keys from the providers above
2. Update `server/.env` with real keys
3. Restart the server: `npm run server:dev`

### Option 2: Test with Demo Keys (Limited)
We can set up demo/test keys for initial testing:

```bash
# OpenAI - You MUST get a real key for this
OPENAI_API_KEY=sk-your-real-openai-key-here

# Weather - Free tier available
OPENWEATHER_API_KEY=your-free-weather-key

# Events - Optional for testing
EVENTBRITE_API_KEY=your-eventbrite-key
```

## 🧪 Testing the Integration

Once you have the keys set up, you can test:

1. **Content Generation Test**:
   ```bash
   cd server
   node test-ai-integration.js
   ```

2. **Weather Integration Test**:
   ```bash
   cd server
   node test-weather-integration.js
   ```

3. **Full API Test**:
   ```bash
   cd server
   node test-external-apis.js
   ```

## 💰 Cost Estimates

### OpenAI API Costs (Typical Usage)
- **Text Generation**: ~$0.002-0.06 per post
- **Image Generation**: ~$0.04 per image
- **Monthly for 100 posts**: ~$5-15

### Weather API Costs
- **Free Tier**: 1000 calls/day (sufficient for most users)
- **Paid Plans**: Start at $40/month for higher limits

### Eventbrite API
- **Free**: For public events
- **No cost** for basic integration

## 🔒 Security Notes

1. **Never commit API keys** to version control
2. **Use environment variables** only
3. **Rotate keys regularly** for production
4. **Monitor usage** to avoid unexpected charges

## 🚀 Next Steps

1. Get OpenAI API key (most important)
2. Get OpenWeatherMap API key (recommended)
3. Update `.env` file with real keys
4. Restart server
5. Test content generation
6. Deploy with confidence!

## 🆘 Troubleshooting

### Common Issues:
- **"OpenAI not configured"**: Check API key format (must start with `sk-`)
- **Weather data not loading**: Verify OpenWeatherMap key
- **Rate limiting**: Add delays between API calls
- **Invalid location**: Ensure location format is "City, State" or "City, Country"

### Support:
- Check server logs for detailed error messages
- Test individual APIs with provided test scripts
- Verify API key permissions and quotas
