const axios = require('axios');

async function debugApifyIssue() {
  console.log('🔍 Debugging Apify Integration Issue\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    console.log('🧪 Testing Paya Finance with Debug Info...');
    
    const response = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      options: {
        limit: 25
      }
    });

    if (response.data.success) {
      const data = response.data.data;
      console.log('📊 Response Analysis:');
      console.log(`   Total Posts: ${data.totalPosts}`);
      console.log(`   Scraping Method: ${data.scrapedWith}`);
      console.log(`   Platform: ${data.platform}`);
      console.log(`   Source URL: ${data.sourceUrl}`);
      
      console.log('\n📝 Post Content Analysis:');
      data.posts.forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      Content: "${post.content.substring(0, 80)}..."`);
        console.log(`      Scraped With: ${post.scrapedWith || 'Not specified'}`);
        console.log(`      Has Real Data: ${post.content.includes('Paya') ? 'YES - Real Paya content ✅' : 'NO - Generic demo content ❌'}`);
        console.log(`      Media URL: ${post.media?.url || 'None'}`);
        console.log(`      Media Type: ${post.media?.type || 'None'}`);
        console.log(`      Has Media: ${post.media?.hasMedia ? 'Yes' : 'No'}`);
      });

      // Check if we're getting real Paya content or demo content
      const hasRealPayaContent = data.posts.some(post => 
        post.content.includes('Paya') || 
        post.content.includes('Buy Now Pay Later') ||
        post.content.includes('Financial Companion')
      );

      console.log(`\n🎯 Content Analysis Result:`);
      if (hasRealPayaContent) {
        console.log('   ✅ SUCCESS: Getting real Paya Finance content');
      } else {
        console.log('   ❌ ISSUE: Getting generic demo content instead of real Paya posts');
        console.log('   🔧 Problem: Apify integration is falling back to demo data');
      }

    } else {
      console.log('❌ Request failed:', response.data.error);
    }

  } catch (error) {
    console.error('❌ Debug Error:', error.response?.data || error.message);
  }

  console.log('\n🔧 DIAGNOSIS:');
  console.log('The system is using fallback demo data instead of real Apify scraping');
  console.log('This means either:');
  console.log('1. Apify API token is not working');
  console.log('2. Apify actor is failing');
  console.log('3. Network/timeout issues with Apify');
  console.log('4. Actor configuration problems');
  
  console.log('\n💡 SOLUTION:');
  console.log('Let me fix the Apify integration to get your real Paya posts back!');
}

debugApifyIssue().catch(console.error);
