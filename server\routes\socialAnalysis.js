const express = require('express');
const router = express.Router();
const socialMediaAnalysisService = require('../services/socialMediaAnalysis');
const socialMediaScraperService = require('../services/socialMediaScraper');

// Analyze social media account posts
router.post('/analyze-account', async (req, res) => {
  try {
    const { platform, accessToken, accountId, options = {} } = req.body;

    // Validate required fields
    if (!platform || !accessToken || !accountId) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: platform, accessToken, and accountId are required'
      });
    }

    // Validate platform
    const supportedPlatforms = ['facebook', 'instagram', 'twitter', 'linkedin'];
    if (!supportedPlatforms.includes(platform.toLowerCase())) {
      return res.status(400).json({
        success: false,
        error: `Unsupported platform: ${platform}. Supported platforms: ${supportedPlatforms.join(', ')}`
      });
    }

    console.log(`Starting social media analysis for ${platform} account: ${accountId}`);

    // Perform the analysis
    const result = await socialMediaAnalysisService.analyzeSocialMediaAccount(
      platform,
      accessToken,
      accountId,
      options
    );

    if (result.success) {
      console.log(`Social media analysis completed successfully for ${platform}`);
      console.log(`Analyzed ${result.data.totalPosts} posts`);

      res.json({
        success: true,
        message: `Successfully analyzed ${result.data.totalPosts} posts from ${platform}`,
        data: result.data
      });
    } else {
      console.error(`Social media analysis failed for ${platform}:`, result.error);
      res.status(400).json({
        success: false,
        error: result.error,
        data: result.data
      });
    }

  } catch (error) {
    console.error('Social media analysis error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during social media analysis',
      details: error.message
    });
  }
});

// Scrape posts from social media URL
router.post('/scrape-posts', async (req, res) => {
  try {
    const { platform, url, options = {} } = req.body;

    // Validate required fields
    if (!platform || !url) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: platform and url are required'
      });
    }

    // Validate platform
    const supportedPlatforms = ['facebook', 'instagram', 'twitter', 'linkedin'];
    if (!supportedPlatforms.includes(platform.toLowerCase())) {
      return res.status(400).json({
        success: false,
        error: `Unsupported platform: ${platform}. Supported platforms: ${supportedPlatforms.join(', ')}`
      });
    }

    console.log(`Starting social media scraping for ${platform} URL: ${url}`);

    // Perform the scraping
    const result = await socialMediaScraperService.scrapePostsFromUrl(
      platform,
      url,
      options
    );

    if (result.success) {
      console.log(`Social media scraping completed successfully for ${platform}`);
      console.log(`Scraped ${result.data.totalPosts} posts`);

      res.json({
        success: true,
        message: `Successfully scraped ${result.data.totalPosts} posts from ${platform}`,
        data: result.data
      });
    } else {
      console.error(`Social media scraping failed for ${platform}:`, result.error);
      res.status(400).json({
        success: false,
        error: result.error,
        data: result.data
      });
    }

  } catch (error) {
    console.error('Social media scraping error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error during social media scraping',
      details: error.message
    });
  }
});

// Get analysis summary for a specific platform
router.get('/summary/:platform/:accountId', async (req, res) => {
  try {
    const { platform, accountId } = req.params;

    // In a real implementation, this would fetch stored analysis data from database
    // For now, we'll return a mock summary
    const summary = {
      platform: platform,
      accountId: accountId,
      lastAnalyzed: new Date().toISOString(),
      totalPostsAnalyzed: 0,
      averageEngagement: 0,
      topPerformingPostType: 'text',
      recommendedPostingTimes: [],
      keyInsights: [
        'Analysis data not available - perform analysis first'
      ]
    };

    res.json({
      success: true,
      data: summary
    });

  } catch (error) {
    console.error('Error fetching analysis summary:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch analysis summary'
    });
  }
});

// Test social media API connection
router.post('/test-connection', async (req, res) => {
  try {
    const { platform, accessToken } = req.body;

    if (!platform || !accessToken) {
      return res.status(400).json({
        success: false,
        error: 'Platform and access token are required'
      });
    }

    // Test the connection based on platform
    let testResult = { connected: false, error: null };

    switch (platform.toLowerCase()) {
      case 'facebook':
        testResult = await testFacebookConnection(accessToken);
        break;
      case 'instagram':
        testResult = await testInstagramConnection(accessToken);
        break;
      case 'twitter':
        testResult = await testTwitterConnection(accessToken);
        break;
      case 'linkedin':
        testResult = await testLinkedInConnection(accessToken);
        break;
      default:
        return res.status(400).json({
          success: false,
          error: `Unsupported platform: ${platform}`
        });
    }

    res.json({
      success: testResult.connected,
      platform: platform,
      connected: testResult.connected,
      error: testResult.error,
      message: testResult.connected
        ? `Successfully connected to ${platform}`
        : `Failed to connect to ${platform}: ${testResult.error}`
    });

  } catch (error) {
    console.error('Connection test error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to test connection'
    });
  }
});

// Get supported platforms and their requirements
router.get('/platforms', (req, res) => {
  const platforms = {
    facebook: {
      name: 'Facebook',
      authType: 'OAuth 2.0',
      requiredScopes: ['pages_read_engagement', 'pages_show_list'],
      dataAvailable: ['posts', 'engagement', 'insights'],
      limitations: 'Requires page access token for business pages'
    },
    instagram: {
      name: 'Instagram Business',
      authType: 'OAuth 2.0',
      requiredScopes: ['instagram_basic', 'instagram_manage_insights'],
      dataAvailable: ['posts', 'stories', 'engagement'],
      limitations: 'Requires Instagram Business account'
    },
    twitter: {
      name: 'Twitter/X',
      authType: 'OAuth 2.0 / Bearer Token',
      requiredScopes: ['tweet.read', 'users.read'],
      dataAvailable: ['tweets', 'engagement', 'metrics'],
      limitations: 'API v2 access required'
    },
    linkedin: {
      name: 'LinkedIn',
      authType: 'OAuth 2.0',
      requiredScopes: ['r_liteprofile', 'r_organization_social'],
      dataAvailable: ['posts', 'engagement'],
      limitations: 'Limited engagement metrics available'
    }
  };

  res.json({
    success: true,
    platforms: platforms,
    totalSupported: Object.keys(platforms).length
  });
});

// Helper functions for testing connections
async function testFacebookConnection(accessToken) {
  try {
    const axios = require('axios');
    const response = await axios.get('https://graph.facebook.com/me', {
      params: { access_token: accessToken }
    });
    return { connected: true, error: null, data: response.data };
  } catch (error) {
    return {
      connected: false,
      error: error.response?.data?.error?.message || error.message
    };
  }
}

async function testInstagramConnection(accessToken) {
  try {
    const axios = require('axios');
    const response = await axios.get('https://graph.instagram.com/me', {
      params: {
        access_token: accessToken,
        fields: 'id,username'
      }
    });
    return { connected: true, error: null, data: response.data };
  } catch (error) {
    return {
      connected: false,
      error: error.response?.data?.error?.message || error.message
    };
  }
}

async function testTwitterConnection(accessToken) {
  try {
    const axios = require('axios');
    const response = await axios.get('https://api.twitter.com/2/users/me', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    return { connected: true, error: null, data: response.data };
  } catch (error) {
    return {
      connected: false,
      error: error.response?.data?.detail || error.message
    };
  }
}

async function testLinkedInConnection(accessToken) {
  try {
    const axios = require('axios');
    const response = await axios.get('https://api.linkedin.com/v2/me', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });
    return { connected: true, error: null, data: response.data };
  } catch (error) {
    return {
      connected: false,
      error: error.response?.data?.message || error.message
    };
  }
}

// Simulate social media analysis with mock data (for testing without real API keys)
router.post('/simulate-analysis', async (req, res) => {
  try {
    const { platform, businessType = 'restaurant' } = req.body;

    if (!platform) {
      return res.status(400).json({
        success: false,
        error: 'Platform is required'
      });
    }

    console.log(`Simulating social media analysis for ${platform} (${businessType})`);

    // Generate mock analysis data
    const mockAnalysis = generateMockAnalysis(platform, businessType);

    res.json({
      success: true,
      message: `Simulated analysis completed for ${platform}`,
      data: mockAnalysis,
      note: 'This is simulated data for testing purposes'
    });

  } catch (error) {
    console.error('Simulation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to simulate analysis'
    });
  }
});

// Generate mock analysis data for testing
function generateMockAnalysis(platform, businessType) {
  const mockPosts = generateMockPosts(platform, businessType);

  return {
    totalPosts: mockPosts.length,
    platform: platform,
    accountId: 'mock_account_123',
    timeRange: {
      earliest: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(), // 6 months ago
      latest: new Date().toISOString(),
      span: '180 days'
    },
    contentAnalysis: {
      averageLength: 120,
      commonWords: [
        { word: 'delicious', count: 15 },
        { word: 'fresh', count: 12 },
        { word: 'special', count: 10 },
        { word: 'today', count: 8 },
        { word: 'menu', count: 7 }
      ],
      hashtags: ['#foodie', '#restaurant', '#delicious', '#fresh', '#local'],
      mentions: ['@localfarm', '@chef'],
      contentTypes: { photo: 25, text: 15, video: 10 },
      toneAnalysis: 'positive'
    },
    engagementAnalysis: {
      averageEngagement: 45,
      averageLikes: 35,
      averageComments: 8,
      averageShares: 2,
      topPerformingPosts: mockPosts.slice(0, 5),
      engagementByType: {
        photo: { average: 52, count: 25 },
        text: { average: 28, count: 15 },
        video: { average: 78, count: 10 }
      },
      mediaImpact: {
        withMedia: 52,
        withoutMedia: 28,
        improvement: 86,
        impact: 'positive'
      }
    },
    performancePatterns: {
      bestTimes: [
        { day: 1, hour: 12, averageEngagement: 65, postCount: 5 },
        { day: 5, hour: 18, averageEngagement: 58, postCount: 4 },
        { day: 6, hour: 11, averageEngagement: 55, postCount: 6 }
      ],
      contentLength: {
        short: { averageEngagement: 35, count: 20 },
        medium: { averageEngagement: 48, count: 25 },
        long: { averageEngagement: 42, count: 5 }
      },
      hashtagUsage: [
        { hashtag: '#foodie', averageEngagement: 58, usageCount: 15 },
        { hashtag: '#delicious', averageEngagement: 52, usageCount: 12 }
      ],
      contentThemes: [
        { theme: 'food', averageEngagement: 55, frequency: 20 },
        { theme: 'special', averageEngagement: 48, frequency: 8 }
      ]
    },
    bestPractices: {
      commonElements: {
        hasQuestions: 30,
        hasCallToAction: 60,
        hasEmojis: 80,
        hasHashtags: 90,
        hasMentions: 20,
        hasLinks: 15
      },
      successfulFormats: {
        visual: 25,
        question: 8,
        announcement: 12,
        educational: 5
      },
      engagementTriggers: [
        'Questions drive engagement',
        'Direct engagement requests',
        'Exclusive content'
      ],
      recommendations: [
        'Include visual content (images/videos) to increase engagement',
        'Use relevant hashtags like #foodie, #delicious, #fresh',
        'Optimal post length appears to be around 120 characters',
        'Post consistently to maintain audience engagement',
        'Engage with your audience through questions and calls-to-action'
      ]
    },
    aiTrainingData: {
      successfulPosts: mockPosts.slice(0, 10).map(post => ({
        content: post.content,
        engagement: post.totalEngagement,
        type: post.type,
        hasMedia: !!post.media?.url
      })),
      contentPatterns: {
        commonPhrases: [
          { phrase: 'fresh ingredients', frequency: 8 },
          { phrase: 'daily special', frequency: 6 },
          { phrase: 'come try', frequency: 5 }
        ],
        sentenceStructures: {
          averageSentenceLength: 8,
          questionRatio: 25,
          exclamationRatio: 35,
          statementRatio: 40
        },
        vocabularyStyle: {
          style: 'casual',
          formalRatio: 10,
          casualRatio: 70,
          technicalRatio: 20
        }
      },
      styleGuide: {
        toneOfVoice: 'positive',
        averagePostLength: 120,
        hashtagUsage: true,
        emojiUsage: true,
        questionUsage: true,
        callToActionUsage: true,
        preferredHashtags: ['#foodie', '#restaurant', '#delicious', '#fresh', '#local'],
        commonWords: ['delicious', 'fresh', 'special', 'today', 'menu']
      }
    }
  };
}

// Generate mock posts for testing
function generateMockPosts(platform, businessType) {
  const posts = [
    {
      id: '1',
      content: 'Fresh pasta made daily with locally sourced ingredients! 🍝 What\'s your favorite pasta dish? #foodie #fresh #pasta',
      totalEngagement: 85,
      engagement: { likes: 65, comments: 15, shares: 5 },
      type: 'photo',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      media: { url: 'https://example.com/pasta.jpg' }
    },
    {
      id: '2',
      content: 'Today\'s special: Grilled salmon with seasonal vegetables. Limited quantities available! 🐟 #special #salmon #healthy',
      totalEngagement: 72,
      engagement: { likes: 58, comments: 12, shares: 2 },
      type: 'photo',
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      media: { url: 'https://example.com/salmon.jpg' }
    },
    {
      id: '3',
      content: 'Behind the scenes: Our chef preparing tomorrow\'s menu. Can you guess what\'s cooking? 👨‍🍳',
      totalEngagement: 95,
      engagement: { likes: 75, comments: 18, shares: 2 },
      type: 'video',
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      media: { url: 'https://example.com/chef-video.mp4' }
    }
  ];

  return posts;
}

module.exports = router;
