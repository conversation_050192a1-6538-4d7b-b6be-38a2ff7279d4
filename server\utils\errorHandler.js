// Custom error classes
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

class ValidationError extends AppError {
  constructor(message, details = null) {
    super(message, 400, 'VALIDATION_ERROR');
    this.details = details;
  }
}

class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed') {
    super(message, 401, 'AUTHENTICATION_ERROR');
  }
}

class AuthorizationError extends AppError {
  constructor(message = 'Access denied') {
    super(message, 403, 'AUTHORIZATION_ERROR');
  }
}

class NotFoundError extends AppError {
  constructor(message = 'Resource not found') {
    super(message, 404, 'NOT_FOUND_ERROR');
  }
}

class ConflictError extends AppError {
  constructor(message = 'Resource conflict') {
    super(message, 409, 'CONFLICT_ERROR');
  }
}

class RateLimitError extends AppError {
  constructor(message = 'Rate limit exceeded') {
    super(message, 429, 'RATE_LIMIT_ERROR');
  }
}

class ExternalServiceError extends AppError {
  constructor(service, message = 'External service error') {
    super(`${service}: ${message}`, 502, 'EXTERNAL_SERVICE_ERROR');
    this.service = service;
  }
}

// Error handler middleware
const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.userId || 'anonymous',
    timestamp: new Date().toISOString()
  });

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = new NotFoundError(message);
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = new ConflictError(message);
  }

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = new ValidationError('Validation failed', message);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Invalid token';
    error = new AuthenticationError(message);
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expired';
    error = new AuthenticationError(message);
  }

  // Supabase errors
  if (err.code && err.code.startsWith('PGRST')) {
    if (err.code === 'PGRST116') {
      error = new NotFoundError('Resource not found');
    } else {
      error = new AppError('Database error', 500, 'DATABASE_ERROR');
    }
  }

  // OpenAI API errors
  if (err.response && err.response.status) {
    if (err.response.status === 429) {
      error = new RateLimitError('AI service rate limit exceeded');
    } else if (err.response.status >= 500) {
      error = new ExternalServiceError('OpenAI', 'AI service temporarily unavailable');
    } else {
      error = new ExternalServiceError('OpenAI', err.message);
    }
  }

  // Stripe errors
  if (err.type && err.type.startsWith('Stripe')) {
    if (err.type === 'StripeCardError') {
      error = new ValidationError('Payment failed: ' + err.message);
    } else if (err.type === 'StripeRateLimitError') {
      error = new RateLimitError('Payment service rate limit exceeded');
    } else {
      error = new ExternalServiceError('Stripe', err.message);
    }
  }

  // Default to 500 server error
  if (!error.isOperational) {
    error = new AppError('Something went wrong', 500, 'INTERNAL_SERVER_ERROR');
  }

  // Send error response
  const response = {
    error: error.message,
    code: error.code,
    timestamp: new Date().toISOString()
  };

  // Add details in development
  if (process.env.NODE_ENV === 'development') {
    response.stack = err.stack;
    if (error.details) {
      response.details = error.details;
    }
  }

  // Add request ID for tracking
  if (req.id) {
    response.requestId = req.id;
  }

  res.status(error.statusCode || 500).json(response);
};

// Async error wrapper
const asyncHandler = (fn) => (req, res, next) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

// 404 handler
const notFoundHandler = (req, res, next) => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};

// Validation error formatter
const formatValidationError = (error) => {
  if (error.details && Array.isArray(error.details)) {
    return error.details.map(detail => ({
      field: detail.path ? detail.path.join('.') : 'unknown',
      message: detail.message,
      value: detail.context ? detail.context.value : undefined
    }));
  }
  return null;
};

// Error logging utility
const logError = (error, context = {}) => {
  const logData = {
    message: error.message,
    stack: error.stack,
    code: error.code,
    statusCode: error.statusCode,
    timestamp: new Date().toISOString(),
    ...context
  };

  // In production, you might want to send this to a logging service
  console.error('Application Error:', JSON.stringify(logData, null, 2));
};

// Health check error
const createHealthCheckError = (checks) => {
  const failedChecks = checks.filter(check => !check.healthy);
  const message = `Health check failed: ${failedChecks.map(c => c.name).join(', ')}`;
  return new AppError(message, 503, 'HEALTH_CHECK_FAILED');
};

module.exports = {
  // Error classes
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  ExternalServiceError,
  
  // Middleware
  errorHandler,
  asyncHandler,
  notFoundHandler,
  
  // Utilities
  formatValidationError,
  logError,
  createHealthCheckError
};
