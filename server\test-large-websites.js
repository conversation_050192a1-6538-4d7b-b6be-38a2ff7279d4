const axios = require('axios');

async function testLargeWebsites() {
  console.log('🌐 Testing Large Website Content Handling\n');

  const testCases = [
    {
      name: 'Small Website (should work)',
      url: 'https://example.com',
      expectedResult: 'success',
      description: 'Simple test case'
    },
    {
      name: 'Medium Website (should work with filtering)',
      url: 'https://www.w3.org',
      expectedResult: 'success',
      description: 'Moderate content size'
    },
    {
      name: 'Large Corporate Site (should work with new limits)',
      url: 'https://www.microsoft.com',
      expectedResult: 'success',
      description: 'Large corporate website'
    },
    {
      name: 'Another Large Site (should work)',
      url: 'https://www.github.com',
      expectedResult: 'success',
      description: 'Popular large website'
    },
    {
      name: 'Very Large Media Site (may need filtering)',
      url: 'https://www.cnn.com',
      expectedResult: 'success_or_filtered',
      description: 'Media-heavy website'
    }
  ];

  let successCount = 0;
  let filteredCount = 0;
  let failureCount = 0;

  for (const testCase of testCases) {
    console.log(`\n🧪 Testing: ${testCase.name}`);
    console.log(`   URL: ${testCase.url}`);
    console.log(`   Expected: ${testCase.expectedResult}`);
    console.log('   ' + '─'.repeat(60));

    try {
      const startTime = Date.now();
      
      const response = await axios.post('http://localhost:5000/api/profile/extract-website', {
        url: testCase.url
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token'
        },
        timeout: 30000 // 30 second timeout for large sites
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (response.data.success) {
        const data = response.data.data;
        console.log(`   ✅ SUCCESS (${duration}ms)`);
        console.log(`      📊 Business Name: ${data.businessName}`);
        console.log(`      🏢 Industry: ${data.industry}`);
        console.log(`      📝 Description: ${data.description?.substring(0, 80)}...`);
        console.log(`      📍 Location: ${data.location || 'Not found'}`);
        console.log(`      🛠️  Services: ${data.services?.length || 0} found`);
        console.log(`      🎨 Colors: ${data.brandColors?.length || 0} found`);
        console.log(`      🤖 AI Enhanced: ${data.enhanced ? 'Yes' : 'No'}`);
        
        if (duration > 10000) {
          console.log(`      ⏱️  Note: Slow response (${duration}ms) - content filtering may have occurred`);
          filteredCount++;
        } else {
          successCount++;
        }
      } else {
        console.log(`   ❌ FAILED: ${response.data.error}`);
        failureCount++;
      }

    } catch (error) {
      console.log(`   ❌ ERROR: ${error.response?.data?.error || error.message}`);
      
      if (error.response?.data?.error?.includes('too large')) {
        console.log(`      💡 This is expected for very large sites`);
        console.log(`      🔧 User will see helpful error message with suggestions`);
        filteredCount++;
      } else {
        failureCount++;
      }
    }
  }

  // Summary
  console.log('\n' + '='.repeat(70));
  console.log('🎯 LARGE WEBSITE HANDLING TEST RESULTS');
  console.log('='.repeat(70));
  console.log(`📊 Total Tests: ${testCases.length}`);
  console.log(`✅ Successful Extractions: ${successCount}`);
  console.log(`🔄 Filtered/Slow Extractions: ${filteredCount}`);
  console.log(`❌ Failed Extractions: ${failureCount}`);
  
  const totalHandled = successCount + filteredCount;
  const successRate = Math.round((totalHandled / testCases.length) * 100);
  
  console.log(`📈 Overall Success Rate: ${successRate}%`);

  if (successRate >= 80) {
    console.log('\n🎉 EXCELLENT! Large website handling is working well!');
  } else if (successRate >= 60) {
    console.log('\n✅ GOOD! Most large websites are being handled properly.');
  } else {
    console.log('\n⚠️  NEEDS IMPROVEMENT! Large website handling needs more work.');
  }

  console.log('\n🔧 IMPROVEMENTS IMPLEMENTED:');
  console.log('✅ Increased content limit: 100KB → 500KB');
  console.log('✅ Smart content filtering: Removes scripts, styles, non-essential elements');
  console.log('✅ Progressive processing: Processes only first 200KB for performance');
  console.log('✅ Better error messages: User-friendly suggestions for large sites');
  console.log('✅ Timeout handling: Extended timeout for large sites');

  console.log('\n💡 USER EXPERIENCE:');
  console.log('• Small/Medium sites: Fast extraction with full data');
  console.log('• Large sites: Filtered extraction with essential data');
  console.log('• Very large sites: Helpful error with manual entry suggestion');
  console.log('• All cases: Clear feedback and suggestions');

  console.log('\n🌐 READY FOR TESTING:');
  console.log('1. Open http://localhost:3000');
  console.log('2. Go to Business Profile → Website Extract');
  console.log('3. Try various website sizes:');
  console.log('   • Small: https://example.com');
  console.log('   • Medium: https://www.w3.org');
  console.log('   • Large: https://www.microsoft.com');
  console.log('4. Observe improved handling and error messages!');
}

testLargeWebsites().catch(console.error);
