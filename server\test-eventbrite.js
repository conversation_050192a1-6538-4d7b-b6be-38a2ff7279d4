require('dotenv').config();
const axios = require('axios');

async function testEventbriteAPI() {
  console.log('🎉 Testing Eventbrite API Integration\n');

  const apiKey = process.env.EVENTBRITE_API_KEY;
  
  if (!apiKey || apiKey === 'your_eventbrite_api_key') {
    console.log('❌ Eventbrite API key not configured');
    return;
  }

  console.log('✅ Eventbrite API key found');
  console.log(`🔑 Key format: ${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);

  try {
    // Test 1: Search for events in New York
    console.log('\n1️⃣ Testing Event Search in New York...');
    
    const response = await axios.get('https://www.eventbriteapi.com/v3/events/search/', {
      headers: {
        'Authorization': `Bearer ${apiKey}`
      },
      params: {
        'location.address': 'New York, NY',
        'location.within': '10mi',
        'start_date.range_start': new Date().toISOString(),
        'expand': 'venue',
        'page_size': 5
      }
    });

    const events = response.data.events;
    console.log(`✅ Found ${events.length} events in New York!`);
    
    if (events.length > 0) {
      console.log('\n📅 Sample Events:');
      events.slice(0, 3).forEach((event, index) => {
        const startDate = new Date(event.start.local);
        console.log(`   ${index + 1}. ${event.name.text}`);
        console.log(`      📍 ${event.venue?.name || 'Online Event'}`);
        console.log(`      📅 ${startDate.toLocaleDateString()} at ${startDate.toLocaleTimeString()}`);
        console.log(`      🎯 ${event.summary || 'No description'}`);
        console.log('');
      });
    }

    // Test 2: Search for different event categories
    console.log('\n2️⃣ Testing Event Categories...');
    
    const categories = ['food-and-drink', 'business', 'community'];
    
    for (const category of categories) {
      try {
        const categoryResponse = await axios.get('https://www.eventbriteapi.com/v3/events/search/', {
          headers: {
            'Authorization': `Bearer ${apiKey}`
          },
          params: {
            'location.address': 'New York, NY',
            'location.within': '15mi',
            'categories': category,
            'start_date.range_start': new Date().toISOString(),
            'page_size': 2
          }
        });
        
        console.log(`   ${category}: ${categoryResponse.data.events.length} events found`);
      } catch (catError) {
        console.log(`   ${category}: Error - ${catError.response?.status}`);
      }
    }

    // Test 3: Generate content suggestions based on events
    console.log('\n3️⃣ Generating Content Suggestions...');
    
    if (events.length > 0) {
      const upcomingEvent = events[0];
      const eventDate = new Date(upcomingEvent.start.local);
      const isThisWeek = (eventDate - new Date()) < (7 * 24 * 60 * 60 * 1000);
      
      console.log('💡 Event-Based Content Ideas:');
      
      if (isThisWeek) {
        console.log(`   🎯 "Don't miss ${upcomingEvent.name.text} happening this week in NYC!"`);
        console.log(`   🍽️ "Fuel up before ${upcomingEvent.name.text} with our pre-event specials!"`);
        console.log(`   🎉 "Join the excitement around ${upcomingEvent.name.text} - we're open late!"`);
      } else {
        console.log(`   📅 "Mark your calendars for ${upcomingEvent.name.text}!"`);
        console.log(`   🌟 "NYC has amazing events like ${upcomingEvent.name.text} - we love this community!"`);
      }
    }

    console.log('\n🎉 Eventbrite Integration Test Complete!');
    console.log('✅ Event search: Working');
    console.log('✅ Location filtering: Working');
    console.log('✅ Content suggestions: Working');
    console.log('\n🚀 Your LocalPost.ai now has local events integration!');

    return true;

  } catch (error) {
    console.log('\n❌ Eventbrite API Error:', error.response?.status, error.message);
    
    if (error.response?.status === 401) {
      console.log('🔑 Authentication failed - check your API key');
      console.log('💡 Make sure you\'re using the Private token, not the Public token');
    } else if (error.response?.status === 403) {
      console.log('🚫 Access forbidden - check API key permissions');
    } else if (error.response?.status === 429) {
      console.log('⏱️  Rate limit exceeded - try again later');
    }
    
    console.log('🔍 Full error details:', error.response?.data || error.message);
    return false;
  }
}

testEventbriteAPI().catch(console.error);
