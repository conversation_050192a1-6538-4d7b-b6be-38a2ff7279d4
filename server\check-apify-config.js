require('dotenv').config();

console.log('🔍 Checking Apify Configuration\n');

console.log('Environment Variables:');
console.log(`APIFY_API_TOKEN: ${process.env.APIFY_API_TOKEN ? 'SET ✅' : 'NOT SET ❌'}`);
if (process.env.APIFY_API_TOKEN) {
  console.log(`Token Preview: ${process.env.APIFY_API_TOKEN.substring(0, 20)}...`);
  console.log(`Token Length: ${process.env.APIFY_API_TOKEN.length} characters`);
}

// Test Apify client initialization
try {
  const { ApifyClient } = require('apify-client');
  const client = new ApifyClient({
    token: process.env.APIFY_API_TOKEN,
  });
  console.log('\n✅ Apify Client initialized successfully');
  
  // Test a simple API call
  console.log('\n🧪 Testing Apify API connection...');
  
  client.user().get().then(user => {
    console.log('✅ Apify API connection successful!');
    console.log(`User: ${user.username || 'Unknown'}`);
    console.log(`Plan: ${user.plan || 'Unknown'}`);
  }).catch(error => {
    console.log('❌ Apify API connection failed:', error.message);
  });
  
} catch (error) {
  console.log('❌ Failed to initialize Apify client:', error.message);
}
