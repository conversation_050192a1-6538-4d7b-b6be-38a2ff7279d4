import React, { createContext, useContext, useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import toast from 'react-hot-toast';

const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;
const supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY;

// Check if we have valid Supabase credentials
const hasValidSupabaseConfig = supabaseUrl &&
  supabaseAnonKey &&
  supabaseUrl.startsWith('https://') &&
  !supabaseUrl.includes('your_supabase') &&
  supabaseAnonKey.length > 20 &&
  !supabaseAnonKey.includes('your_supabase');

let supabase = null;
let isDemoMode = false;

if (hasValidSupabaseConfig) {
  supabase = createClient(supabaseUrl, supabaseAnonKey);
} else {
  console.warn('⚠️  Supabase not configured - running in DEMO MODE');
  console.warn('   To enable full functionality, set up Supabase credentials in client/.env');
  isDemoMode = true;

  // Create mock Supabase client for demo mode
  supabase = {
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: null }),
      onAuthStateChange: (callback) => {
        // Call callback immediately with no session
        callback('SIGNED_OUT', null);
        return { data: { subscription: { unsubscribe: () => { } } } };
      },
      signUp: () => Promise.resolve({
        data: { user: null },
        error: { message: 'Demo mode - Please set up Supabase for full functionality' }
      }),
      signInWithPassword: () => Promise.resolve({
        data: { user: null },
        error: { message: 'Demo mode - Please set up Supabase for full functionality' }
      }),
      signOut: () => Promise.resolve({ error: null }),
      getUser: () => Promise.resolve({
        data: { user: null },
        error: { message: 'Demo mode - Please set up Supabase for full functionality' }
      }),
      refreshSession: () => Promise.resolve({
        data: { session: null },
        error: { message: 'Demo mode - Please set up Supabase for full functionality' }
      })
    },
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({
            data: null,
            error: { message: 'Demo mode - Please set up Supabase for full functionality' }
          })
        })
      }),
      insert: () => Promise.resolve({
        data: null,
        error: { message: 'Demo mode - Please set up Supabase for full functionality' }
      }),
      update: () => ({
        eq: () => Promise.resolve({
          data: null,
          error: { message: 'Demo mode - Please set up Supabase for full functionality' }
        })
      })
    })
  };
}

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [session, setSession] = useState(null);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error('Error getting session:', error);
        } else if (session) {
          setSession(session);
          await fetchUserProfile(session.user.id);
        }
      } catch (error) {
        console.error('Error in getInitialSession:', error);
      } finally {
        setLoading(false);
      }
    };

    // Add timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      console.warn('Loading timeout reached, stopping loading state');
      setLoading(false);
    }, 10000); // 10 second timeout

    getInitialSession().then(() => {
      clearTimeout(loadingTimeout);
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);

        try {
          setSession(session);

          if (session?.user) {
            // Don't wait for profile fetch to complete - do it in background
            fetchUserProfile(session.user.id).catch(error => {
              console.error('Background profile fetch failed:', error);
            });
          } else {
            setUser(null);
          }
        } catch (error) {
          console.error('Error in auth state change:', error);
        } finally {
          // Set loading to false immediately, don't wait for profile
          setLoading(false);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const fetchUserProfile = async (userId) => {
    try {
      // Add timeout for profile fetching to prevent long waits
      const profileTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Profile fetch timeout')), 5000)
      );

      const profileFetch = async () => {
        // Check if we're in demo mode (no real Supabase tables)
        if (isDemoMode) {
          console.warn('Demo mode: Using mock user profile');
          setUser({
            id: userId,
            email: '<EMAIL>',
            businessName: 'Demo Business',
            businessType: 'Restaurant',
            location: 'New York, NY',
            city: 'New York',
            state: 'NY',
            zipCode: '10001',
            subscriptionTier: 'professional',
            subscriptionStatus: 'active',
            createdAt: new Date().toISOString()
          });
          return;
        }

        const { data, error } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();

        if (error) {
          console.error('Error fetching user profile:', error);

          // If table doesn't exist, use fallback profile
          if (error.message?.includes('Could not find the table') ||
            error.message?.includes('schema cache')) {
            console.warn('⚠️ Database tables not found, using fallback profile');
            setUser({
              id: userId,
              email: '<EMAIL>',
              businessName: 'Your Business',
              businessType: 'Business',
              location: 'Your Location',
              city: 'City',
              state: 'State',
              zipCode: '00000',
              subscriptionTier: 'starter',
              subscriptionStatus: 'active',
              createdAt: new Date().toISOString()
            });
            return;
          }
          throw error;
        }

        setUser({
          id: data.id,
          email: data.email,
          businessName: data.business_name,
          businessType: data.business_type,
          location: data.location,
          city: data.city,
          state: data.state,
          zipCode: data.zip_code,
          subscriptionTier: data.subscription_tier,
          subscriptionStatus: data.subscription_status,
          createdAt: data.created_at
        });
      };

      // Race between profile fetch and timeout
      await Promise.race([profileFetch(), profileTimeout]);

    } catch (error) {
      console.error('Error in fetchUserProfile:', error);

      // Always set a fallback user to prevent infinite loading
      setUser({
        id: userId,
        email: '<EMAIL>',
        businessName: 'Your Business',
        businessType: 'Business',
        location: 'Your Location',
        city: 'City',
        state: 'State',
        zipCode: '00000',
        subscriptionTier: 'starter',
        subscriptionStatus: 'active',
        createdAt: new Date().toISOString()
      });

      if (error.message !== 'Profile fetch timeout') {
        toast.error('Using fallback profile - please set up database for full features');
      }
    }
  };

  const register = async (userData) => {
    try {
      setLoading(true);

      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      // Set session from registration response
      if (data.session) {
        setSession(data.session);
        await fetchUserProfile(data.user.id);
      }

      toast.success('Registration successful! Welcome to LocalPost.ai');
      return { success: true, user: data.user };

    } catch (error) {
      console.error('Registration error:', error);
      toast.error(error.message || 'Registration failed');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  };

  const login = async (email, password) => {
    try {
      setLoading(true);

      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      // Set session immediately for faster UI response
      if (data.session) {
        setSession(data.session);

        // Start profile fetch in background, don't wait for it
        if (data.user) {
          fetchUserProfile(data.user.id).catch(error => {
            console.error('Background profile fetch during login failed:', error);
          });
        }
      }

      toast.success('Login successful!');
      return { success: true };

    } catch (error) {
      console.error('Login error:', error);
      toast.error(error.message || 'Login failed');
      return { success: false, error: error.message };
    } finally {
      // Set loading to false immediately, don't wait for profile
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setLoading(true);

      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      setUser(null);
      setSession(null);
      toast.success('Logged out successfully');

    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed');
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates) => {
    try {
      if (!user) throw new Error('No user logged in');

      const { error } = await supabase
        .from('users')
        .update(updates)
        .eq('id', user.id);

      if (error) throw error;

      // Update local user state
      setUser(prev => ({ ...prev, ...updates }));
      toast.success('Profile updated successfully');

      return { success: true };

    } catch (error) {
      console.error('Profile update error:', error);
      toast.error(error.message || 'Profile update failed');
      return { success: false, error: error.message };
    }
  };

  const getAuthHeaders = () => {
    if (!session?.access_token) {
      // In development mode, use test token when no session
      if (process.env.NODE_ENV === 'development' || isDemoMode) {
        return {
          'Authorization': 'Bearer test-token',
          'Content-Type': 'application/json',
        };
      }
      return {
        'Content-Type': 'application/json',
      };
    }

    return {
      'Authorization': `Bearer ${session.access_token}`,
      'Content-Type': 'application/json',
    };
  };

  const apiCall = async (url, options = {}) => {
    const headers = {
      ...getAuthHeaders(),
      ...options.headers,
    };

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (response.status === 401) {
      // Token expired, try to refresh
      const { data, error } = await supabase.auth.refreshSession();

      if (error || !data.session) {
        await logout();
        throw new Error('Session expired. Please log in again.');
      }

      // Retry with new token
      const newHeaders = {
        'Authorization': `Bearer ${data.session.access_token}`,
        'Content-Type': 'application/json',
        ...options.headers,
      };

      return fetch(url, {
        ...options,
        headers: newHeaders,
      });
    }

    return response;
  };

  const value = {
    user,
    session,
    loading,
    register,
    login,
    logout,
    updateProfile,
    getAuthHeaders,
    apiCall,
    supabase
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
