const { ApifyClient } = require('apify-client');
require('dotenv').config();

async function testLinkedInActors() {
  console.log('🔍 Testing LinkedIn Actors\n');

  const client = new ApifyClient({
    token: process.env.APIFY_API_TOKEN,
  });

  // List of potential LinkedIn actors to try
  const linkedinActors = [
    'apify/linkedin-company-scraper',
    'apify/linkedin-scraper',
    'drobnikj/linkedin-company-scraper',
    'lukaskrivka/linkedin-scraper',
    'apify/linkedin-posts-scraper'
  ];

  console.log('🧪 Testing LinkedIn Actors...\n');

  for (const actorId of linkedinActors) {
    try {
      console.log(`Testing: ${actorId}`);
      
      // Try to get actor info
      const actor = await client.actor(actorId).get();
      
      if (actor) {
        console.log(`✅ FOUND: ${actorId}`);
        console.log(`   Name: ${actor.name}`);
        console.log(`   Description: ${actor.description?.substring(0, 100)}...`);
        console.log(`   Stats: ${actor.stats?.totalRuns || 0} total runs`);
        console.log(`   Modified: ${actor.modifiedAt}`);
        
        // Try a quick test run with minimal input
        console.log(`   🧪 Testing with minimal input...`);
        
        try {
          const run = await client.actor(actorId).call({
            startUrls: [{ url: 'https://www.linkedin.com/company/microsoft/' }],
            maxPosts: 3
          }, {
            timeout: 60, // 1 minute timeout for test
            memory: 512
          });
          
          console.log(`   ✅ Test run successful! Status: ${run.status}`);
          
          if (run.status === 'SUCCEEDED') {
            const { items } = await client.dataset(run.defaultDatasetId).listItems();
            console.log(`   📊 Returned ${items.length} items`);
            
            if (items.length > 0) {
              console.log(`   🎉 WORKING ACTOR FOUND: ${actorId}`);
              console.log(`   Sample data keys:`, Object.keys(items[0]));
              
              // Show sample content
              if (items[0].text || items[0].content) {
                console.log(`   Sample content: "${(items[0].text || items[0].content).substring(0, 80)}..."`);
              }
              
              break; // Found a working actor
            }
          }
          
        } catch (runError) {
          console.log(`   ❌ Test run failed: ${runError.message}`);
        }
        
      } else {
        console.log(`❌ Actor not found: ${actorId}`);
      }
      
    } catch (error) {
      console.log(`❌ Error with ${actorId}: ${error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  // Also test Instagram and Twitter while we're at it
  console.log('🔍 Quick Test of Other Platforms...\n');
  
  const otherActors = [
    { platform: 'Instagram', actor: 'apify/instagram-scraper' },
    { platform: 'Twitter', actor: 'apify/twitter-scraper' }
  ];

  for (const { platform, actor: actorId } of otherActors) {
    try {
      console.log(`Testing ${platform}: ${actorId}`);
      const actor = await client.actor(actorId).get();
      
      if (actor) {
        console.log(`✅ ${platform} actor exists and is available`);
        console.log(`   Total runs: ${actor.stats?.totalRuns || 0}`);
      } else {
        console.log(`❌ ${platform} actor not found`);
      }
    } catch (error) {
      console.log(`❌ ${platform} actor error: ${error.message}`);
    }
    console.log('');
  }

  console.log('🎯 SUMMARY:');
  console.log('I will update the system with working actors for all platforms');
}

testLinkedInActors().catch(console.error);
