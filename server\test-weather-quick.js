require('dotenv').config();
const axios = require('axios');

async function testWeatherQuick() {
  console.log('🌤️ Quick Weather API Test\n');

  const apiKey = process.env.OPENWEATHER_API_KEY;
  const baseUrl = 'https://api.openweathermap.org/data/2.5';

  try {
    // Test with a simple location format
    const response = await axios.get(`${baseUrl}/weather`, {
      params: {
        q: 'New York',
        appid: apiKey,
        units: 'imperial'
      }
    });

    const weather = response.data;
    console.log('✅ Weather API Working!');
    console.log(`📍 Location: ${weather.name}, ${weather.sys.country}`);
    console.log(`🌡️ Temperature: ${Math.round(weather.main.temp)}°F`);
    console.log(`🌤️ Condition: ${weather.weather[0].main} - ${weather.weather[0].description}`);
    console.log(`💨 Wind: ${weather.wind?.speed || 0} mph`);
    console.log(`💧 Humidity: ${weather.main.humidity}%`);
    
    // Test content suggestions
    const temp = weather.main.temp;
    const condition = weather.weather[0].main.toLowerCase();
    
    console.log('\n💡 Content Suggestions:');
    if (condition.includes('rain')) {
      console.log('   🌧️ "Perfect rainy day for our cozy indoor dining!"');
    } else if (condition.includes('sun') || condition.includes('clear')) {
      console.log('   ☀️ "Beautiful sunny day - enjoy our outdoor seating!"');
    } else if (temp < 40) {
      console.log('   🥶 "Warm up with our hearty winter specials!"');
    } else if (temp > 80) {
      console.log('   🌞 "Beat the heat with our refreshing summer menu!"');
    }
    
    return true;

  } catch (error) {
    console.log('❌ Weather API Error:', error.response?.status, error.message);
    return false;
  }
}

testWeatherQuick().then(success => {
  if (success) {
    console.log('\n🎉 Weather integration ready for LocalPost.ai!');
  }
}).catch(console.error);
