// Force the new OpenAI API key and test content generation
const axios = require('axios');

// Force set the new API key
process.env.OPENAI_API_KEY = '********************************************************************************************************************************************************************';

const OpenAI = require('openai');

async function testForceNewKey() {
  console.log('🔑 Testing with Forced New OpenAI API Key\n');

  const apiKey = process.env.OPENAI_API_KEY;

  console.log('📋 API Key Check:');
  console.log(`   Length: ${apiKey.length} characters`);
  console.log(`   Starts with: ${apiKey.substring(0, 8)}`);
  console.log(`   Ends with: ${apiKey.substring(apiKey.length - 8)}`);
  console.log(`   Format: ${apiKey.startsWith('sk-') ? '✅ Correct' : '❌ Invalid'}`);

  if (apiKey.substring(apiKey.length - 8) !== '7MeY5vEA') {
    console.log('❌ Still using wrong API key!');
    console.log(`   Expected ending: 7MeY5vEA`);
    console.log(`   Actual ending: ${apiKey.substring(apiKey.length - 8)}`);
    return false;
  }

  console.log('✅ Using the correct new API key!');

  console.log('\n🧪 Testing OpenAI API Connection...');

  try {
    const openai = new OpenAI({ apiKey });

    // Test simple API call
    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "user",
          content: "Say 'Hello World'"
        }
      ],
      max_tokens: 10,
      temperature: 0
    });

    console.log('🎉 SUCCESS! OpenAI API is working with new key!');
    console.log('📝 Response:', completion.choices[0].message.content);

    // Test content generation for LocalPost.ai
    console.log('\n🎨 Testing LocalPost.ai Content Generation...');

    const contentCompletion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert social media content creator for local businesses. Always respond with valid JSON only."
        },
        {
          role: "user",
          content: `Create a Facebook post for a restaurant called "Demo Restaurant" in New York, NY. 
                   Current weather: sunny, 71°F. Make it engaging, weather-aware, and locally relevant.
                   Return ONLY valid JSON with this exact format:
                   {"text": "post content here", "hashtags": ["tag1", "tag2", "tag3"], "imagePrompt": "detailed image description"}`
        }
      ],
      max_tokens: 400,
      temperature: 0.7,
    });

    const aiContent = contentCompletion.choices[0].message.content;
    console.log('✅ Real AI content generation successful!');

    try {
      const parsedContent = JSON.parse(aiContent);
      console.log('\n🎯 AI-Generated Content for LocalPost.ai:');
      console.log('📄 Text:', parsedContent.text);
      console.log('🏷️  Hashtags:', parsedContent.hashtags?.join(', '));
      console.log('🖼️  Image Prompt:', parsedContent.imagePrompt);

      console.log('\n🌟 This is what your users will get instead of mock data!');

    } catch (parseError) {
      console.log('📝 Raw AI Response:', aiContent);
      console.log('⚠️  JSON parsing needs adjustment, but AI generation works!');
    }

    return true;

  } catch (error) {
    console.log('❌ API Error:', error.message);

    if (error.status === 401) {
      console.log('\n🔍 This means the API key is still invalid');
      console.log('💡 The key might not be activated yet or there\'s a billing issue');
    }

    return false;
  }
}

// Test the content generation endpoint with forced key
async function testContentEndpointWithForcedKey() {
  console.log('\n🌐 Testing Content Generation Endpoint with Forced Key...');

  try {
    const generateData = {
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      platforms: ['facebook', 'instagram'],
      regenerate: true
    };

    const response = await axios.post('http://localhost:5000/api/content/generate', generateData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 60000
    });

    console.log('✅ Content generation endpoint successful!');
    console.log(`🎉 Generated ${response.data.generatedPosts?.length || 0} posts!`);

    if (response.data.generatedPosts?.length > 0) {
      const post = response.data.generatedPosts[0];
      console.log('\n📱 Sample Generated Post:');
      console.log(`   🎯 Platform: ${post.platform}`);
      console.log(`   📄 Content: ${post.content_text?.substring(0, 100)}...`);
      console.log(`   🏷️  Hashtags: ${post.hashtags?.join(', ') || 'None'}`);
      console.log(`   🌤️  Weather: ${post.weather_context || 'None'}`);

      // Check if it's real AI content
      const isAIGenerated = post.content_text &&
        post.content_text.length > 100 &&
        !post.content_text.includes('Demo Restaurant, we\'re passionate');

      console.log(`\n🤖 Content Type: ${isAIGenerated ? '✅ Real AI Generated!' : '⚠️  Mock/Fallback Data'}`);
    }

    if (response.data.errors?.length > 0) {
      console.log('\n⚠️  Generation Errors:');
      response.data.errors.forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`);
      });
    }

    return true;

  } catch (error) {
    console.log('❌ Content endpoint test failed:', error.message);
    return false;
  }
}

// Run tests
async function runTests() {
  const directApiWorking = await testForceNewKey();

  if (directApiWorking) {
    console.log('\n🎯 Direct OpenAI API is working with new key!');

    // Now test the server endpoint
    const endpointWorking = await testContentEndpointWithForcedKey();

    if (endpointWorking) {
      console.log('\n🎉 AMAZING! Both direct API and server endpoint are working!');
      console.log('🚀 Your "Failed to generate content" issue should be resolved!');
    } else {
      console.log('\n⚠️  Server endpoint still has issues - may need server restart');
    }
  } else {
    console.log('\n❌ Direct OpenAI API still not working with new key');
    console.log('🔍 Check if the new API key is activated and billing is set up');
  }
}

runTests().catch(console.error);
