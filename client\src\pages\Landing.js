import React from 'react';
import { Link } from 'react-router-dom';
import { 
  MapPin, 
  Cloud, 
  Calendar, 
  Zap, 
  Users, 
  TrendingUp,
  CheckCircle,
  ArrowRight
} from 'lucide-react';

const Landing = () => {
  const features = [
    {
      icon: MapPin,
      title: 'Hyper-Local Content',
      description: 'Weather-aware, community-relevant posts that resonate with your local audience'
    },
    {
      icon: Cloud,
      title: 'AI-Powered Generation',
      description: 'Advanced AI creates authentic content that matches your brand voice and style'
    },
    {
      icon: Calendar,
      title: '30-Day Content Calendar',
      description: 'Complete monthly content planning with scheduling and optimization'
    },
    {
      icon: Zap,
      title: 'Multi-Platform Ready',
      description: 'Optimized content for Facebook, Instagram, LinkedIn, and Twitter'
    },
    {
      icon: Users,
      title: 'Brand Voice Learning',
      description: 'Analyzes your existing content to maintain consistent brand messaging'
    },
    {
      icon: TrendingUp,
      title: 'Local Event Integration',
      description: 'Automatically incorporates local events and community happenings'
    }
  ];

  const pricingPlans = [
    {
      name: 'Starter',
      price: '$49',
      period: '/month',
      description: 'Perfect for single location businesses',
      features: [
        '1 business location',
        '30 posts per month',
        '2 social platforms',
        'Basic analytics',
        'Email support'
      ],
      popular: false
    },
    {
      name: 'Professional',
      price: '$99',
      period: '/month',
      description: 'Ideal for growing businesses',
      features: [
        '3 business locations',
        '90 posts per month',
        'All social platforms',
        'Advanced analytics',
        'Priority support',
        'Custom brand templates'
      ],
      popular: true
    },
    {
      name: 'Agency',
      price: '$199',
      period: '/month',
      description: 'For agencies and multi-location businesses',
      features: [
        '10 business locations',
        '300 posts per month',
        'White-label option',
        'API access',
        'Dedicated account manager',
        'Custom integrations'
      ],
      popular: false
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">LP</span>
              </div>
              <span className="text-xl font-bold text-gray-900">LocalPost.ai</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link
                to="/login"
                className="text-gray-600 hover:text-gray-900 font-medium"
              >
                Login
              </Link>
              <Link
                to="/register"
                className="btn-primary"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-br from-primary-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Hyper-Local Social Media
              <span className="text-primary-600 block">Made Simple</span>
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Generate 30 days of location-specific, weather-aware, community-relevant 
              social media content for your local business in minutes, not hours.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/register"
                className="btn-primary text-lg px-8 py-3 flex items-center justify-center space-x-2"
              >
                <span>Start Free Trial</span>
                <ArrowRight size={20} />
              </Link>
              
              <button className="btn-secondary text-lg px-8 py-3">
                Watch Demo
              </button>
            </div>
            
            <p className="text-sm text-gray-500 mt-4">
              7-day free trial • No credit card required
            </p>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Everything You Need for Local Success
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Our AI understands your local market and creates content that connects 
              with your community.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="card text-center">
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon className="text-primary-600" size={24} />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Simple, Transparent Pricing
            </h2>
            <p className="text-xl text-gray-600">
              Choose the plan that fits your business needs
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {pricingPlans.map((plan, index) => (
              <div 
                key={index} 
                className={`card relative ${plan.popular ? 'ring-2 ring-primary-500' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary-600 text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="flex items-baseline justify-center">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600 ml-1">{plan.period}</span>
                  </div>
                  <p className="text-gray-600 mt-2">{plan.description}</p>
                </div>
                
                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <CheckCircle className="text-green-500 mr-3" size={16} />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Link
                  to="/register"
                  className={`w-full text-center py-3 px-4 rounded-lg font-medium transition-colors ${
                    plan.popular
                      ? 'bg-primary-600 hover:bg-primary-700 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-900'
                  }`}
                >
                  Get Started
                </Link>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Ready to Transform Your Social Media?
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            Join hundreds of local businesses already using LocalPost.ai to grow their online presence.
          </p>
          
          <Link
            to="/register"
            className="bg-white text-primary-600 hover:bg-gray-50 font-bold py-4 px-8 rounded-lg text-lg transition-colors inline-flex items-center space-x-2"
          >
            <span>Start Your Free Trial</span>
            <ArrowRight size={20} />
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">LP</span>
              </div>
              <span className="text-xl font-bold">LocalPost.ai</span>
            </div>
            
            <p className="text-gray-400 mb-4">
              AI-powered local social media content generation
            </p>
            
            <p className="text-gray-500 text-sm">
              © 2024 LocalPost.ai. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Landing;
