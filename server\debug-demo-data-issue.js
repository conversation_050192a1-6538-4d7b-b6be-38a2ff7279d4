const axios = require('axios');

async function debugDemoDataIssue() {
  console.log('🔍 Debugging Demo Data Issue\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  try {
    console.log('🧪 Testing Facebook (should be real data)...');
    
    const facebookResponse = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      options: { limit: 10 }
    });

    if (facebookResponse.data.success) {
      const data = facebookResponse.data.data;
      console.log('📊 Facebook Response Analysis:');
      console.log(`   Total Posts: ${data.totalPosts}`);
      console.log(`   Scraping Method: ${data.scrapedWith}`);
      console.log(`   Is Demo: ${data.isDemo || 'Not specified'}`);
      console.log(`   Platform: ${data.platform}`);
      
      console.log('\n📝 Post Content Analysis:');
      data.posts.forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      Content: "${post.content.substring(0, 80)}..."`);
        console.log(`      Scraped With: ${post.scrapedWith || 'Not specified'}`);
        console.log(`      Is Demo: ${post.isDemo || 'Not specified'}`);
        console.log(`      Has Real Paya Content: ${post.content.includes('Paya') ? 'YES ✅' : 'NO ❌'}`);
        console.log(`      Media URL: ${post.media?.url ? 'Present' : 'None'}`);
        console.log(`      Media Type: ${post.media?.type || 'None'}`);
      });

      // Check if we're getting real Paya content or demo content
      const hasRealPayaContent = data.posts.some(post => 
        post.content.includes('Paya') || 
        post.content.includes('Buy Now Pay Later') ||
        post.content.includes('Financial Companion')
      );

      console.log(`\n🎯 Facebook Analysis Result:`);
      if (hasRealPayaContent && data.scrapedWith === 'Apify') {
        console.log('   ✅ SUCCESS: Getting real Paya Finance content via Apify');
      } else if (data.scrapedWith.includes('Fallback') || data.scrapedWith.includes('Demo')) {
        console.log('   ❌ ISSUE: Facebook is using demo/fallback data instead of Apify');
        console.log('   🔧 Problem: Apify integration is not working properly');
      } else {
        console.log('   ⚠️  UNCLEAR: Mixed signals in the data');
      }

    } else {
      console.log('❌ Facebook request failed:', facebookResponse.data.error);
    }

    console.log('\n' + '='.repeat(60));

    console.log('\n🧪 Testing Instagram (should be real data)...');
    
    const instagramResponse = await axios.post(`${baseURL}/scrape-posts`, {
      platform: 'Instagram',
      url: 'https://www.instagram.com/microsoft/',
      options: { limit: 10 }
    });

    if (instagramResponse.data.success) {
      const data = instagramResponse.data.data;
      console.log('📊 Instagram Response Analysis:');
      console.log(`   Total Posts: ${data.totalPosts}`);
      console.log(`   Scraping Method: ${data.scrapedWith}`);
      console.log(`   Is Demo: ${data.isDemo || 'Not specified'}`);
      console.log(`   Platform: ${data.platform}`);
      
      console.log('\n📝 Post Content Analysis:');
      data.posts.forEach((post, index) => {
        console.log(`\n   Post ${index + 1}:`);
        console.log(`      Content: "${post.content.substring(0, 80)}..."`);
        console.log(`      Scraped With: ${post.scrapedWith || 'Not specified'}`);
        console.log(`      Is Demo: ${post.isDemo || 'Not specified'}`);
        console.log(`      Media URL: ${post.media?.url ? 'Present' : 'None'}`);
      });

      console.log(`\n🎯 Instagram Analysis Result:`);
      if (data.scrapedWith === 'Apify') {
        console.log('   ✅ SUCCESS: Instagram using real Apify integration');
      } else if (data.scrapedWith.includes('Fallback') || data.scrapedWith.includes('Demo')) {
        console.log('   ❌ ISSUE: Instagram is using demo/fallback data instead of Apify');
        console.log('   🔧 Problem: Apify integration is not working properly');
      } else {
        console.log('   ⚠️  UNCLEAR: Mixed signals in the data');
      }

    } else {
      console.log('❌ Instagram request failed:', instagramResponse.data.error);
    }

  } catch (error) {
    console.error('❌ Debug Error:', error.response?.data || error.message);
  }

  console.log('\n🔧 DIAGNOSIS:');
  console.log('If both platforms are showing demo data, the issue is likely:');
  console.log('1. Apify API token not working properly');
  console.log('2. Apify actors failing and falling back to demo data');
  console.log('3. Network/timeout issues with Apify service');
  console.log('4. Recent changes broke the Apify integration');
  
  console.log('\n💡 NEXT STEPS:');
  console.log('1. Check Apify API token configuration');
  console.log('2. Test Apify connection directly');
  console.log('3. Check server logs for Apify errors');
  console.log('4. Verify actor configurations are correct');
}

debugDemoDataIssue().catch(console.error);
