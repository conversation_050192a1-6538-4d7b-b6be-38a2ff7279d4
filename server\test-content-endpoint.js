const axios = require('axios');

async function testContentEndpoint() {
  console.log('🧪 Testing Content Generation Endpoint\n');

  const baseUrl = 'http://localhost:5000';
  
  try {
    // First, test if the server is responding
    console.log('1️⃣ Testing server health...');
    const healthResponse = await axios.get(`${baseUrl}/api/health`);
    console.log('✅ Server is responding:', healthResponse.status);
  } catch (healthError) {
    console.log('❌ Server health check failed:', healthError.message);
    return;
  }

  try {
    // Test the content generation endpoint
    console.log('\n2️⃣ Testing content generation endpoint...');
    
    const generateData = {
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      platforms: ['facebook', 'instagram'],
      regenerate: true
    };

    console.log('📤 Sending request:', JSON.stringify(generateData, null, 2));

    const response = await axios.post(`${baseUrl}/api/content/generate`, generateData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer mock-token' // Using mock token for testing
      },
      timeout: 30000 // 30 second timeout
    });

    console.log('✅ Content generation successful!');
    console.log('📊 Response status:', response.status);
    console.log('📝 Response data:', JSON.stringify(response.data, null, 2));

    if (response.data.generatedPosts) {
      console.log(`\n🎉 Generated ${response.data.generatedPosts.length} posts!`);
      
      response.data.generatedPosts.forEach((post, index) => {
        console.log(`\n📱 Post ${index + 1} (${post.platform}):`);
        console.log(`   📄 Content: ${post.content_text?.substring(0, 100)}...`);
        console.log(`   🏷️  Hashtags: ${post.hashtags?.join(', ') || 'None'}`);
        console.log(`   📅 Date: ${post.date_scheduled}`);
        console.log(`   🌤️  Weather: ${post.weather_context || 'None'}`);
      });
    }

  } catch (error) {
    console.log('\n❌ Content generation failed!');
    console.log('📊 Status:', error.response?.status || 'Network Error');
    console.log('📝 Error message:', error.message);
    
    if (error.response?.data) {
      console.log('🔍 Error details:', JSON.stringify(error.response.data, null, 2));
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.log('🔧 Server is not running on port 5000');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('⏱️  Request timed out - content generation takes time');
    }
  }
}

// Test with authentication
async function testWithAuth() {
  console.log('\n3️⃣ Testing with authentication...');
  
  try {
    // Try to login first
    const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
      email: '<EMAIL>',
      password: 'demo123'
    });
    
    const token = loginResponse.data.token;
    console.log('✅ Login successful, got token');
    
    // Now try content generation with real auth
    const generateData = {
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      platforms: ['facebook', 'instagram'],
      regenerate: true
    };

    const response = await axios.post('http://localhost:5000/api/content/generate', generateData, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      timeout: 30000
    });

    console.log('✅ Authenticated content generation successful!');
    console.log(`🎉 Generated ${response.data.generatedPosts?.length || 0} posts with real auth!`);
    
  } catch (authError) {
    console.log('❌ Authentication test failed:', authError.message);
    if (authError.response?.data) {
      console.log('🔍 Auth error details:', JSON.stringify(authError.response.data, null, 2));
    }
  }
}

// Run the tests
async function runTests() {
  await testContentEndpoint();
  await testWithAuth();
  
  console.log('\n🎯 Test Summary:');
  console.log('If content generation is failing in the UI:');
  console.log('1. Check browser console for errors');
  console.log('2. Verify authentication token is valid');
  console.log('3. Check network tab for failed requests');
  console.log('4. Ensure server is running on port 5000');
}

runTests().catch(console.error);
