import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { usePosts } from '../contexts/PostsContext';
import {
  Calendar,
  TrendingUp,
  Users,
  Zap,
  Plus,
  ArrowRight,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';

const Dashboard = () => {
  const { user, apiCall } = useAuth();
  const { posts, addPostsToCalendar, loading: postsLoading } = usePosts();
  const [stats, setStats] = useState({
    totalPosts: 0,
    postsThisMonth: 0,
    scheduledPosts: 0,
    completedPosts: 0
  });
  const [recentPosts, setRecentPosts] = useState([]);
  const [todaysPosts, setTodaysPosts] = useState([]);
  const [tomorrowsPosts, setTomorrowsPosts] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  // Update dashboard data when posts change in context
  useEffect(() => {
    if (posts.length > 0) {
      // Get today's and tomorrow's dates
      const today = new Date().toISOString().split('T')[0];
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

      // Filter posts for today and tomorrow
      const todayPosts = posts.filter(post => {
        const postDate = new Date(post.scheduledDate).toISOString().split('T')[0];
        return postDate === today;
      });

      const tomorrowPosts = posts.filter(post => {
        const postDate = new Date(post.scheduledDate).toISOString().split('T')[0];
        return postDate === tomorrow;
      });

      setRecentPosts(posts.slice(0, 5)); // Show 5 most recent
      setTodaysPosts(todayPosts);
      setTomorrowsPosts(tomorrowPosts);

      setStats({
        totalPosts: posts.length,
        postsThisMonth: posts.length,
        scheduledPosts: posts.filter(p => p.status === 'generated' || p.status === 'approved').length,
        completedPosts: posts.filter(p => p.status === 'posted').length
      });
    }
  }, [posts]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // Get current month date range
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Fetch posts for current month
      const response = await apiCall(`/api/content/posts?startDate=${startOfMonth.toISOString().split('T')[0]}&endDate=${endOfMonth.toISOString().split('T')[0]}`);

      if (response.ok) {
        const data = await response.json();
        const apiPosts = data.posts || [];

        // Convert API posts to calendar format and add to PostsContext
        const calendarPosts = apiPosts.map(post => ({
          id: post.id,
          title: (post.content_text || '').split('\n')[0].substring(0, 50) + '...',
          content: post.content_text || '',
          platform: post.platform,
          scheduledDate: post.date_scheduled,
          status: post.status || 'generated',
          hashtags: post.hashtags || [],
          weather: post.weather_context || '',
          localEvent: post.events_context || '',
          imageDesign: post.image_url ? { mockUrl: post.image_url } : null,
          createdAt: post.created_at,
          updatedAt: post.updated_at
        }));

        // Add posts to PostsContext for calendar integration
        await addPostsToCalendar(calendarPosts);

        // Get today's and tomorrow's dates
        const today = new Date().toISOString().split('T')[0];
        const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split('T')[0];

        // Filter posts for today and tomorrow
        const todayPosts = calendarPosts.filter(post => {
          const postDate = new Date(post.scheduledDate).toISOString().split('T')[0];
          return postDate === today;
        });

        const tomorrowPosts = calendarPosts.filter(post => {
          const postDate = new Date(post.scheduledDate).toISOString().split('T')[0];
          return postDate === tomorrow;
        });

        setRecentPosts(calendarPosts.slice(0, 5)); // Show 5 most recent
        setTodaysPosts(todayPosts);
        setTomorrowsPosts(tomorrowPosts);

        setStats({
          totalPosts: calendarPosts.length,
          postsThisMonth: calendarPosts.length,
          scheduledPosts: calendarPosts.filter(p => p.status === 'generated' || p.status === 'approved').length,
          completedPosts: calendarPosts.filter(p => p.status === 'posted').length
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateContent = async () => {
    try {
      setLoading(true);
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + 30); // Next 30 days

      const response = await apiCall('/api/content/generate', {
        method: 'POST',
        body: JSON.stringify({
          startDate: startDate.toISOString().split('T')[0],
          endDate: endDate.toISOString().split('T')[0],
          platforms: ['facebook', 'instagram'],
          regenerate: false
        })
      });

      if (response.ok) {
        const data = await response.json();
        console.log('Content generated:', data);
        // Refresh dashboard data to show new posts
        await fetchDashboardData();
      }
    } catch (error) {
      console.error('Error generating content:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <LoadingSpinner text="Loading dashboard..." />;
  }

  const statCards = [
    {
      title: 'Total Posts',
      value: stats.totalPosts,
      icon: Calendar,
      color: 'bg-blue-500',
      change: '+12%'
    },
    {
      title: 'This Month',
      value: stats.postsThisMonth,
      icon: TrendingUp,
      color: 'bg-green-500',
      change: '+8%'
    },
    {
      title: 'Scheduled',
      value: stats.scheduledPosts,
      icon: Clock,
      color: 'bg-yellow-500',
      change: null
    },
    {
      title: 'Completed',
      value: stats.completedPosts,
      icon: CheckCircle,
      color: 'bg-purple-500',
      change: null
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.businessName}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here's what's happening with your social media content.
        </p>
      </div>

      {/* Quick Actions */}
      <div className="mb-8">
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={generateContent}
            className="btn-primary flex items-center space-x-2 px-6 py-3"
          >
            <Zap size={20} />
            <span>Generate Content</span>
          </button>

          <Link
            to="/calendar"
            className="btn-secondary flex items-center space-x-2 px-6 py-3"
          >
            <Calendar size={20} />
            <span>View Calendar</span>
          </Link>

          <Link
            to="/profile"
            className="btn-secondary flex items-center space-x-2 px-6 py-3"
          >
            <Users size={20} />
            <span>Update Profile</span>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="card">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  {stat.change && (
                    <p className="text-sm text-green-600 mt-1">{stat.change} from last month</p>
                  )}
                </div>
                <div className={`w-12 h-12 ${stat.color} rounded-lg flex items-center justify-center`}>
                  <Icon className="text-white" size={24} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Post of the Day & Next Day */}
      <div className="grid md:grid-cols-2 gap-6 mb-8">
        {/* Post of the Day */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <Calendar className="text-blue-600" size={20} />
              <span>Post of the Day</span>
            </h2>
            <span className="text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'short',
                day: 'numeric'
              })}
            </span>
          </div>

          {todaysPosts.length > 0 ? (
            <div className="space-y-3">
              {todaysPosts.map((post) => (
                <div key={post.id} className="border border-gray-200 rounded-lg p-4 bg-blue-50">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-blue-900 capitalize">
                      {post.platform}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${post.status === 'generated' ? 'bg-blue-100 text-blue-800' :
                      post.status === 'approved' ? 'bg-green-100 text-green-800' :
                        post.status === 'posted' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                      }`}>
                      {post.status}
                    </span>
                  </div>
                  <p className="text-gray-700 text-sm line-clamp-3">
                    {post.content_text || post.content}
                  </p>
                  {post.hashtags && post.hashtags.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {post.hashtags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Calendar className="mx-auto text-gray-400 mb-2" size={32} />
              <p className="text-sm">No posts scheduled for today</p>
              <Link
                to="/generate"
                className="text-primary-600 hover:text-primary-700 text-sm font-medium mt-1 inline-block"
              >
                Generate content →
              </Link>
            </div>
          )}
        </div>

        {/* Next Day Posts */}
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <Clock className="text-green-600" size={20} />
              <span>Tomorrow's Posts</span>
            </h2>
            <span className="text-sm text-gray-500">
              {new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'short',
                day: 'numeric'
              })}
            </span>
          </div>

          {tomorrowsPosts.length > 0 ? (
            <div className="space-y-3">
              {tomorrowsPosts.map((post) => (
                <div key={post.id} className="border border-gray-200 rounded-lg p-4 bg-green-50">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium text-green-900 capitalize">
                      {post.platform}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded-full ${post.status === 'generated' ? 'bg-blue-100 text-blue-800' :
                      post.status === 'approved' ? 'bg-green-100 text-green-800' :
                        post.status === 'posted' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                      }`}>
                      {post.status}
                    </span>
                  </div>
                  <p className="text-gray-700 text-sm line-clamp-3">
                    {post.content_text || post.content}
                  </p>
                  {post.hashtags && post.hashtags.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {post.hashtags.slice(0, 3).map((tag, index) => (
                        <span key={index} className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Clock className="mx-auto text-gray-400 mb-2" size={32} />
              <p className="text-sm">No posts scheduled for tomorrow</p>
              <Link
                to="/generate"
                className="text-primary-600 hover:text-primary-700 text-sm font-medium mt-1 inline-block"
              >
                Generate content →
              </Link>
            </div>
          )}
        </div>
      </div>

      <div className="grid lg:grid-cols-3 gap-8">
        {/* Recent Posts */}
        <div className="lg:col-span-2">
          <div className="card">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Recent Posts</h2>
              <Link
                to="/calendar"
                className="text-primary-600 hover:text-primary-700 flex items-center space-x-1"
              >
                <span>View all</span>
                <ArrowRight size={16} />
              </Link>
            </div>

            {recentPosts.length > 0 ? (
              <div className="space-y-4">
                {recentPosts.map((post) => (
                  <div key={post.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-sm font-medium text-gray-900 capitalize">
                            {post.platform}
                          </span>
                          <span className="text-sm text-gray-500">
                            {new Date(post.scheduledDate || post.date_scheduled).toLocaleDateString()}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full ${post.status === 'generated' ? 'bg-blue-100 text-blue-800' :
                            post.status === 'approved' ? 'bg-green-100 text-green-800' :
                              post.status === 'posted' ? 'bg-purple-100 text-purple-800' :
                                'bg-gray-100 text-gray-800'
                            }`}>
                            {post.status}
                          </span>
                        </div>
                        <p className="text-gray-700 text-sm line-clamp-2">
                          {post.content || post.content_text}
                        </p>
                        {post.hashtags && post.hashtags.length > 0 && (
                          <div className="mt-2">
                            <div className="flex flex-wrap gap-1">
                              {post.hashtags.slice(0, 3).map((tag, index) => (
                                <span key={index} className="text-xs text-primary-600">
                                  {tag}
                                </span>
                              ))}
                              {post.hashtags.length > 3 && (
                                <span className="text-xs text-gray-500">
                                  +{post.hashtags.length - 3} more
                                </span>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                      {(post.imageDesign?.mockUrl || post.image_url) && (
                        <div className="ml-4">
                          <img
                            src={post.imageDesign?.mockUrl || post.image_url}
                            alt="Post image"
                            className="w-16 h-16 object-cover rounded-lg"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Calendar className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by generating your first batch of content.
                </p>
                <div className="mt-6">
                  <button
                    onClick={generateContent}
                    className="btn-primary flex items-center space-x-2 mx-auto"
                  >
                    <Plus size={16} />
                    <span>Generate Content</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Account Info & Tips */}
        <div className="space-y-6">
          {/* Account Status */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Status</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Plan</span>
                <span className="text-sm font-medium text-gray-900 capitalize">
                  {user?.subscriptionTier}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Status</span>
                <span className={`text-sm font-medium ${user?.subscriptionStatus === 'active' ? 'text-green-600' : 'text-yellow-600'
                  }`}>
                  {user?.subscriptionStatus === 'trial' ? 'Free Trial' : user?.subscriptionStatus}
                </span>
              </div>
              <div className="pt-3 border-t border-gray-200">
                <Link
                  to="/subscription"
                  className="text-sm text-primary-600 hover:text-primary-700 flex items-center space-x-1"
                >
                  <span>Manage subscription</span>
                  <ArrowRight size={14} />
                </Link>
              </div>
            </div>
          </div>

          {/* Tips */}
          <div className="card">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Tips for Success</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="text-green-500 mt-0.5" size={16} />
                <div>
                  <p className="text-sm font-medium text-gray-900">Review & Edit</p>
                  <p className="text-xs text-gray-600">Always review generated content before posting</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <AlertCircle className="text-yellow-500 mt-0.5" size={16} />
                <div>
                  <p className="text-sm font-medium text-gray-900">Stay Consistent</p>
                  <p className="text-xs text-gray-600">Post regularly to maintain engagement</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <TrendingUp className="text-blue-500 mt-0.5" size={16} />
                <div>
                  <p className="text-sm font-medium text-gray-900">Track Performance</p>
                  <p className="text-xs text-gray-600">Monitor which posts perform best</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
