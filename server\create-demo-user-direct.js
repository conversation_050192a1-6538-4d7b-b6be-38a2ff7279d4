require('dotenv').config();
const { supabase } = require('./config/database');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

async function createDemoUserDirect() {
  console.log('👤 Creating demo user directly in database\n');

  const demoEmail = '<EMAIL>';
  const demoPassword = 'demo123';
  const userId = uuidv4(); // Generate a UUID for the user

  try {
    // Hash the password
    const hashedPassword = await bcrypt.hash(demoPassword, 10);

    // Create user record directly
    console.log('1️⃣ Creating user record...');
    
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        id: userId,
        email: demoEmail,
        password_hash: hashedPassword, // Store hashed password
        business_name: 'Demo Restaurant',
        business_type: 'restaurant',
        location: 'New York, NY',
        subscription_status: 'active',
        subscription_tier: 'starter',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (userError) {
      console.log('❌ User creation failed:', userError.message);
      return null;
    }

    console.log('✅ User created successfully!');
    console.log(`   👤 ID: ${userData.id}`);
    console.log(`   📧 Email: ${userData.email}`);
    console.log(`   🏢 Business: ${userData.business_name}`);

    // Create business profile
    console.log('\n2️⃣ Creating business profile...');
    
    const { data: businessData, error: businessError } = await supabase
      .from('business_profiles')
      .insert({
        user_id: userId,
        business_description: 'A cozy restaurant serving delicious food in the heart of New York City.',
        target_audience: 'Local food lovers, families, young professionals',
        brand_voice: 'friendly, welcoming, professional',
        primary_color: '#3b82f6',
        secondary_color: '#1e40af',
        visual_style: {
          tone: 'warm and inviting',
          imagery: 'food photography, restaurant ambiance',
          colors: ['#3b82f6', '#1e40af', '#f59e0b']
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (businessError) {
      console.log('⚠️  Business profile creation failed:', businessError.message);
      console.log('   (User can still login and use the app)');
    } else {
      console.log('✅ Business profile created successfully!');
    }

    // Create some sample posts
    console.log('\n3️⃣ Creating sample posts...');
    
    const samplePosts = [
      {
        user_id: userId,
        platform: 'facebook',
        content_text: '✨ At Demo Restaurant, we\'re passionate about providing excellent restaurant services to our New York, NY community. Visit us today!',
        hashtags: ['#NewYorkNY', '#restaurant', '#LocalBusiness'],
        date_scheduled: new Date().toISOString().split('T')[0],
        status: 'generated',
        weather_context: 'sunny, 71°F',
        created_at: new Date().toISOString()
      },
      {
        user_id: userId,
        platform: 'instagram',
        content_text: '📍 New York, NY | Demo Restaurant Where quality meets excellence in restaurant services 🌟',
        hashtags: ['#NewYorkNY', '#restaurant', '#LocalBusiness'],
        date_scheduled: new Date().toISOString().split('T')[0],
        status: 'generated',
        weather_context: 'sunny, 71°F',
        created_at: new Date().toISOString()
      }
    ];

    const { data: postsData, error: postsError } = await supabase
      .from('generated_posts')
      .insert(samplePosts)
      .select();

    if (postsError) {
      console.log('⚠️  Sample posts creation failed:', postsError.message);
    } else {
      console.log(`✅ Created ${postsData.length} sample posts`);
    }

    console.log('\n🎉 Demo user setup complete!');
    console.log('📋 Demo User Details:');
    console.log(`   📧 Email: ${demoEmail}`);
    console.log(`   🔑 Password: ${demoPassword}`);
    console.log(`   🏢 Business: Demo Restaurant`);
    console.log(`   📍 Location: New York, NY`);
    console.log(`   💳 Subscription: Active (Starter)`);

    return userData;

  } catch (error) {
    console.log('❌ Demo user creation failed:', error.message);
    return null;
  }
}

// Update the auth route to handle direct database authentication
async function updateAuthRoute() {
  console.log('\n🔧 Note: You may need to update the auth route to handle database authentication');
  console.log('The current auth system uses Supabase Auth, but we created a direct database user.');
  console.log('For testing, you can modify the login route to check the users table directly.');
}

// Run creation
async function runCreation() {
  const user = await createDemoUserDirect();
  
  if (user) {
    await updateAuthRoute();
    console.log('\n🚀 Demo user is ready!');
    console.log('Next steps:');
    console.log('1. The user exists in the database');
    console.log('2. You may need to update auth to handle direct DB users');
    console.log('3. Or create the user through Supabase Auth dashboard');
  }
}

runCreation().catch(console.error);
