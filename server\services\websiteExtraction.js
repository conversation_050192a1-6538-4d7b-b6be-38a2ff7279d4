const axios = require('axios');
const { JSDOM } = require('jsdom');

class WebsiteExtractionService {
  constructor() {
    this.timeout = 15000; // 15 second timeout
    this.maxContentLength = 500000; // Increased to 500KB for larger sites
    this.maxProcessingLength = 200000; // Process only first 200KB for performance
    this.essentialContentOnly = true; // Filter out non-essential content
  }

  // Main extraction method
  async extractBusinessData(url) {
    try {
      // Validate and normalize URL
      const normalizedUrl = this.normalizeUrl(url);

      // Fetch website content
      const html = await this.fetchWebsiteContent(normalizedUrl);

      // Parse HTML and extract data
      const extractedData = await this.parseWebsiteContent(html, normalizedUrl);

      // Use AI to enhance and structure the data
      const enhancedData = await this.enhanceWithAI(extractedData, html);

      return {
        success: true,
        data: enhancedData,
        sourceUrl: normalizedUrl
      };

    } catch (error) {
      console.error('Website extraction error:', error.message);

      return {
        success: false,
        error: error.message,
        data: null
      };
    }
  }

  // Normalize and validate URL
  normalizeUrl(url) {
    if (!url) {
      throw new Error('URL is required');
    }

    // Add protocol if missing
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }

    // Validate URL format
    try {
      new URL(url);
      return url;
    } catch (error) {
      throw new Error('Invalid URL format');
    }
  }

  // Fetch website content with proper headers and error handling
  async fetchWebsiteContent(url) {
    try {
      const response = await axios.get(url, {
        timeout: this.timeout,
        maxContentLength: this.maxContentLength,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1'
        },
        validateStatus: (status) => status < 400 // Accept redirects
      });

      if (!response.data) {
        throw new Error('No content received from website');
      }

      // Filter and optimize content if it's large
      let content = response.data;
      if (typeof content === 'string' && content.length > this.maxProcessingLength) {
        console.log(`Large content detected (${content.length} chars), filtering...`);
        content = this.filterEssentialContent(content);
      }

      return content;

    } catch (error) {
      if (error.code === 'ENOTFOUND') {
        throw new Error('Website not found. Please check the URL.');
      } else if (error.code === 'ECONNREFUSED') {
        throw new Error('Connection refused. The website may be down.');
      } else if (error.code === 'ETIMEDOUT') {
        throw new Error('Request timed out. The website is taking too long to respond.');
      } else if (error.response?.status === 403) {
        throw new Error('Access forbidden. The website is blocking automated requests.');
      } else if (error.response?.status === 404) {
        throw new Error('Page not found. Please check the URL.');
      } else if (error.response?.status >= 500) {
        throw new Error('Website server error. Please try again later.');
      } else if (error.message?.includes('maxContentLength') || error.message?.includes('content size')) {
        throw new Error('Website is too large to process automatically. Please try entering your business information manually or use a simpler page URL (like your About page).');
      } else if (error.message?.includes('exceeded')) {
        throw new Error('Website content is too large. Try using a specific page URL (like /about or /contact) instead of the homepage.');
      } else {
        throw new Error(`Failed to fetch website: ${error.message}`);
      }
    }
  }

  // Filter content to keep only essential HTML for business data extraction
  filterEssentialContent(html) {
    try {
      // Remove large script and style blocks first (before parsing)
      let filtered = html
        // Remove script tags and their content
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        // Remove style tags and their content
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
        // Remove comments
        .replace(/<!--[\s\S]*?-->/g, '')
        // Remove inline styles (keep the tags but remove style attributes)
        .replace(/\s+style\s*=\s*["'][^"']*["']/gi, '')
        // Remove large data attributes
        .replace(/\s+data-[a-zA-Z0-9-]*\s*=\s*["'][^"']*["']/gi, '');

      // If still too large, keep only essential sections
      if (filtered.length > this.maxProcessingLength) {
        const dom = new (require('jsdom').JSDOM)(filtered);
        const document = dom.window.document;

        // Remove non-essential elements
        const elementsToRemove = [
          'script', 'style', 'noscript', 'iframe', 'embed', 'object',
          'video', 'audio', 'canvas', 'svg', 'map', 'area'
        ];

        elementsToRemove.forEach(tag => {
          const elements = document.querySelectorAll(tag);
          elements.forEach(el => el.remove());
        });

        // Keep only essential content areas
        const essentialSelectors = [
          'head', 'title', 'meta',
          'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
          'p', 'div', 'span', 'section', 'article',
          'header', 'main', 'footer', 'nav',
          'ul', 'ol', 'li', 'dl', 'dt', 'dd',
          'a[href]', 'img[alt]',
          '[class*="about"]', '[class*="contact"]', '[class*="service"]',
          '[class*="business"]', '[class*="company"]', '[class*="brand"]'
        ];

        filtered = document.documentElement.outerHTML;

        // Final size check - truncate if still too large
        if (filtered.length > this.maxProcessingLength) {
          console.log(`Content still large after filtering, truncating to ${this.maxProcessingLength} chars`);
          filtered = filtered.substring(0, this.maxProcessingLength) + '</html>';
        }
      }

      console.log(`Content filtered: ${html.length} -> ${filtered.length} chars`);
      return filtered;

    } catch (error) {
      console.warn('Content filtering failed, using truncated original:', error.message);
      // Fallback: simple truncation
      return html.substring(0, this.maxProcessingLength);
    }
  }

  // Parse HTML content and extract business information
  parseWebsiteContent(html, url) {
    try {
      const dom = new JSDOM(html);
      const document = dom.window.document;

      // Extract basic information
      const title = this.extractTitle(document);
      const description = this.extractDescription(document);
      const businessName = this.extractBusinessName(document, title, url);
      const contact = this.extractContactInfo(document);
      const phone = this.extractPhone(document);
      const hours = this.extractHours(document);
      const servicesData = this.extractServices(document);
      const location = this.extractLocation(document);
      const industry = this.extractIndustry(document, title, description, servicesData.allServices);
      const targetAudience = this.extractTargetAudience(document, description, servicesData.allServices);
      const socialLinks = this.extractSocialLinks(document);
      const colors = this.extractColors(document);
      const images = this.extractImages(document, url);

      return {
        businessName,
        title,
        description,
        services: servicesData.allServices, // For backward compatibility
        coreServices: servicesData.coreServices,
        additionalServices: servicesData.additionalServices,
        products: servicesData.products,
        location,
        phone,
        hours,
        industry,
        targetAudience,
        contact,
        socialLinks,
        colors,
        images,
        rawText: this.extractCleanText(document)
      };

    } catch (error) {
      throw new Error(`Failed to parse website content: ${error.message}`);
    }
  }

  // Extract page title
  extractTitle(document) {
    const titleElement = document.querySelector('title');
    return titleElement ? titleElement.textContent.trim() : '';
  }

  // Enhanced business description extraction with comprehensive analysis
  extractDescription(document) {
    const descriptions = [];

    // 1. Meta descriptions (highest priority)
    const metaDesc = document.querySelector('meta[name="description"]');
    if (metaDesc && metaDesc.getAttribute('content')) {
      descriptions.push({
        source: 'meta',
        content: metaDesc.getAttribute('content').trim(),
        priority: 10
      });
    }

    const ogDesc = document.querySelector('meta[property="og:description"]');
    if (ogDesc && ogDesc.getAttribute('content')) {
      descriptions.push({
        source: 'og',
        content: ogDesc.getAttribute('content').trim(),
        priority: 9
      });
    }

    // 2. About sections
    const aboutSections = this.extractAboutSections(document);
    aboutSections.forEach(content => {
      descriptions.push({
        source: 'about',
        content: content,
        priority: 8
      });
    });

    // 3. Hero/banner sections
    const heroContent = this.extractHeroContent(document);
    if (heroContent) {
      descriptions.push({
        source: 'hero',
        content: heroContent,
        priority: 7
      });
    }

    // 4. Main content paragraphs
    const mainContent = this.extractMainContent(document);
    if (mainContent) {
      descriptions.push({
        source: 'main',
        content: mainContent,
        priority: 6
      });
    }

    // 5. Service descriptions
    const serviceDesc = this.extractServiceDescriptions(document);
    if (serviceDesc) {
      descriptions.push({
        source: 'services',
        content: serviceDesc,
        priority: 5
      });
    }

    // Combine and create comprehensive description
    return this.createComprehensiveDescription(descriptions);
  }

  // Extract content from about sections
  extractAboutSections(document) {
    const aboutContent = [];
    const aboutSelectors = [
      '.about', '.about-us', '.about-section', '.company-info',
      '[class*="about"]', '[id*="about"]', '.overview', '.intro',
      '.company-description', '.business-info'
    ];

    aboutSelectors.forEach(selector => {
      const sections = document.querySelectorAll(selector);
      sections.forEach(section => {
        const paragraphs = section.querySelectorAll('p');
        paragraphs.forEach(p => {
          const text = p.textContent.trim();
          if (text.length > 50 && text.length < 500) {
            aboutContent.push(text);
          }
        });
      });
    });

    return aboutContent;
  }

  // Extract hero/banner content
  extractHeroContent(document) {
    const heroSelectors = [
      '.hero', '.banner', '.jumbotron', '.intro', '.tagline',
      '.hero-content', '.banner-content', '.main-message',
      '[class*="hero"]', '[class*="banner"]'
    ];

    for (const selector of heroSelectors) {
      const hero = document.querySelector(selector);
      if (hero) {
        const text = hero.textContent.trim();
        if (text.length > 30 && text.length < 300) {
          return text;
        }
      }
    }

    return null;
  }

  // Extract main content
  extractMainContent(document) {
    const mainSelectors = [
      'main', '.main', '.content', '.main-content', '.page-content',
      '.container p', '.wrapper p', 'article p'
    ];

    for (const selector of mainSelectors) {
      const elements = document.querySelectorAll(selector);
      for (const element of elements) {
        if (selector.endsWith(' p')) {
          // For paragraph selectors
          const text = element.textContent.trim();
          if (text.length > 100 && text.length < 400) {
            return text;
          }
        } else {
          // For container selectors
          const firstP = element.querySelector('p');
          if (firstP) {
            const text = firstP.textContent.trim();
            if (text.length > 100 && text.length < 400) {
              return text;
            }
          }
        }
      }
    }

    return null;
  }

  // Extract service descriptions
  extractServiceDescriptions(document) {
    const serviceSelectors = [
      '.services p', '.service p', '.offerings p',
      '.what-we-do p', '.our-services p'
    ];

    for (const selector of serviceSelectors) {
      const paragraphs = document.querySelectorAll(selector);
      for (const p of paragraphs) {
        const text = p.textContent.trim();
        if (text.length > 80 && text.length < 300) {
          return text;
        }
      }
    }

    return null;
  }

  // Create comprehensive business description
  createComprehensiveDescription(descriptions) {
    if (descriptions.length === 0) {
      return 'Professional business providing quality services to customers.';
    }

    // Sort by priority
    descriptions.sort((a, b) => b.priority - a.priority);

    // Start with highest priority description
    let baseDescription = descriptions[0].content;

    // Enhance with additional context
    const aboutContent = descriptions.filter(d => d.source === 'about');
    const serviceContent = descriptions.filter(d => d.source === 'services');
    const heroContent = descriptions.filter(d => d.source === 'hero');

    let enhancedDescription = baseDescription;

    // Add about information if different from base
    if (aboutContent.length > 0 && !this.isSimilarContent(baseDescription, aboutContent[0].content)) {
      enhancedDescription += ' ' + aboutContent[0].content;
    }

    // Add service context if available and different
    if (serviceContent.length > 0 && !this.isSimilarContent(enhancedDescription, serviceContent[0].content)) {
      enhancedDescription += ' ' + serviceContent[0].content;
    }

    // Clean and format the description
    enhancedDescription = this.cleanDescription(enhancedDescription);

    // Ensure reasonable length (2-3 paragraphs worth)
    if (enhancedDescription.length > 800) {
      enhancedDescription = enhancedDescription.substring(0, 800) + '...';
    }

    // Ensure minimum quality and comprehensive content
    if (enhancedDescription.length < 150) {
      // Fallback to combining multiple sources
      const allContent = descriptions.map(d => d.content).join(' ');
      enhancedDescription = this.cleanDescription(allContent).substring(0, 500);
    }

    // If still too short, create a basic description from available data
    if (enhancedDescription.length < 100) {
      const businessName = descriptions.find(d => d.source === 'meta')?.content || 'This business';
      enhancedDescription = `${businessName} provides professional services and solutions to customers. We are committed to delivering quality results and excellent customer service.`;
    }

    return enhancedDescription || 'Professional business providing quality services to customers.';
  }

  // Check if two content pieces are similar
  isSimilarContent(content1, content2) {
    const words1 = content1.toLowerCase().split(/\s+/);
    const words2 = content2.toLowerCase().split(/\s+/);

    const commonWords = words1.filter(word => words2.includes(word));
    const similarity = commonWords.length / Math.max(words1.length, words2.length);

    return similarity > 0.6; // 60% similarity threshold
  }

  // Clean and format description
  cleanDescription(description) {
    if (!description) return '';

    let cleaned = description
      // Remove extra whitespace
      .replace(/\s+/g, ' ')
      // Remove common website elements
      .replace(/\b(click here|learn more|read more|contact us|get started|sign up|download|subscribe)\b/gi, '')
      // Remove navigation elements
      .replace(/\b(home|about|services|products|contact|blog|news)\b/gi, '')
      // Remove promotional language
      .replace(/\b(best|top|leading|premier|#1|award-winning)\b/gi, '')
      // Clean up punctuation
      .replace(/[.]{2,}/g, '.')
      .replace(/\s+[.]/g, '.')
      .trim();

    // Ensure proper sentence structure
    if (cleaned && !cleaned.endsWith('.') && !cleaned.endsWith('!') && !cleaned.endsWith('?')) {
      cleaned += '.';
    }

    return cleaned;
  }

  // Extract business name with improved algorithm
  extractBusinessName(document, title, url) {
    // Priority 1: OpenGraph site name (most reliable)
    const ogSiteName = document.querySelector('meta[property="og:site_name"]');
    if (ogSiteName && ogSiteName.getAttribute('content')?.trim()) {
      const siteName = ogSiteName.getAttribute('content').trim();
      if (this.isValidBusinessName(siteName)) {
        return this.cleanBusinessName(siteName);
      }
    }

    // Priority 2: OpenGraph title (very reliable)
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle && ogTitle.getAttribute('content')?.trim()) {
      const ogTitleText = ogTitle.getAttribute('content').trim();
      const cleanedOgTitle = this.cleanBusinessName(ogTitleText);
      if (this.isValidBusinessName(cleanedOgTitle)) {
        return cleanedOgTitle;
      }
    }

    // Priority 3: Twitter title
    const twitterTitle = document.querySelector('meta[name="twitter:title"]');
    if (twitterTitle && twitterTitle.getAttribute('content')?.trim()) {
      const twitterTitleText = twitterTitle.getAttribute('content').trim();
      const cleanedTwitterTitle = this.cleanBusinessName(twitterTitleText);
      if (this.isValidBusinessName(cleanedTwitterTitle)) {
        return cleanedTwitterTitle;
      }
    }

    // Priority 4: Page title (cleaned)
    if (title && title.trim()) {
      const cleanedTitle = this.cleanBusinessName(title);
      if (this.isValidBusinessName(cleanedTitle)) {
        return cleanedTitle;
      }
    }

    // Priority 5: Logo/brand elements
    const brandSelectors = [
      '.logo', '.brand', '.site-title', '.site-name', '.navbar-brand',
      '[class*="logo"]', '[class*="brand"]', '[class*="site"]',
      'header .brand', 'nav .brand', '.header-brand'
    ];

    for (const selector of brandSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent?.trim()) {
        const brandText = this.cleanBusinessName(element.textContent.trim());
        if (this.isValidBusinessName(brandText)) {
          return brandText;
        }
      }
    }

    // Priority 6: Specific business name selectors
    const businessSelectors = [
      '.business-name', '.company-name', '.brand-name', '.logo-text',
      '[class*="business"]', '[class*="company"]'
    ];

    for (const selector of businessSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent?.trim()) {
        const businessText = this.cleanBusinessName(element.textContent.trim());
        if (this.isValidBusinessName(businessText)) {
          return businessText;
        }
      }
    }

    // Priority 7: Domain name (fallback)
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      const domainName = domain.split('.')[0];
      return domainName.charAt(0).toUpperCase() + domainName.slice(1);
    } catch {
      return 'Business';
    }
  }

  // Clean business name by removing common suffixes and splitting on delimiters
  cleanBusinessName(name) {
    if (!name) return '';

    // Split on common delimiters and take the first part
    const delimiters = ['|', '-', '–', '—', ':', '•', '·', '/', '\\'];
    let cleaned = name;

    for (const delimiter of delimiters) {
      if (cleaned.includes(delimiter)) {
        cleaned = cleaned.split(delimiter)[0].trim();
        break;
      }
    }

    // Remove common suffixes
    const suffixes = [
      'Official Site', 'Official Website', 'Home Page', 'Homepage',
      'Welcome', 'Home', 'Main Page', 'Website', 'Site'
    ];

    for (const suffix of suffixes) {
      if (cleaned.toLowerCase().endsWith(suffix.toLowerCase())) {
        cleaned = cleaned.substring(0, cleaned.length - suffix.length).trim();
      }
    }

    return cleaned.trim();
  }

  // Validate if extracted text is a reasonable business name
  isValidBusinessName(name) {
    if (!name || name.length < 2) return false;
    if (name.length > 100) return false;

    // Reject if it looks like promotional content
    const promotionalKeywords = [
      'sale', 'discount', 'offer', 'deal', 'save', 'free', 'buy', 'get',
      'limited time', 'special', 'promotion', 'cash back', 'trade-in',
      'new', 'latest', 'now', 'today', 'finally back'
    ];

    const lowerName = name.toLowerCase();
    for (const keyword of promotionalKeywords) {
      if (lowerName.includes(keyword)) {
        return false;
      }
    }

    // Reject if it's mostly numbers or special characters
    const alphaCount = (name.match(/[a-zA-Z]/g) || []).length;
    if (alphaCount < name.length * 0.5) return false;

    return true;
  }

  // Extract contact information
  extractContactInfo(document) {
    const contact = {};

    // Phone number
    const phoneRegex = /(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})/g;
    const bodyText = document.body.textContent;
    const phoneMatch = bodyText.match(phoneRegex);
    if (phoneMatch) {
      contact.phone = phoneMatch[0];
    }

    // Email
    const emailRegex = /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g;
    const emailMatch = bodyText.match(emailRegex);
    if (emailMatch) {
      contact.email = emailMatch[0];
    }

    // Address
    const addressSelectors = [
      '.address',
      '.location',
      '[class*="address"]',
      '[class*="location"]'
    ];

    for (const selector of addressSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        contact.address = element.textContent.trim();
        break;
      }
    }

    return contact;
  }

  // Enhanced services/products extraction with comprehensive detection
  extractServices(document) {
    const servicesData = {
      coreServices: [],
      additionalServices: [],
      products: [],
      allServices: []
    };

    // 1. Navigation menu services
    const navServices = this.extractNavigationServices(document);

    // 2. Dedicated service/product sections
    const sectionServices = this.extractSectionServices(document);

    // 3. Service cards and listings
    const cardServices = this.extractServiceCards(document);

    // 4. Product catalogs and listings
    const productServices = this.extractProductListings(document);

    // 5. Main content area services
    const contentServices = this.extractContentServices(document);

    // Combine and categorize all services
    const allExtracted = [
      ...navServices,
      ...sectionServices,
      ...cardServices,
      ...productServices,
      ...contentServices
    ];

    // Remove duplicates and clean
    const uniqueServices = [...new Set(allExtracted)]
      .map(service => this.cleanServiceText(service))
      .filter(service => this.isValidService(service));

    // Categorize services
    const categorized = this.categorizeServices(uniqueServices);

    // Ensure we have some categorized services even if categorization doesn't work perfectly
    if (categorized.core.length === 0 && categorized.products.length === 0 && uniqueServices.length > 0) {
      // Fallback: put first half in core, second half in additional
      const midpoint = Math.ceil(uniqueServices.length / 2);
      categorized.core = uniqueServices.slice(0, midpoint);
      categorized.additional = uniqueServices.slice(midpoint);
    }

    return {
      coreServices: categorized.core.slice(0, 8),
      additionalServices: categorized.additional.slice(0, 6),
      products: categorized.products.slice(0, 8),
      allServices: uniqueServices.slice(0, 15)
    };
  }

  // Extract services from navigation menus
  extractNavigationServices(document) {
    const services = [];
    const navSelectors = [
      'nav a', 'header nav a', '.navbar a', '.menu a',
      '.navigation a', '[class*="nav"] a', '[role="navigation"] a'
    ];

    navSelectors.forEach(selector => {
      const links = document.querySelectorAll(selector);
      links.forEach(link => {
        const text = link.textContent.trim();
        const href = link.getAttribute('href') || '';

        // Look for service-related navigation items
        if (this.isServiceNavItem(text, href)) {
          services.push(text);
        }
      });
    });

    return services;
  }

  // Extract services from dedicated sections
  extractSectionServices(document) {
    const services = [];
    const sectionSelectors = [
      '.services', '.service', '.offerings', '.solutions',
      '.products', '.product', '.specialties', '.expertise',
      '[class*="service"]', '[class*="product"]', '[class*="offering"]'
    ];

    sectionSelectors.forEach(selector => {
      const sections = document.querySelectorAll(selector);
      sections.forEach(section => {
        // Look for lists within sections
        const listItems = section.querySelectorAll('li, .item, .card, .box');
        listItems.forEach(item => {
          const text = item.textContent.trim();
          if (text && text.length < 150) {
            services.push(text);
          }
        });

        // Look for headings within sections
        const headings = section.querySelectorAll('h1, h2, h3, h4, h5, h6');
        headings.forEach(heading => {
          const text = heading.textContent.trim();
          if (text && text.length < 100 && this.isServiceHeading(text)) {
            services.push(text);
          }
        });
      });
    });

    return services;
  }

  // Extract services from service cards
  extractServiceCards(document) {
    const services = [];
    const cardSelectors = [
      '.card', '.service-card', '.product-card', '.offering-card',
      '.feature', '.feature-box', '.service-box', '.product-box',
      '[class*="card"]', '[class*="box"]', '[class*="tile"]'
    ];

    cardSelectors.forEach(selector => {
      const cards = document.querySelectorAll(selector);
      cards.forEach(card => {
        // Get card title/heading
        const title = card.querySelector('h1, h2, h3, h4, h5, h6, .title, .heading');
        if (title) {
          const text = title.textContent.trim();
          if (text && text.length < 100) {
            services.push(text);
          }
        }

        // Get card description for context
        const description = card.querySelector('p, .description, .summary');
        if (description) {
          const text = description.textContent.trim();
          if (text && text.length < 200 && this.containsServiceKeywords(text)) {
            services.push(text);
          }
        }
      });
    });

    return services;
  }

  // Extract products from catalogs and listings
  extractProductListings(document) {
    const products = [];
    const productSelectors = [
      '.product-list li', '.catalog li', '.menu-item',
      '.product-grid .item', '.shop-item', '.store-item',
      '[class*="product"] .item', '[class*="catalog"] .item'
    ];

    productSelectors.forEach(selector => {
      const items = document.querySelectorAll(selector);
      items.forEach(item => {
        const name = item.querySelector('.name, .title, h1, h2, h3, h4, h5, h6');
        if (name) {
          const text = name.textContent.trim();
          if (text && text.length < 80) {
            products.push(text);
          }
        }
      });
    });

    return products;
  }

  // Extract services from main content
  extractContentServices(document) {
    const services = [];
    const contentText = document.body.textContent;

    // Look for service patterns in text
    const servicePatterns = [
      /(?:we offer|we provide|our services include|services offered|we specialize in)\s*:?\s*([^.!?]+)/gi,
      /(?:including|such as|like)\s*:?\s*([^.!?]+)/gi
    ];

    servicePatterns.forEach(pattern => {
      const matches = contentText.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const cleanMatch = match.replace(/^(we offer|we provide|our services include|services offered|we specialize in|including|such as|like)\s*:?\s*/gi, '');
          if (cleanMatch.length < 200) {
            // Split on common delimiters
            const items = cleanMatch.split(/[,;•·\n]/).map(item => item.trim());
            services.push(...items);
          }
        });
      }
    });

    return services;
  }

  // Helper methods for service extraction
  isServiceNavItem(text, href) {
    const serviceKeywords = [
      'service', 'product', 'solution', 'offering', 'specialty',
      'treatment', 'procedure', 'consultation', 'repair', 'installation',
      'maintenance', 'support', 'training', 'design', 'development'
    ];

    const lowerText = text.toLowerCase();
    const lowerHref = href.toLowerCase();

    return serviceKeywords.some(keyword =>
      lowerText.includes(keyword) || lowerHref.includes(keyword)
    ) && text.length < 50;
  }

  isServiceHeading(text) {
    const serviceIndicators = [
      'service', 'product', 'solution', 'offering', 'specialty',
      'treatment', 'consultation', 'repair', 'installation', 'maintenance',
      'design', 'development', 'training', 'support', 'plan', 'package'
    ];

    const lowerText = text.toLowerCase();
    return serviceIndicators.some(indicator => lowerText.includes(indicator));
  }

  containsServiceKeywords(text) {
    const serviceKeywords = [
      'provide', 'offer', 'specialize', 'expert', 'professional',
      'service', 'solution', 'help', 'assist', 'support', 'deliver'
    ];

    const lowerText = text.toLowerCase();
    return serviceKeywords.some(keyword => lowerText.includes(keyword));
  }

  cleanServiceText(service) {
    if (!service) return '';

    // Remove common prefixes and suffixes
    let cleaned = service
      .replace(/^(our|we|the)\s+/gi, '')
      .replace(/\s+(services?|products?|solutions?|offerings?)$/gi, '')
      .replace(/^(learn more|read more|view details|see more)$/gi, '')
      .trim();

    // Remove pricing and promotional text
    cleaned = cleaned.replace(/\$[\d,]+.*$/gi, '').trim();
    cleaned = cleaned.replace(/(starting at|from|only|just)\s*\$.*$/gi, '').trim();

    return cleaned;
  }

  isValidService(service) {
    if (!service || service.length < 3 || service.length > 100) return false;

    // Filter out common non-service items
    const invalidItems = [
      'home', 'about', 'contact', 'blog', 'news', 'careers', 'login',
      'register', 'search', 'cart', 'checkout', 'privacy', 'terms',
      'copyright', 'all rights reserved', 'learn more', 'read more',
      'click here', 'get started', 'sign up', 'download', 'subscribe'
    ];

    const lowerService = service.toLowerCase();
    return !invalidItems.some(invalid => lowerService.includes(invalid));
  }

  categorizeServices(services) {
    const core = [];
    const additional = [];
    const products = [];

    const coreKeywords = [
      'consulting', 'design', 'development', 'installation', 'repair',
      'maintenance', 'training', 'support', 'management', 'strategy',
      'planning', 'analysis', 'assessment', 'implementation'
    ];

    const productKeywords = [
      'product', 'item', 'package', 'plan', 'subscription', 'software',
      'hardware', 'equipment', 'tool', 'system', 'platform', 'app'
    ];

    services.forEach(service => {
      const lowerService = service.toLowerCase();

      if (productKeywords.some(keyword => lowerService.includes(keyword))) {
        products.push(service);
      } else if (coreKeywords.some(keyword => lowerService.includes(keyword))) {
        core.push(service);
      } else {
        additional.push(service);
      }
    });

    return { core, additional, products };
  }

  // Extract location information with enhanced detection
  extractLocation(document) {
    // Priority 1: Structured data (JSON-LD, microdata)
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    for (const script of jsonLdScripts) {
      try {
        const data = JSON.parse(script.textContent);
        if (data.address) {
          const address = data.address;
          if (typeof address === 'string') return address;
          if (address.addressLocality && address.addressRegion) {
            return `${address.addressLocality}, ${address.addressRegion}`;
          }
        }
        if (data.location && data.location.address) {
          const addr = data.location.address;
          if (addr.addressLocality && addr.addressRegion) {
            return `${addr.addressLocality}, ${addr.addressRegion}`;
          }
        }
      } catch (error) {
        // Skip invalid JSON
      }
    }

    // Priority 2: Meta tags
    const geoTags = [
      'geo.placename',
      'geo.region',
      'ICBM', // Sometimes contains coordinates
      'geo.position'
    ];

    for (const tag of geoTags) {
      const meta = document.querySelector(`meta[name="${tag}"]`);
      if (meta && meta.getAttribute('content')) {
        const content = meta.getAttribute('content');
        if (tag === 'geo.placename' || tag === 'geo.region') {
          return content;
        }
      }
    }

    // Priority 3: Contact/address sections
    const locationSelectors = [
      '.address', '.location', '.contact-address', '.office-location',
      '.headquarters', '.contact-info address', '.footer address',
      '[class*="location"]', '[class*="address"]', '[class*="contact"]',
      '.city', '.region', '.state', '.country'
    ];

    for (const selector of locationSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        const location = this.cleanLocation(element.textContent.trim());
        if (location) return location;
      }
    }

    // Priority 4: Text pattern matching
    const bodyText = document.body.textContent;

    // Look for "Located in", "Based in", "Serving", etc.
    const locationPatterns = [
      /(?:located|based|headquartered|serving|available)\s+in\s+([^.!?]+)/gi,
      /(?:office|location|address):\s*([^.!?\n]+)/gi,
      /([A-Z][a-z]+,\s*[A-Z]{2}(?:\s+\d{5})?)/g, // City, State format
      /([A-Z][a-z]+\s+[A-Z][a-z]+,\s*[A-Z]{2})/g // City Name, State
    ];

    for (const pattern of locationPatterns) {
      const matches = bodyText.match(pattern);
      if (matches && matches.length > 0) {
        const location = this.cleanLocation(matches[0]);
        if (location) return location;
      }
    }

    return '';
  }

  // Clean and validate location text
  cleanLocation(location) {
    if (!location || location.length < 3) return '';

    // Remove common prefixes
    location = location.replace(/^(located|based|headquartered|serving|available)\s+in\s+/gi, '');
    location = location.replace(/^(office|location|address):\s*/gi, '');

    // Clean up
    location = location.trim().replace(/[.!?]+$/, '');

    // Filter out non-location text
    const invalidLocationPatterns = [
      /app/gi, /iOS/gi, /android/gi, /software/gi, /version/gi, /update/gi,
      /download/gi, /install/gi, /sign up/gi, /register/gi, /login/gi,
      /hours/gi, /days/gi, /minutes/gi, /seconds/gi, /tab/gi, /menu/gi,
      /offer/gi, /deal/gi, /discount/gi, /sale/gi, /promotion/gi,
      /click/gi, /button/gi, /link/gi, /page/gi, /website/gi, /site/gi
    ];

    for (const pattern of invalidLocationPatterns) {
      if (pattern.test(location)) {
        return ''; // Reject this as a location
      }
    }

    // Validate - should look like a real location
    if (!/[a-zA-Z]/.test(location)) return '';
    if (location.length > 50) return ''; // Locations shouldn't be too long
    if (location.length < 3) return '';

    // Should contain typical location indicators
    const locationIndicators = [
      /\b[A-Z][a-z]+,\s*[A-Z]{2}\b/, // City, State
      /\b[A-Z][a-z]+\s+[A-Z][a-z]+,\s*[A-Z]{2}\b/, // City Name, State
      /\b[A-Z][a-z]+,\s*[A-Z][a-z]+\b/, // City, Country
      /\b(street|st|avenue|ave|road|rd|drive|dr|boulevard|blvd)\b/gi, // Street indicators
      /\b\d+\s+[A-Z][a-z]+\s+(street|st|avenue|ave|road|rd)\b/gi // Address format
    ];

    const hasLocationIndicator = locationIndicators.some(pattern => pattern.test(location));
    if (!hasLocationIndicator && location.split(' ').length > 3) {
      return ''; // Reject long text without location indicators
    }

    return location;
  }

  // Extract phone number with enhanced detection
  extractPhone(document) {
    // Priority 1: Structured data (JSON-LD, microdata)
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    for (const script of jsonLdScripts) {
      try {
        const data = JSON.parse(script.textContent);
        if (data.telephone) return data.telephone;
        if (data.contactPoint && data.contactPoint.telephone) return data.contactPoint.telephone;
      } catch (error) {
        // Skip invalid JSON
      }
    }

    // Priority 2: Phone-specific selectors
    const phoneSelectors = [
      '.phone', '.telephone', '.contact-phone', '.phone-number',
      '[class*="phone"]', '[class*="tel"]', '.contact-info .phone',
      'a[href^="tel:"]'
    ];

    for (const selector of phoneSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        let phone = element.textContent || element.getAttribute('href');
        if (phone) {
          phone = this.cleanPhone(phone);
          if (phone) return phone;
        }
      }
    }

    // Priority 3: Text pattern matching
    const bodyText = document.body.textContent;
    const phonePatterns = [
      /(?:phone|call|tel):\s*([+]?[\d\s\-\(\)\.]{10,})/gi,
      /([+]?1?[\s\-]?\(?[0-9]{3}\)?[\s\-]?[0-9]{3}[\s\-]?[0-9]{4})/g,
      /([+]?[\d\s\-\(\)\.]{10,15})/g
    ];

    for (const pattern of phonePatterns) {
      const matches = bodyText.match(pattern);
      if (matches) {
        for (const match of matches) {
          const cleaned = this.cleanPhone(match);
          if (cleaned && this.isValidPhone(cleaned)) {
            return cleaned;
          }
        }
      }
    }

    return '';
  }

  // Clean and validate phone number
  cleanPhone(phone) {
    if (!phone) return '';

    // Remove tel: prefix
    phone = phone.replace(/^tel:/, '');

    // Remove common prefixes
    phone = phone.replace(/^(phone|call|tel):\s*/gi, '');

    // Clean up formatting but keep basic structure
    phone = phone.trim();

    return phone;
  }

  // Validate phone number format
  isValidPhone(phone) {
    // Remove all non-digit characters for validation
    const digits = phone.replace(/\D/g, '');

    // Should have 10-15 digits
    if (digits.length < 10 || digits.length > 15) return false;

    // Should not be all the same digit
    if (/^(\d)\1+$/.test(digits)) return false;

    return true;
  }

  // Extract business hours with enhanced detection
  extractHours(document) {
    // Priority 1: Structured data (JSON-LD, microdata)
    const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
    for (const script of jsonLdScripts) {
      try {
        const data = JSON.parse(script.textContent);
        if (data.openingHours) {
          return Array.isArray(data.openingHours)
            ? data.openingHours.join(', ')
            : data.openingHours;
        }
        if (data.openingHoursSpecification) {
          const hours = data.openingHoursSpecification;
          if (Array.isArray(hours) && hours.length > 0) {
            return hours.map(h => `${h.dayOfWeek}: ${h.opens}-${h.closes}`).join(', ');
          }
        }
      } catch (error) {
        // Skip invalid JSON
      }
    }

    // Priority 2: Hours-specific selectors
    const hoursSelectors = [
      '.hours', '.opening-hours', '.business-hours', '.store-hours',
      '[class*="hours"]', '[class*="time"]', '.contact-info .hours',
      '.hours-of-operation'
    ];

    for (const selector of hoursSelectors) {
      const element = document.querySelector(selector);
      if (element) {
        const hours = this.cleanHours(element.textContent);
        if (hours) return hours;
      }
    }

    // Priority 3: Text pattern matching
    const bodyText = document.body.textContent;
    const hoursPatterns = [
      /(?:hours|open|operating):\s*([^.!?\n]{10,100})/gi,
      /(mon|monday|tue|tuesday|wed|wednesday|thu|thursday|fri|friday|sat|saturday|sun|sunday)[^.!?\n]{5,80}/gi
    ];

    for (const pattern of hoursPatterns) {
      const matches = bodyText.match(pattern);
      if (matches) {
        for (const match of matches) {
          const cleaned = this.cleanHours(match);
          if (cleaned && this.isValidHours(cleaned)) {
            return cleaned;
          }
        }
      }
    }

    return '';
  }

  // Clean and validate hours text
  cleanHours(hours) {
    if (!hours) return '';

    // Remove common prefixes
    hours = hours.replace(/^(hours|open|operating):\s*/gi, '');

    // Clean up
    hours = hours.trim().replace(/[.!?]+$/, '');

    return hours;
  }

  // Validate hours format
  isValidHours(hours) {
    if (!hours || hours.length < 5 || hours.length > 200) return false;

    // Should contain time-related keywords
    const timeKeywords = /\b(am|pm|monday|tuesday|wednesday|thursday|friday|saturday|sunday|mon|tue|wed|thu|fri|sat|sun|\d{1,2}:\d{2}|\d{1,2}am|\d{1,2}pm)\b/gi;

    return timeKeywords.test(hours);
  }

  // Extract industry/business type with intelligent detection
  extractIndustry(document, title, description, services) {
    // Industry keyword mapping
    const industryKeywords = {
      'restaurant': ['restaurant', 'dining', 'food', 'cuisine', 'menu', 'chef', 'kitchen', 'cafe', 'bistro', 'eatery'],
      'healthcare': ['medical', 'health', 'doctor', 'clinic', 'hospital', 'dental', 'pharmacy', 'therapy', 'wellness'],
      'retail': ['shop', 'store', 'retail', 'boutique', 'fashion', 'clothing', 'merchandise', 'shopping'],
      'technology': ['software', 'tech', 'digital', 'app', 'platform', 'development', 'IT', 'computer', 'data'],
      'finance': ['bank', 'financial', 'investment', 'insurance', 'loan', 'credit', 'accounting', 'tax'],
      'real estate': ['real estate', 'property', 'homes', 'housing', 'realtor', 'mortgage', 'rental'],
      'education': ['school', 'education', 'learning', 'training', 'course', 'university', 'college', 'academy'],
      'automotive': ['auto', 'car', 'vehicle', 'automotive', 'repair', 'dealership', 'garage'],
      'beauty': ['beauty', 'salon', 'spa', 'cosmetic', 'hair', 'nail', 'skincare', 'massage'],
      'fitness': ['gym', 'fitness', 'workout', 'exercise', 'training', 'yoga', 'pilates', 'sports'],
      'legal': ['law', 'legal', 'attorney', 'lawyer', 'court', 'litigation', 'counsel'],
      'construction': ['construction', 'building', 'contractor', 'renovation', 'remodeling', 'roofing'],
      'consulting': ['consulting', 'advisory', 'strategy', 'management', 'business', 'professional services'],
      'entertainment': ['entertainment', 'music', 'event', 'party', 'wedding', 'photography', 'video'],
      'travel': ['travel', 'tourism', 'hotel', 'vacation', 'trip', 'booking', 'airline'],
      'nonprofit': ['nonprofit', 'charity', 'foundation', 'volunteer', 'donation', 'community']
    };

    // Combine all text for analysis
    const allText = [title, description, services.join(' ')].join(' ').toLowerCase();

    // Score each industry based on keyword matches
    const industryScores = {};
    for (const [industry, keywords] of Object.entries(industryKeywords)) {
      let score = 0;
      for (const keyword of keywords) {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = allText.match(regex);
        if (matches) {
          score += matches.length;
        }
      }
      if (score > 0) {
        industryScores[industry] = score;
      }
    }

    // Find the highest scoring industry
    let bestIndustry = 'business';
    let bestScore = 0;
    for (const [industry, score] of Object.entries(industryScores)) {
      if (score > bestScore) {
        bestIndustry = industry;
        bestScore = score;
      }
    }

    // Format industry name
    return bestIndustry.split(' ').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  // Extract target audience with intelligent analysis
  extractTargetAudience(document, description, services) {
    const audienceKeywords = {
      'families': ['family', 'families', 'kids', 'children', 'parents', 'baby', 'toddler'],
      'professionals': ['professional', 'business', 'corporate', 'executive', 'office', 'workplace'],
      'students': ['student', 'college', 'university', 'academic', 'education', 'learning'],
      'seniors': ['senior', 'elderly', 'retirement', 'mature', 'older adults'],
      'young adults': ['young', 'millennial', 'gen z', 'youth', 'teen', 'college'],
      'homeowners': ['homeowner', 'property owner', 'house', 'home', 'residential'],
      'small businesses': ['small business', 'entrepreneur', 'startup', 'local business'],
      'women': ['women', 'ladies', 'female', 'mom', 'mother'],
      'men': ['men', 'male', 'father', 'dad', 'gentleman'],
      'athletes': ['athlete', 'sports', 'fitness', 'training', 'performance'],
      'pet owners': ['pet', 'dog', 'cat', 'animal', 'veterinary'],
      'local community': ['local', 'community', 'neighborhood', 'area', 'region']
    };

    // Analyze content for audience indicators
    const allText = [description, services.join(' ')].join(' ').toLowerCase();

    const audienceScores = {};
    for (const [audience, keywords] of Object.entries(audienceKeywords)) {
      let score = 0;
      for (const keyword of keywords) {
        const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
        const matches = allText.match(regex);
        if (matches) {
          score += matches.length;
        }
      }
      if (score > 0) {
        audienceScores[audience] = score;
      }
    }

    // Get top audiences
    const sortedAudiences = Object.entries(audienceScores)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 3)
      .map(([audience]) => audience);

    if (sortedAudiences.length > 0) {
      return sortedAudiences.join(', ');
    }

    // Enhanced fallback based on industry patterns and business context
    const businessText = allText;

    // Restaurant/Food industry
    if (businessText.includes('restaurant') || businessText.includes('food') || businessText.includes('coffee') || businessText.includes('dining')) {
      return 'food lovers, families, coffee enthusiasts, local diners';
    }

    // Technology industry
    if (businessText.includes('tech') || businessText.includes('software') || businessText.includes('digital') || businessText.includes('computer')) {
      if (businessText.includes('enterprise') || businessText.includes('business')) {
        return 'businesses, enterprises, IT professionals';
      } else if (businessText.includes('consumer') || businessText.includes('personal')) {
        return 'consumers, tech enthusiasts, individuals';
      } else {
        return 'businesses, professionals, tech users';
      }
    }

    // Healthcare industry
    if (businessText.includes('medical') || businessText.includes('health') || businessText.includes('doctor') || businessText.includes('clinic')) {
      return 'patients, healthcare seekers, medical professionals';
    }

    // Retail industry
    if (businessText.includes('retail') || businessText.includes('shop') || businessText.includes('store') || businessText.includes('buy')) {
      if (businessText.includes('fashion') || businessText.includes('clothing')) {
        return 'fashion enthusiasts, shoppers, style-conscious consumers';
      } else {
        return 'shoppers, consumers, retail customers';
      }
    }

    // Finance industry
    if (businessText.includes('bank') || businessText.includes('financial') || businessText.includes('investment')) {
      return 'investors, business owners, individuals seeking financial services';
    }

    // Education industry
    if (businessText.includes('education') || businessText.includes('school') || businessText.includes('learning')) {
      return 'students, educators, lifelong learners';
    }

    return 'local customers, community members';
  }

  // Extract social media links
  extractSocialLinks(document) {
    const socialLinks = {};
    const links = document.querySelectorAll('a[href]');

    links.forEach(link => {
      const href = link.getAttribute('href');
      if (href.includes('facebook.com')) socialLinks.facebook = href;
      if (href.includes('instagram.com')) socialLinks.instagram = href;
      if (href.includes('twitter.com') || href.includes('x.com')) socialLinks.twitter = href;
      if (href.includes('linkedin.com')) socialLinks.linkedin = href;
    });

    return socialLinks;
  }

  // Extract brand colors
  extractColors(document) {
    const colors = [];

    // This is a simplified color extraction
    // In a real implementation, you might want to use a more sophisticated approach
    const colorRegex = /#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})/g;
    const styles = document.querySelectorAll('style');

    styles.forEach(style => {
      const matches = style.textContent.match(colorRegex);
      if (matches) {
        colors.push(...matches);
      }
    });

    return [...new Set(colors)].slice(0, 5); // Remove duplicates and limit
  }

  // Extract images
  extractImages(document, baseUrl) {
    const images = [];
    const imgElements = document.querySelectorAll('img[src]');

    imgElements.forEach(img => {
      const src = img.getAttribute('src');
      if (src && !src.startsWith('data:')) {
        try {
          const fullUrl = new URL(src, baseUrl).href;
          images.push({
            src: fullUrl,
            alt: img.getAttribute('alt') || '',
            title: img.getAttribute('title') || ''
          });
        } catch (error) {
          // Skip invalid URLs
        }
      }
    });

    return images.slice(0, 10); // Limit to 10 images
  }

  // Extract clean text content
  extractCleanText(document) {
    // Remove script and style elements
    const scripts = document.querySelectorAll('script, style');
    scripts.forEach(script => script.remove());

    return document.body.textContent.trim().substring(0, 2000); // Limit text length
  }

  // Enhance extracted data using AI with intelligent website analysis
  async enhanceWithAI(extractedData, html) {
    try {
      const openaiService = require('./openai');

      // Create a comprehensive prompt for AI analysis
      const websiteContent = this.prepareContentForAI(extractedData, html);

      const prompt = `You are a business intelligence analyst. Analyze this website content and create a comprehensive business profile.

WEBSITE CONTENT TO ANALYZE:
Business Name: ${extractedData.businessName}
Page Title: ${extractedData.title}
Meta Description: ${extractedData.description}
Extracted Services: ${extractedData.allServices?.join(', ') || 'None found'}
Location: ${extractedData.location || 'Not specified'}
Contact Info: ${JSON.stringify(extractedData.contact)}
Website Content Summary: ${websiteContent}

TASK: Create an intelligent business description and analysis based on your understanding of this website.

Please provide a JSON response with:
{
  "businessName": "cleaned and properly formatted business name",
  "industry": "specific industry category (e.g., 'Coffee & Restaurants', 'Technology Services', 'Healthcare')",
  "description": "A comprehensive 2-3 sentence business description that captures what the business does, who they serve, and what makes them unique. Write this as if you understand the business from visiting their website.",
  "services": ["list of 5-10 main services/products this business actually offers based on website analysis"],
  "location": "formatted location if available",
  "targetAudience": "specific target audience based on website content and industry",
  "brandVoice": "professional|friendly|casual|luxury|innovative",
  "keyFeatures": ["3-5 unique selling points or key features"],
  "businessType": "restaurant|retail|service|healthcare|technology|finance|education|etc",
  "yearsInBusiness": "if mentioned on website",
  "specialties": ["specific areas of expertise or specialization"]
}

IMPORTANT: Base your analysis on understanding the business, not just extracting text. Create a description that shows you comprehend what this business does and why customers would choose them.`;

      const aiResponse = await openaiService.generateContent({
        businessName: extractedData.businessName,
        businessType: 'general',
        location: extractedData.location || 'Local Area',
        platform: 'analysis',
        customPrompt: prompt
      });

      // Try to parse AI response as JSON
      try {
        const aiData = JSON.parse(aiResponse.text);
        return {
          ...extractedData,
          ...aiData,
          enhanced: true
        };
      } catch (parseError) {
        console.warn('Could not parse AI response as JSON, using extracted data');
        return {
          ...extractedData,
          enhanced: false
        };
      }

    } catch (error) {
      console.warn('AI enhancement failed, using extracted data:', error.message);
      return {
        ...extractedData,
        enhanced: false
      };
    }
  }

  // Prepare website content for AI analysis
  prepareContentForAI(extractedData, html) {
    try {
      // Extract key content sections for AI analysis
      const dom = new (require('jsdom').JSDOM)(html);
      const document = dom.window.document;

      const contentSections = [];

      // Get main headings
      const headings = document.querySelectorAll('h1, h2, h3');
      const headingTexts = Array.from(headings)
        .map(h => h.textContent.trim())
        .filter(text => text.length > 5 && text.length < 100)
        .slice(0, 10);

      if (headingTexts.length > 0) {
        contentSections.push(`Main Headings: ${headingTexts.join(', ')}`);
      }

      // Get key paragraphs
      const paragraphs = document.querySelectorAll('p');
      const keyParagraphs = Array.from(paragraphs)
        .map(p => p.textContent.trim())
        .filter(text => text.length > 50 && text.length < 300)
        .slice(0, 5);

      if (keyParagraphs.length > 0) {
        contentSections.push(`Key Content: ${keyParagraphs.join(' ')}`);
      }

      // Get navigation items
      const navItems = document.querySelectorAll('nav a, .menu a, .navigation a');
      const navTexts = Array.from(navItems)
        .map(a => a.textContent.trim())
        .filter(text => text.length > 2 && text.length < 50)
        .slice(0, 10);

      if (navTexts.length > 0) {
        contentSections.push(`Navigation: ${navTexts.join(', ')}`);
      }

      // Combine all content with length limit
      const combinedContent = contentSections.join(' | ');
      return combinedContent.length > 1500
        ? combinedContent.substring(0, 1500) + '...'
        : combinedContent;

    } catch (error) {
      console.warn('Error preparing content for AI:', error.message);
      return `Business: ${extractedData.businessName}, Description: ${extractedData.description}`;
    }
  }
}

module.exports = new WebsiteExtractionService();
