const axios = require('axios');

async function testFrontendAuth() {
  console.log('🔐 Testing Frontend Authentication for Website Extraction\n');

  // Test 1: Test with test-token (should work in development)
  console.log('1️⃣ Testing with test-token (development mode)...');
  try {
    const response = await axios.post('http://localhost:5000/api/profile/extract-website', {
      url: 'https://example.com'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer test-token'
      },
      timeout: 15000
    });

    if (response.data.success) {
      console.log('✅ Test token authentication: WORKING');
      console.log(`   Business Name: ${response.data.data.businessName}`);
    } else {
      console.log('❌ Test token authentication: FAILED');
      console.log(`   Error: ${response.data.error}`);
    }
  } catch (error) {
    console.log('❌ Test token authentication: FAILED');
    console.log(`   Error: ${error.response?.data?.error || error.message}`);
  }

  // Test 2: Test without token (should fail)
  console.log('\n2️⃣ Testing without token (should fail)...');
  try {
    const response = await axios.post('http://localhost:5000/api/profile/extract-website', {
      url: 'https://example.com'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 15000
    });

    console.log('❌ No token test: UNEXPECTED SUCCESS (should have failed)');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ No token test: CORRECTLY FAILED with 401');
      console.log(`   Error: ${error.response.data.error}`);
    } else {
      console.log('❌ No token test: FAILED with unexpected error');
      console.log(`   Error: ${error.response?.data?.error || error.message}`);
    }
  }

  // Test 3: Test with invalid token (should fail)
  console.log('\n3️⃣ Testing with invalid token (should fail)...');
  try {
    const response = await axios.post('http://localhost:5000/api/profile/extract-website', {
      url: 'https://example.com'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-token-12345'
      },
      timeout: 15000
    });

    console.log('❌ Invalid token test: UNEXPECTED SUCCESS (should have failed)');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Invalid token test: CORRECTLY FAILED with 401');
      console.log(`   Error: ${error.response.data.error}`);
    } else {
      console.log('❌ Invalid token test: FAILED with unexpected error');
      console.log(`   Error: ${error.response?.data?.error || error.message}`);
    }
  }

  console.log('\n🎯 Authentication Test Summary:');
  console.log('✅ Test token (development): Should work');
  console.log('❌ No token: Should fail with 401');
  console.log('❌ Invalid token: Should fail with 401');
  
  console.log('\n💡 Frontend Integration:');
  console.log('The AuthContext now provides test-token in development mode');
  console.log('when no session is available, which should resolve the');
  console.log('"Invalid token" error in the UI.');
  
  console.log('\n🌐 Ready to test in browser:');
  console.log('1. Go to http://localhost:3000');
  console.log('2. Navigate to Business Profile');
  console.log('3. Click "Website Extract" tab');
  console.log('4. Enter a URL and click "Extract Data"');
  console.log('5. Should work without authentication errors!');
}

testFrontendAuth().catch(console.error);
