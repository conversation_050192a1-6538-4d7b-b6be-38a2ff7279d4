import React, { useState } from 'react';
import {
  Building2,
  Globe,
  MapPin,
  Upload,
  Link,
  Instagram,
  Facebook,
  Twitter,
  Linkedin,
  Search,
  Download,
  Settings,
  Target,
  Calendar,
  TrendingUp,
  Users,
  Zap,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Save,
  Palette,
  Plus,
  X,
  Package,
  Plug
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useAuth } from '../contexts/AuthContext';

const BusinessProfile = () => {
  const { apiCall, getAuthHeaders, session } = useAuth();
  const [activeTab, setActiveTab] = useState('basic');
  const [isExtracting, setIsExtracting] = useState(false);
  const [isTraining, setIsTraining] = useState(false);
  const [extractionResults, setExtractionResults] = useState(null);

  // Basic Info State
  const [businessInfo, setBusinessInfo] = useState({
    name: '',
    industry: '',
    description: '',
    website: '',
    location: '',
    targetAudience: '',
    brandVoice: 'professional',
    phone: '',
    hours: ''
  });

  // Logo State
  const [logo, setLogo] = useState(null);
  const [logoPreview, setLogoPreview] = useState(null);

  // Social Media Analysis State
  const [analysisData, setAnalysisData] = useState(null);
  const [analysisLoading, setAnalysisLoading] = useState(false);
  const [connectedPlatforms, setConnectedPlatforms] = useState({});
  const [selectedPlatform, setSelectedPlatform] = useState('facebook');
  const [socialMediaUrl, setSocialMediaUrl] = useState('');

  // Services State
  const [services, setServices] = useState([]);
  const [newService, setNewService] = useState('');

  // Sync changes from Basic Info back to extracted data
  React.useEffect(() => {
    if (extractedData) {
      setExtractedData(prev => ({
        ...prev,
        businessName: businessInfo.name,
        description: businessInfo.description,
        location: businessInfo.location,
        industry: businessInfo.industry,
        targetAudience: businessInfo.targetAudience,
        website: businessInfo.website,
        brandVoice: businessInfo.brandVoice,
        phone: businessInfo.phone,
        hours: businessInfo.hours,
        services: services
      }));
    }
  }, [businessInfo, services]);

  // Website Extraction State
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [extractedData, setExtractedData] = useState(null);

  // Social Media State
  const [socialAccounts, setSocialAccounts] = useState({
    facebook: '',
    instagram: '',
    twitter: '',
    linkedin: ''
  });
  const [socialPosts, setSocialPosts] = useState([]);
  const [uploadedFiles, setUploadedFiles] = useState({
    facebook: [],
    instagram: [],
    twitter: [],
    linkedin: [],
    general: [],
    competitors: []
  });
  // Removed competitorUrls - now using competitors array for multiple competitors
  const [competitorPosts, setCompetitorPosts] = useState([]);
  const [competitors, setCompetitors] = useState([]);
  const [newCompetitorUrl, setNewCompetitorUrl] = useState('');
  const [uploadedImages, setUploadedImages] = useState([]);
  const [selectedSocialPlatform, setSelectedSocialPlatform] = useState('');

  // Local Settings State
  const [localSettings, setLocalSettings] = useState({
    radius: 10,
    includeEvents: true,
    includeTrends: true,
    includeWeather: true,
    eventTypes: ['community', 'business', 'seasonal', 'cultural'],
    discounts: {
      enabled: false,
      percentage: '',
      description: '',
      validUntil: '',
      terms: '',
      promoCode: ''
    }
  });

  // Brand Colors State
  const [brandColors, setBrandColors] = useState({
    primary: '#3B82F6',
    secondary: '#10B981',
    accent: '#F59E0B'
  });

  const industries = [
    'Restaurant/Food Service',
    'Retail/E-commerce',
    'Healthcare',
    'Real Estate',
    'Professional Services',
    'Fitness/Wellness',
    'Beauty/Salon',
    'Automotive',
    'Home Services',
    'Technology',
    'Education',
    'Other'
  ];

  const brandVoices = [
    { id: 'professional', name: 'Professional', description: 'Formal, authoritative, trustworthy' },
    { id: 'friendly', name: 'Friendly', description: 'Warm, approachable, conversational' },
    { id: 'casual', name: 'Casual', description: 'Relaxed, informal, relatable' },
    { id: 'energetic', name: 'Energetic', description: 'Enthusiastic, dynamic, exciting' },
    { id: 'sophisticated', name: 'Sophisticated', description: 'Elegant, refined, premium' }
  ];

  const eventTypes = [
    { id: 'community', name: 'Community Events', description: 'Local festivals, markets, gatherings' },
    { id: 'business', name: 'Business Events', description: 'Networking, conferences, trade shows' },
    { id: 'seasonal', name: 'Seasonal Events', description: 'Holidays, seasonal celebrations' },
    { id: 'cultural', name: 'Cultural Events', description: 'Arts, music, cultural celebrations' },
    { id: 'sports', name: 'Sports Events', description: 'Local games, tournaments, sports activities' }
  ];

  // Website Extraction Function
  const handleWebsiteExtraction = async () => {
    if (!websiteUrl) {
      toast.error('Please enter a website URL');
      return;
    }

    setIsExtracting(true);
    setExtractionResults(null);

    try {
      // Call the real API endpoint using proper authentication
      const response = await apiCall('/api/profile/extract-website', {
        method: 'POST',
        body: JSON.stringify({ url: websiteUrl })
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || result.details || 'Failed to extract website data');
      }

      if (result.success && result.data) {
        const extractedData = result.data;

        setExtractedData(extractedData);
        setExtractionResults({
          success: true,
          message: result.message,
          enhanced: extractedData.enhanced
        });

        // Automatically prefill ALL basic info fields with real extracted data
        setBusinessInfo(prev => ({
          ...prev,
          name: extractedData.businessName || prev.name,
          description: extractedData.description || prev.description,
          location: extractedData.location || prev.location,
          website: websiteUrl,
          industry: extractedData.industry || prev.industry,
          targetAudience: extractedData.targetAudience || prev.targetAudience,
          brandVoice: extractedData.brandVoice || prev.brandVoice,
          phone: extractedData.phone || prev.phone,
          hours: extractedData.hours || prev.hours
        }));

        // Update social accounts if found
        if (extractedData.socialLinks) {
          setSocialAccounts(prev => ({
            ...prev,
            facebook: extractedData.socialLinks.facebook || prev.facebook,
            instagram: extractedData.socialLinks.instagram || prev.instagram,
            twitter: extractedData.socialLinks.twitter || prev.twitter,
            linkedin: extractedData.socialLinks.linkedin || prev.linkedin
          }));
        }

        // Update services if found
        if (extractedData.services && extractedData.services.length > 0) {
          setServices(extractedData.services);
        }

        toast.success(`Website data extracted successfully! ${extractedData.enhanced ? 'Enhanced with AI analysis.' : ''} Please review the Basic Info tab.`);

        // Automatically switch to Basic Info tab for review
        setActiveTab('basic');
      } else {
        throw new Error('No data extracted from website');
      }

    } catch (error) {
      console.error('Website extraction error:', error);

      // Provide user-friendly error messages
      let userMessage = error.message;
      let suggestions = '';

      if (error.message.includes('too large')) {
        userMessage = 'Website is too large to process automatically';
        suggestions = 'Try using a specific page URL (like /about or /contact) instead of the homepage, or enter your business information manually.';
      } else if (error.message.includes('exceeded')) {
        userMessage = 'Website content exceeds size limits';
        suggestions = 'Try a simpler page URL or enter information manually.';
      } else if (error.message.includes('timeout')) {
        userMessage = 'Website is taking too long to respond';
        suggestions = 'The website may be slow or overloaded. Try again later.';
      } else if (error.message.includes('not found')) {
        userMessage = 'Website not found';
        suggestions = 'Please check the URL and make sure it\'s correct.';
      }

      setExtractionResults({
        success: false,
        message: userMessage,
        suggestions: suggestions
      });

      toast.error(`${userMessage}${suggestions ? '. ' + suggestions : ''}`);
    } finally {
      setIsExtracting(false);
    }
  };

  // Services Management Functions
  const addService = () => {
    if (newService.trim() && !services.includes(newService.trim())) {
      setServices([...services, newService.trim()]);
      setNewService('');
    }
  };

  const removeService = (serviceToRemove) => {
    setServices(services.filter(service => service !== serviceToRemove));
  };

  const handleServiceKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addService();
    }
  };

  // Logo Upload Functions
  const handleLogoUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Logo file size must be less than 5MB');
        return;
      }

      setLogo(file);

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);

      toast.success('Logo uploaded successfully!');
    }
  };

  const removeLogo = () => {
    setLogo(null);
    setLogoPreview(null);
    // Reset file input
    const fileInput = document.getElementById('logo-upload');
    if (fileInput) fileInput.value = '';
  };

  // Social Media Analysis Functions
  const runSocialMediaAnalysis = async (platform, accessToken, accountId) => {
    setAnalysisLoading(true);
    try {
      const response = await fetch('/api/social-analysis/analyze-account', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform,
          accessToken,
          accountId,
          options: {
            limit: 50,
            timeframe: '6months'
          }
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAnalysisData(result.data);
        toast.success(`Successfully analyzed ${result.data.totalPosts} posts from ${platform}!`);
      } else {
        toast.error(result.error || 'Failed to analyze social media posts');
      }
    } catch (error) {
      console.error('Social media analysis error:', error);
      toast.error('Failed to analyze social media posts');
    } finally {
      setAnalysisLoading(false);
    }
  };

  const simulateAnalysis = async (platform) => {
    setAnalysisLoading(true);
    try {
      const response = await fetch('/api/social-analysis/simulate-analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform,
          businessType: businessInfo.industry?.toLowerCase() || 'restaurant'
        }),
      });

      const result = await response.json();

      if (result.success) {
        setAnalysisData(result.data);
        toast.success(`Simulated analysis completed for ${platform}!`);
      } else {
        toast.error(result.error || 'Failed to simulate analysis');
      }
    } catch (error) {
      console.error('Simulation error:', error);
      toast.error('Failed to simulate analysis');
    } finally {
      setAnalysisLoading(false);
    }
  };

  const testPlatformConnection = async (platform, accessToken) => {
    try {
      const response = await fetch('/api/social-analysis/test-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform,
          accessToken
        }),
      });

      const result = await response.json();

      if (result.success) {
        setConnectedPlatforms(prev => ({
          ...prev,
          [platform]: { connected: true, accessToken }
        }));
        toast.success(`Successfully connected to ${platform}!`);
      } else {
        toast.error(result.error || `Failed to connect to ${platform}`);
      }
    } catch (error) {
      console.error('Connection test error:', error);
      toast.error('Failed to test connection');
    }
  };

  // Social Media Analysis Function
  const handleSocialAnalysis = async (platform) => {
    if (!socialAccounts[platform]) {
      toast.error(`Please enter your ${platform} account URL`);
      return;
    }

    setIsExtracting(true);

    // Simulate API call for social media analysis
    setTimeout(() => {
      const mockSocialData = {
        platform: platform,
        posts: [
          {
            id: 1,
            content: 'Just finished a complex pipe repair in Cherry Creek! Our team takes pride in solving the toughest plumbing challenges. 💪 #PlumbingExperts #Denver',
            engagement: { likes: 45, comments: 8, shares: 3 },
            date: '2024-01-10',
            performance: 'high',
            source: 'url'
          },
          {
            id: 2,
            content: 'Winter tip: Let your faucets drip during freezing nights to prevent pipe bursts! Stay safe, Denver! ❄️',
            engagement: { likes: 78, comments: 12, shares: 15 },
            date: '2024-01-08',
            performance: 'excellent',
            source: 'url'
          }
        ],
        insights: {
          topHashtags: ['#PlumbingExperts', '#Denver', '#HomeRepair'],
          bestPostingTimes: ['9AM-11AM', '6PM-8PM'],
          audienceAge: '35-55',
          topContent: 'Educational tips and behind-the-scenes work'
        }
      };

      setSocialPosts(prev => [...prev, ...mockSocialData.posts]);
      setIsExtracting(false);
      toast.success(`${platform} posts analyzed successfully!`);
    }, 2500);
  };

  // File Upload Handler
  const handleFileUpload = async (platform, files) => {
    if (!files || files.length === 0) return;

    setIsExtracting(true);

    // Simulate file processing and OCR/image analysis
    setTimeout(() => {
      const mockExtractedPosts = Array.from(files).map((file, index) => ({
        id: Date.now() + index,
        content: `Extracted post content from ${file.name}. This would be the actual text content extracted from the uploaded image using OCR and AI analysis. Great engagement with our community! 🎉`,
        engagement: { likes: Math.floor(Math.random() * 100), comments: Math.floor(Math.random() * 20), shares: Math.floor(Math.random() * 10) },
        date: new Date().toISOString().split('T')[0],
        performance: ['good', 'high', 'excellent'][Math.floor(Math.random() * 3)],
        source: 'upload',
        fileName: file.name
      }));

      setSocialPosts(prev => [...prev, ...mockExtractedPosts]);
      setUploadedFiles(prev => ({
        ...prev,
        [platform]: [...prev[platform], ...Array.from(files)]
      }));

      setIsExtracting(false);
      toast.success(`${files.length} post(s) extracted from uploaded files!`);
    }, 3000);
  };

  // Removed old competitor analysis function - now using multiple competitors approach

  // Add Competitor Function
  const addCompetitor = () => {
    if (newCompetitorUrl.trim()) {
      const newCompetitor = {
        id: Date.now(),
        url: newCompetitorUrl.trim(),
        name: extractBusinessNameFromUrl(newCompetitorUrl),
        posts: []
      };
      setCompetitors(prev => [...prev, newCompetitor]);
      setNewCompetitorUrl('');
      toast.success(`Competitor "${newCompetitor.name}" added successfully!`);
    }
  };

  // Remove Competitor Function
  const removeCompetitor = (competitorId) => {
    setCompetitors(prev => prev.filter(comp => comp.id !== competitorId));
    toast.success('Competitor removed successfully!');
  };

  // Extract business name from URL
  const extractBusinessNameFromUrl = (url) => {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      return domain.split('.')[0].charAt(0).toUpperCase() + domain.split('.')[0].slice(1);
    } catch {
      return 'Competitor';
    }
  };

  // Handle Image Upload
  const handleImageUpload = (event) => {
    const files = Array.from(event.target.files);
    const newImages = files.map(file => ({
      id: Date.now() + Math.random(),
      file,
      url: URL.createObjectURL(file),
      name: file.name
    }));

    setUploadedImages(prev => [...prev, ...newImages]);

    if (uploadedImages.length + newImages.length >= 5) {
      toast.success(`${uploadedImages.length + newImages.length} images uploaded! Ready for analysis.`);
    } else {
      toast.success(`${newImages.length} image(s) uploaded. Upload ${5 - (uploadedImages.length + newImages.length)} more for optimal analysis.`);
    }
  };

  // Remove Uploaded Image
  const removeUploadedImage = (imageId) => {
    setUploadedImages(prev => prev.filter(img => img.id !== imageId));
  };

  // Pull Posts from Social Platform
  const pullPostsFromPlatform = async () => {
    if (!selectedSocialPlatform) {
      toast.error('Please select a social media platform first.');
      return;
    }

    // Get the URL from the state
    const socialUrl = socialMediaUrl.trim();

    if (!socialUrl) {
      toast.error('Please enter a social media page URL first.');
      return;
    }

    setIsExtracting(true);

    try {
      const response = await fetch('/api/social-analysis/scrape-posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          platform: selectedSocialPlatform,
          url: socialUrl,
          options: {
            limit: 25 // Increased for better business analysis
          }
        }),
      });

      const result = await response.json();

      if (result.success) {
        setSocialPosts(result.data.posts);
        toast.success(`Successfully pulled ${result.data.totalPosts} posts from ${selectedSocialPlatform}!`);

        // Show analysis insights if available
        if (result.data.analysis && result.data.analysis.insights) {
          console.log('Analysis insights:', result.data.analysis.insights);
        }
      } else {
        toast.error(result.error || 'Failed to pull posts from social media');

        // Fallback to sample posts for demonstration
        const fallbackPosts = [
          {
            id: 'demo_1',
            platform: selectedSocialPlatform,
            content: `Great service experience at our ${businessInfo.industry || 'business'}! Our team delivered exceptional results for another satisfied customer.`,
            engagement: { likes: 45, comments: 12, shares: 8 },
            date: '2024-01-15',
            performance: 'high'
          },
          {
            id: 'demo_2',
            platform: selectedSocialPlatform,
            content: `Behind the scenes at ${businessInfo.name || 'our business'}! Here's how we ensure quality in everything we do.`,
            engagement: { likes: 32, comments: 7, shares: 5 },
            date: '2024-01-10',
            performance: 'medium'
          },
          {
            id: 'demo_3',
            platform: selectedSocialPlatform,
            content: `Local community event was amazing! Proud to be part of this neighborhood.`,
            engagement: { likes: 67, comments: 23, shares: 15 },
            date: '2024-01-08',
            performance: 'high'
          }
        ];

        setSocialPosts(fallbackPosts);
        toast.info('Showing demo posts - enter a valid social media URL for real scraping');
      }
    } catch (error) {
      console.error('Error pulling posts:', error);
      toast.error('Failed to connect to social media scraping service');
    } finally {
      setIsExtracting(false);
    }
  };

  // Update & Train Function
  const handleUpdateAndTrain = async () => {
    setIsTraining(true);

    try {
      // Compile all business profile data
      const trainingData = {
        businessInfo,
        brandColors,
        socialPosts: [...socialPosts, ...competitorPosts],
        competitors,
        uploadedImages: uploadedImages.map(img => ({ name: img.name, size: img.file.size })),
        localSettings,
        timestamp: new Date().toISOString()
      };

      // Simulate API call to update and train the AI model
      setTimeout(() => {
        setIsTraining(false);
        toast.success('🎉 Business profile updated and AI model trained successfully! Your content generation will now be more personalized.');
      }, 4000);

      // In real implementation, this would:
      // 1. Save all profile data to database
      // 2. Send data to AI training pipeline
      // 3. Update the content generation model
      // 4. Refresh user's content recommendations

    } catch (error) {
      console.error('Error updating and training:', error);
      setIsTraining(false);
      toast.error('Failed to update and train. Please try again.');
    }
  };

  const tabs = [
    { id: 'basic', name: 'Basic Info', icon: Building2 },
    { id: 'website', name: 'Website Extract', icon: Globe },
    { id: 'social', name: 'Social Media', icon: Instagram },
    { id: 'analysis', name: 'Post Analysis', icon: TrendingUp },
    { id: 'local', name: 'Local Settings', icon: MapPin },
    { id: 'brand', name: 'Brand & Design', icon: Palette }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Business Profile</h1>
          <p className="mt-2 text-gray-600">
            Start with Website Extract to auto-fill your business information, then complete your profile for personalized, locally-relevant content
          </p>

          {/* Workflow Guide */}
          <div className="mt-4 bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <Zap className="text-blue-600 mt-0.5" size={20} />
              <div>
                <h3 className="font-medium text-blue-900">Quick Setup Workflow</h3>
                <div className="mt-2 text-sm text-blue-700">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full font-medium">1</span>
                    <span><strong>Website Extract</strong> - Auto-fill from your website URL</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full font-medium">2</span>
                    <span><strong>Review Basic Info</strong> - Edit the auto-filled information as needed</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full font-medium">3</span>
                    <span><strong>Social Media Analysis</strong> - Analyze existing posts for insights</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full font-medium">4</span>
                    <span><strong>Local Settings</strong> - Configure area and promotions</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-blue-600 text-white text-xs px-2 py-0.5 rounded-full font-medium">5</span>
                    <span><strong>Brand & Design</strong> - Set your visual identity and colors</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                  >
                    <Icon size={16} />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          <div className="p-6">
            {/* Basic Info Tab */}
            {activeTab === 'basic' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                      <span>Business Name *</span>
                      {businessInfo.name && extractedData && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-filled</span>
                      )}
                    </label>
                    <input
                      type="text"
                      value={businessInfo.name}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, name: e.target.value }))}
                      className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.name && extractedData
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-300'
                        }`}
                      placeholder="Enter your business name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                      <span>Industry *</span>
                      {businessInfo.industry && extractedData && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-detected</span>
                      )}
                    </label>
                    <select
                      value={businessInfo.industry}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, industry: e.target.value }))}
                      className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.industry && extractedData
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-300'
                        }`}
                    >
                      <option value="">Select your industry</option>
                      {industries.map(industry => (
                        <option key={industry} value={industry}>{industry}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                    <span>Business Description *</span>
                    {businessInfo.description && extractedData && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-extracted</span>
                    )}
                  </label>
                  <textarea
                    value={businessInfo.description}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.description && extractedData
                      ? 'border-green-300 bg-green-50'
                      : 'border-gray-300'
                      }`}
                    placeholder="Describe your business, services, and what makes you unique..."
                  />
                </div>

                {/* Logo Upload Section */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Business Logo
                  </label>
                  <div className="flex items-start space-x-4">
                    {/* Logo Preview */}
                    <div className="flex-shrink-0">
                      {logoPreview ? (
                        <div className="relative">
                          <img
                            src={logoPreview}
                            alt="Logo preview"
                            className="w-20 h-20 object-cover rounded-lg border-2 border-gray-300"
                          />
                          <button
                            onClick={removeLogo}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            <X size={12} />
                          </button>
                        </div>
                      ) : (
                        <div className="w-20 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                          <Building2 className="text-gray-400" size={24} />
                        </div>
                      )}
                    </div>

                    {/* Upload Controls */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <label
                          htmlFor="logo-upload"
                          className="bg-white border border-gray-300 rounded-md px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 cursor-pointer flex items-center space-x-2"
                        >
                          <Upload size={16} />
                          <span>{logo ? 'Change Logo' : 'Upload Logo'}</span>
                        </label>
                        <input
                          id="logo-upload"
                          type="file"
                          accept="image/*"
                          onChange={handleLogoUpload}
                          className="hidden"
                        />
                        {logo && (
                          <button
                            onClick={removeLogo}
                            className="text-red-600 hover:text-red-700 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-2">
                        Upload your business logo. Recommended: Square image, max 5MB, PNG or JPG format.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                      <span>Website URL</span>
                      {businessInfo.website && extractedData && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Source URL</span>
                      )}
                    </label>
                    <input
                      type="url"
                      value={businessInfo.website}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, website: e.target.value }))}
                      className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.website && extractedData
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-300'
                        }`}
                      placeholder="https://yourbusiness.com"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                      <span>Location *</span>
                      {businessInfo.location && extractedData && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-extracted</span>
                      )}
                    </label>
                    <input
                      type="text"
                      value={businessInfo.location}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, location: e.target.value }))}
                      className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.location && extractedData
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-300'
                        }`}
                      placeholder="City, State"
                    />
                  </div>
                </div>

                {/* Phone and Hours */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                      <span>Phone Number</span>
                      {businessInfo.phone && extractedData && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-extracted</span>
                      )}
                    </label>
                    <input
                      type="tel"
                      value={businessInfo.phone}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, phone: e.target.value }))}
                      className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.phone && extractedData
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-300'
                        }`}
                      placeholder="(*************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                      <span>Business Hours</span>
                      {businessInfo.hours && extractedData && (
                        <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-extracted</span>
                      )}
                    </label>
                    <input
                      type="text"
                      value={businessInfo.hours}
                      onChange={(e) => setBusinessInfo(prev => ({ ...prev, hours: e.target.value }))}
                      className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.hours && extractedData
                        ? 'border-green-300 bg-green-50'
                        : 'border-gray-300'
                        }`}
                      placeholder="Mon-Fri 9AM-5PM, Sat 10AM-3PM"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center space-x-2">
                    <span>Target Audience</span>
                    {businessInfo.targetAudience && extractedData && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-generated</span>
                    )}
                  </label>
                  <input
                    type="text"
                    value={businessInfo.targetAudience}
                    onChange={(e) => setBusinessInfo(prev => ({ ...prev, targetAudience: e.target.value }))}
                    className={`w-full rounded-md shadow-sm focus:border-primary-500 focus:ring-primary-500 ${businessInfo.targetAudience && extractedData
                      ? 'border-green-300 bg-green-50'
                      : 'border-gray-300'
                      }`}
                    placeholder="e.g., Homeowners aged 30-55, Local families, Small business owners"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center space-x-2">
                    <span>Brand Voice & Tone</span>
                    {businessInfo.brandVoice && extractedData && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-detected</span>
                    )}
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {brandVoices.map(voice => (
                      <div
                        key={voice.id}
                        onClick={() => setBusinessInfo(prev => ({ ...prev, brandVoice: voice.id }))}
                        className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${businessInfo.brandVoice === voice.id
                          ? 'border-primary-500 bg-primary-50'
                          : 'border-gray-200 hover:border-gray-300'
                          }`}
                      >
                        <h4 className="font-medium text-gray-900">{voice.name}</h4>
                        <p className="text-sm text-gray-600 mt-1">{voice.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Services Section */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3 flex items-center space-x-2">
                    <span>Services & Products *</span>
                    {services.length > 0 && extractedData && (
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">Auto-extracted</span>
                    )}
                  </label>
                  <div className="space-y-3">
                    {/* Add New Service */}
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        value={newService}
                        onChange={(e) => setNewService(e.target.value)}
                        onKeyPress={handleServiceKeyPress}
                        className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="Add a service or product (e.g., Web Design, Coffee, Consulting)"
                      />
                      <button
                        onClick={addService}
                        disabled={!newService.trim()}
                        className="bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center space-x-1"
                      >
                        <Plus size={16} />
                        <span>Add</span>
                      </button>
                    </div>

                    {/* Services List */}
                    {services.length > 0 ? (
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600">
                          {services.length} service{services.length !== 1 ? 's' : ''} added. Click to remove any that don't apply:
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {services.map((service, index) => (
                            <div
                              key={index}
                              className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm flex items-center space-x-2 group hover:bg-red-100 hover:text-red-800 cursor-pointer transition-colors"
                              onClick={() => removeService(service)}
                              title="Click to remove"
                            >
                              <span>{service}</span>
                              <X size={14} className="opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                        <Package className="mx-auto text-gray-400 mb-2" size={24} />
                        <p className="text-gray-600 text-sm">
                          No services added yet. Add your main services and products above.
                        </p>
                        <p className="text-gray-500 text-xs mt-1">
                          These help AI generate relevant content for your business.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Website Extraction Tab */}
            {activeTab === 'website' && (
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Globe className="text-blue-600 mt-0.5" size={20} />
                    <div>
                      <h3 className="font-medium text-blue-900">Website Data Extraction</h3>
                      <p className="text-sm text-blue-700 mt-1">
                        Extract business information, services, and brand details from your website automatically.
                        Any edits made in the Basic Info tab will be reflected here in real-time.
                      </p>
                    </div>
                  </div>
                </div>

                {extractedData && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center space-x-2">
                      <RefreshCw className="text-green-600" size={16} />
                      <p className="text-sm text-green-700">
                        <strong>Live Sync Active:</strong> This data automatically updates when you edit information in the Basic Info tab.
                        Items marked as "(edited)" show your customizations.
                      </p>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Website URL
                    </label>
                    <div className="flex space-x-3">
                      <input
                        type="url"
                        value={websiteUrl}
                        onChange={(e) => setWebsiteUrl(e.target.value)}
                        className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        placeholder="https://yourbusiness.com"
                      />
                      <button
                        onClick={handleWebsiteExtraction}
                        disabled={isExtracting || !websiteUrl}
                        className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:bg-gray-400 flex items-center space-x-2"
                      >
                        {isExtracting ? (
                          <>
                            <RefreshCw size={16} className="animate-spin" />
                            <span>Extracting...</span>
                          </>
                        ) : (
                          <>
                            <Search size={16} />
                            <span>Extract Data</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Extraction Status */}
                  {extractionResults && (
                    <div className={`p-4 rounded-lg border ${extractionResults.success
                      ? 'bg-green-50 border-green-200'
                      : 'bg-red-50 border-red-200'
                      }`}>
                      <div className="flex items-start space-x-3">
                        {extractionResults.success ? (
                          <CheckCircle className="text-green-600 mt-0.5" size={20} />
                        ) : (
                          <AlertCircle className="text-red-600 mt-0.5" size={20} />
                        )}
                        <div>
                          <h4 className={`font-medium ${extractionResults.success
                            ? 'text-green-900'
                            : 'text-red-900'
                            }`}>
                            {extractionResults.success ? 'Extraction Successful' : 'Extraction Failed'}
                          </h4>
                          <p className={`text-sm mt-1 ${extractionResults.success
                            ? 'text-green-700'
                            : 'text-red-700'
                            }`}>
                            {extractionResults.message}
                          </p>
                          {extractionResults.success && extractionResults.enhanced && (
                            <p className="text-sm text-green-600 mt-1">
                              ✨ Data enhanced with AI analysis for better accuracy
                            </p>
                          )}
                          {!extractionResults.success && extractionResults.suggestions && (
                            <p className="text-sm text-red-600 mt-2">
                              💡 Suggestion: {extractionResults.suggestions}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {extractedData && extractionResults?.success && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                      <div className="flex items-center space-x-2 mb-4">
                        <CheckCircle className="text-green-600" size={20} />
                        <h3 className="font-medium text-green-900">Extraction Complete - Profile Auto-Filled!</h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                            <span>Business Details</span>
                            {(businessInfo.name !== extractedData.businessName ||
                              businessInfo.industry !== extractedData.industry ||
                              businessInfo.location !== extractedData.location ||
                              businessInfo.phone !== extractedData.phone ||
                              businessInfo.hours !== extractedData.hours) && (
                                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">Updated</span>
                              )}
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Name:</span>
                              <span className={businessInfo.name !== extractedData.businessName ? 'text-blue-600 font-medium' : ''}>
                                {businessInfo.name || extractedData.businessName}
                              </span>
                              {businessInfo.name !== extractedData.businessName && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Industry:</span>
                              <span className={`${businessInfo.industry !== extractedData.industry ? 'text-blue-600 font-medium' : 'text-blue-600'}`}>
                                {businessInfo.industry || extractedData.industry}
                              </span>
                              {businessInfo.industry !== extractedData.industry && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Location:</span>
                              <span className={businessInfo.location !== extractedData.location ? 'text-blue-600 font-medium' : ''}>
                                {businessInfo.location || extractedData.location || 'Not specified'}
                              </span>
                              {businessInfo.location !== extractedData.location && businessInfo.location && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Phone:</span>
                              <span className={businessInfo.phone !== extractedData.phone ? 'text-blue-600 font-medium' : ''}>
                                {businessInfo.phone || extractedData.phone || 'Not specified'}
                              </span>
                              {businessInfo.phone !== extractedData.phone && businessInfo.phone && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Hours:</span>
                              <span className={businessInfo.hours !== extractedData.hours ? 'text-blue-600 font-medium' : ''}>
                                {businessInfo.hours || extractedData.hours || 'Not specified'}
                              </span>
                              {businessInfo.hours !== extractedData.hours && businessInfo.hours && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                            <span>Auto-Detected Profile</span>
                            {(businessInfo.brandVoice !== extractedData.brandVoice ||
                              businessInfo.targetAudience !== extractedData.targetAudience) && (
                                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">Updated</span>
                              )}
                          </h4>
                          <div className="space-y-2 text-sm">
                            <div>
                              <span className="font-medium">Brand Voice:</span>
                              <span className={`text-purple-600 capitalize ${businessInfo.brandVoice !== extractedData.brandVoice ? 'font-medium' : ''}`}>
                                {businessInfo.brandVoice || extractedData.brandVoice}
                              </span>
                              {businessInfo.brandVoice !== extractedData.brandVoice && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                            <div>
                              <span className="font-medium">Target Audience:</span>
                              <span className={`text-green-600 ${businessInfo.targetAudience !== extractedData.targetAudience ? 'font-medium' : ''}`}>
                                {businessInfo.targetAudience || extractedData.targetAudience || 'Not specified'}
                              </span>
                              {businessInfo.targetAudience !== extractedData.targetAudience && businessInfo.targetAudience && (
                                <span className="text-xs text-blue-600 ml-2">(edited)</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="mt-4">
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                          <span>Services Detected</span>
                          {services.length !== extractedData.services?.length && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">Updated</span>
                          )}
                          <span className="text-xs text-gray-500">({services.length} services)</span>
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {services.length > 0 ? services.map((service, idx) => (
                            <span key={idx} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {service}
                            </span>
                          )) : (
                            <span className="text-gray-500 text-sm">No services added yet</span>
                          )}
                        </div>
                        {services.length !== extractedData.services?.length && (
                          <p className="text-xs text-blue-600 mt-2">
                            Services have been edited in Basic Info tab
                          </p>
                        )}
                      </div>

                      <div className="mt-4">
                        <h4 className="font-medium text-gray-900 mb-2 flex items-center space-x-2">
                          <span>Business Description</span>
                          {businessInfo.description !== extractedData.description && (
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">Updated</span>
                          )}
                        </h4>
                        <p className={`text-sm text-gray-700 bg-white p-3 rounded border ${businessInfo.description !== extractedData.description ? 'border-blue-300' : ''}`}>
                          {businessInfo.description || extractedData.description || 'No description available'}
                        </p>
                        {businessInfo.description !== extractedData.description && businessInfo.description && (
                          <p className="text-xs text-blue-600 mt-2">
                            Description has been edited in Basic Info tab
                          </p>
                        )}
                      </div>

                      <div className="mt-4">
                        <h4 className="font-medium text-gray-900 mb-2">Key Features</h4>
                        <div className="flex flex-wrap gap-2">
                          {extractedData.keyFeatures.map((feature, idx) => (
                            <span key={idx} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-green-200">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                          <p className="text-sm text-blue-800">
                            ✅ <strong>Profile automatically updated!</strong> All extracted data has been applied to your Basic Info tab.
                            Please review and edit as needed.
                          </p>
                        </div>
                        <button
                          onClick={() => setActiveTab('basic')}
                          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
                        >
                          <Settings size={16} />
                          <span>Review & Edit Basic Info</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Social Media Tab */}
            {activeTab === 'social' && (
              <div className="space-y-6">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Instagram className="text-purple-600 mt-0.5" size={20} />
                    <div>
                      <h3 className="font-medium text-purple-900">Social Media Analysis & Competitor Benchmarking</h3>
                      <p className="text-sm text-purple-700 mt-1">
                        Pull posts from your social media accounts, upload images for analysis, or add multiple competitors for benchmarking.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Pull Posts from Your Social Media */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Your Social Media Posts</h3>
                  <p className="text-sm text-gray-600 mb-4">Analyze your existing social media content to understand your brand voice and audience preferences.</p>

                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Option 1: Pull Posts from URL
                    </label>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Select Platform
                      </label>
                      <select
                        value={selectedSocialPlatform}
                        onChange={(e) => {
                          setSelectedSocialPlatform(e.target.value);
                          setSocialMediaUrl(''); // Clear URL when platform changes
                        }}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                      >
                        <option value="">Choose a platform...</option>
                        <option value="Facebook">Facebook</option>
                        <option value="Instagram">Instagram</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Social Media URL
                      </label>
                      <input
                        type="url"
                        value={socialMediaUrl}
                        onChange={(e) => setSocialMediaUrl(e.target.value)}
                        placeholder={selectedSocialPlatform ? `Your ${selectedSocialPlatform} page URL...` : 'Select platform first...'}
                        disabled={!selectedSocialPlatform}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 disabled:bg-gray-100"
                      />
                    </div>
                  </div>

                  {/* Instagram Notice */}
                  {selectedSocialPlatform === 'Instagram' && (
                    <div className="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
                      <div className="flex items-start">
                        <svg className="w-5 h-5 text-amber-400 mt-0.5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                        <div>
                          <h4 className="text-sm font-medium text-amber-800">
                            Instagram Access Limited
                          </h4>
                          <p className="text-sm text-amber-700 mt-1">
                            Instagram restricts automated scraping for privacy. This affects all social media tools industry-wide. Consider using Instagram Business API or manual upload for analysis.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-end">
                      <button
                        onClick={pullPostsFromPlatform}
                        disabled={!selectedSocialPlatform || isExtracting}
                        className="w-full bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 flex items-center justify-center space-x-2"
                      >
                        {isExtracting ? (
                          <>
                            <RefreshCw size={16} className="animate-spin" />
                            <span>Pulling Posts...</span>
                          </>
                        ) : (
                          <>
                            <Download size={16} />
                            <span>Pull Posts</span>
                          </>
                        )}
                      </button>
                    </div>
                  </div>

                  {socialPosts.length > 0 && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h4 className="font-medium text-green-900 mb-2">✅ {socialPosts.length} Posts Analyzed</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {socialPosts.slice(0, 4).map(post => (
                          <div key={post.id} className="bg-white rounded-lg p-3 border border-green-200">
                            {/* Post Image */}
                            {post.media?.url && (
                              <div className="mb-3">
                                <img
                                  src={post.media.url}
                                  alt="Post media"
                                  className="w-full h-32 object-cover rounded-lg"
                                  onError={(e) => {
                                    e.target.style.display = 'none';
                                  }}
                                />
                              </div>
                            )}

                            {/* Post Content */}
                            <p className="text-sm text-gray-700 mb-2">{post.content.substring(0, 100)}...</p>

                            {/* Engagement Metrics */}
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-4 text-xs text-gray-500">
                                <span>👍 {post.engagement.likes}</span>
                                <span>💬 {post.engagement.comments}</span>
                                <span>🔄 {post.engagement.shares}</span>
                                <span className={`px-2 py-1 rounded ${post.performance === 'high' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                                  {post.performance}
                                </span>
                              </div>

                              {/* Media Type Indicator */}
                              {post.media?.hasMedia && (
                                <div className="flex items-center space-x-1 text-xs text-blue-600">
                                  {post.media.type === 'video' ? (
                                    <>
                                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z" />
                                      </svg>
                                      <span>Video</span>
                                    </>
                                  ) : post.media.type === 'photo' ? (
                                    <>
                                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                                      </svg>
                                      <span>Photo</span>
                                    </>
                                  ) : null}
                                </div>
                              )}
                            </div>

                            {/* Scraped with indicator */}
                            {post.scrapedWith && (
                              <div className="mt-2 text-xs text-gray-400">
                                Scraped with: {post.scrapedWith}
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Alternative: Upload Post Files */}
                  <div className="border-t border-gray-200 pt-6 mt-6">
                    <h4 className="text-md font-medium text-gray-900 mb-3">Option 2: Upload Post Screenshots/Exports</h4>
                    <div className="space-y-3">
                      <input
                        type="file"
                        multiple
                        accept="image/*,.pdf,.txt,.csv"
                        onChange={(e) => handleFileUpload('general', e.target.files)}
                        className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-purple-100 file:text-purple-700 hover:file:bg-purple-200"
                        disabled={isExtracting}
                      />
                      <p className="text-sm text-gray-600">
                        Upload screenshots, exported posts, or CSV files from any social media platform. Supports images, PDFs, and text files.
                      </p>
                      {uploadedFiles.general && uploadedFiles.general.length > 0 && (
                        <div className="bg-gray-50 rounded-lg p-3">
                          <p className="text-sm font-medium text-gray-700 mb-2">Uploaded files ({uploadedFiles.general.length}):</p>
                          <div className="space-y-2">
                            {uploadedFiles.general.map((file, idx) => (
                              <div key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                                <Upload size={14} />
                                <span>{file.name}</span>
                                <span className="text-gray-400">({(file.size / 1024).toFixed(1)}KB)</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>



                {/* Multiple Competitors Section */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Competitor Analysis</h3>
                    <span className="text-sm text-gray-500">Optional for benchmarking</span>
                  </div>

                  <div className="space-y-4 mb-4">
                    {/* URL Input Option */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Option 1: Add Competitor URL
                      </label>
                      <div className="flex space-x-2">
                        <input
                          type="url"
                          value={newCompetitorUrl}
                          onChange={(e) => setNewCompetitorUrl(e.target.value)}
                          placeholder="Enter competitor website or social media URL..."
                          className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                        />
                        <button
                          onClick={addCompetitor}
                          disabled={!newCompetitorUrl.trim()}
                          className="bg-orange-600 text-white px-4 py-2 rounded-md hover:bg-orange-700 disabled:bg-gray-400 flex items-center space-x-2"
                        >
                          <span>Add</span>
                        </button>
                      </div>
                    </div>

                    {/* File Upload Option */}
                    <div className="border-t border-gray-200 pt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Option 2: Upload Competitor Content
                      </label>
                      <div className="space-y-2">
                        <input
                          type="file"
                          multiple
                          accept="image/*,.pdf,.txt,.csv"
                          onChange={(e) => handleFileUpload('competitors', e.target.files)}
                          className="w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-orange-100 file:text-orange-700 hover:file:bg-orange-200"
                          disabled={isExtracting}
                        />
                        <p className="text-sm text-gray-600">
                          Upload screenshots or exports of competitor posts for analysis. Supports images, PDFs, and text files.
                        </p>
                        {uploadedFiles.competitors && uploadedFiles.competitors.length > 0 && (
                          <div className="bg-gray-50 rounded-lg p-3">
                            <p className="text-sm font-medium text-gray-700 mb-2">Uploaded competitor files ({uploadedFiles.competitors.length}):</p>
                            <div className="space-y-2">
                              {uploadedFiles.competitors.map((file, idx) => (
                                <div key={idx} className="flex items-center space-x-2 text-sm text-gray-600">
                                  <Upload size={14} />
                                  <span>{file.name}</span>
                                  <span className="text-gray-400">({(file.size / 1024).toFixed(1)}KB)</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {competitors.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-900">Added Competitors ({competitors.length})</h4>
                      {competitors.map(competitor => (
                        <div key={competitor.id} className="flex items-center justify-between bg-gray-50 rounded-lg p-3">
                          <div>
                            <p className="font-medium text-gray-900">{competitor.name}</p>
                            <p className="text-sm text-gray-600">{competitor.url}</p>
                          </div>
                          <button
                            onClick={() => removeCompetitor(competitor.id)}
                            className="text-red-600 hover:text-red-800 p-1"
                          >
                            <span className="sr-only">Remove</span>
                            ×
                          </button>
                        </div>
                      ))}

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mt-4">
                        <p className="text-sm text-blue-800">
                          <strong>💡 Smart Benchmarking:</strong> We'll analyze competitor content for insights while ensuring all generated content maintains your unique brand voice and style.
                        </p>
                      </div>
                    </div>
                  )}

                  {competitors.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <p className="text-sm">No competitors added yet.</p>
                      <p className="text-xs mt-1">Add competitor URLs to benchmark against their content strategy.</p>
                    </div>
                  )}
                </div>



                {(socialPosts.length > 0 || competitorPosts.length > 0) && (
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-medium text-gray-900">Analyzed Posts</h3>
                      <div className="flex items-center space-x-4 text-sm text-gray-500">
                        {socialPosts.length > 0 && <span>{socialPosts.length} your posts</span>}
                        {competitorPosts.length > 0 && <span>{competitorPosts.length} competitor posts</span>}
                      </div>
                    </div>
                    <div className="space-y-6">
                      {/* Your Posts Section */}
                      {socialPosts.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                            <span>Your Content Analysis</span>
                            <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                              {socialPosts.length} posts
                            </span>
                          </h4>
                          <div className="space-y-3">
                            {socialPosts.slice(0, 3).map(post => (
                              <div key={post.id} className="bg-white p-4 rounded-lg border border-gray-200">
                                <div className="flex items-start justify-between mb-2">
                                  <span className={`text-xs px-2 py-1 rounded-full font-medium ${post.source === 'upload'
                                    ? 'bg-purple-100 text-purple-800'
                                    : 'bg-blue-100 text-blue-800'
                                    }`}>
                                    {post.source === 'upload' ? '📁 Uploaded' : '🔗 From URL'}
                                  </span>
                                  {post.fileName && (
                                    <span className="text-xs text-gray-400">{post.fileName}</span>
                                  )}
                                </div>
                                <p className="text-sm text-gray-800 mb-3">{post.content}</p>
                                <div className="flex items-center justify-between text-xs text-gray-500">
                                  <div className="flex items-center space-x-4">
                                    <span>👍 {post.engagement.likes}</span>
                                    <span>💬 {post.engagement.comments}</span>
                                    <span>🔄 {post.engagement.shares}</span>
                                    <span className="text-gray-400">{post.date}</span>
                                  </div>
                                  <span className={`px-2 py-1 rounded-full ${post.performance === 'excellent' ? 'bg-green-100 text-green-800' :
                                    post.performance === 'high' ? 'bg-blue-100 text-blue-800' :
                                      post.performance === 'good' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-gray-100 text-gray-800'
                                    }`}>
                                    {post.performance}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Competitor Posts Section */}
                      {competitorPosts.length > 0 && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                            <span>Competitor Benchmarking</span>
                            <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">
                              {competitorPosts.length} posts
                            </span>
                          </h4>
                          <div className="bg-orange-50 border border-orange-200 rounded-lg p-3 mb-3">
                            <p className="text-sm text-orange-800">
                              <strong>📊 Benchmarking Purpose:</strong> These competitor insights help identify successful content patterns.
                              All your generated content will maintain YOUR brand voice, colors, and unique identity.
                            </p>
                          </div>
                          <div className="space-y-3">
                            {competitorPosts.slice(0, 3).map(post => (
                              <div key={post.id} className="bg-white p-4 rounded-lg border border-orange-200">
                                <div className="flex items-start justify-between mb-2">
                                  <span className="bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full font-medium">
                                    🏆 Competitor Insight
                                  </span>
                                  <span className="text-xs text-gray-500">{post.competitorName}</span>
                                </div>
                                <p className="text-sm text-gray-800 mb-3">{post.content}</p>
                                <div className="flex items-center justify-between text-xs text-gray-500">
                                  <div className="flex items-center space-x-4">
                                    <span>👍 {post.engagement.likes}</span>
                                    <span>💬 {post.engagement.comments}</span>
                                    <span>🔄 {post.engagement.shares}</span>
                                    <span className="text-gray-400">{post.date}</span>
                                  </div>
                                  <span className={`px-2 py-1 rounded-full ${post.performance === 'excellent' ? 'bg-green-100 text-green-800' :
                                    post.performance === 'high' ? 'bg-blue-100 text-blue-800' :
                                      'bg-gray-100 text-gray-800'
                                    }`}>
                                    {post.performance}
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Combined Insights */}
                      {(socialPosts.length > 3 || competitorPosts.length > 3) && (
                        <div className="text-center pt-4 border-t border-gray-200">
                          <p className="text-sm text-gray-500">
                            Showing recent posts. All {socialPosts.length + competitorPosts.length} posts are analyzed for content strategy insights.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Local Settings Tab */}
            {activeTab === 'local' && (
              <div className="space-y-6">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <MapPin className="text-green-600 mt-0.5" size={20} />
                    <div>
                      <h3 className="font-medium text-green-900">Local Content Settings</h3>
                      <p className="text-sm text-green-700 mt-1">
                        Configure how we find local events, trends, and community happenings for your content.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Search Radius
                    </label>
                    <div className="space-y-3">
                      <input
                        type="range"
                        min="5"
                        max="50"
                        value={localSettings.radius}
                        onChange={(e) => setLocalSettings(prev => ({ ...prev, radius: parseInt(e.target.value) }))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>5 miles</span>
                        <span className="font-medium text-primary-600">{localSettings.radius} miles</span>
                        <span>50 miles</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Content Sources
                    </label>
                    <div className="space-y-2">
                      {[
                        { key: 'includeEvents', label: 'Local Events', icon: Calendar },
                        { key: 'includeTrends', label: 'Local Trends', icon: TrendingUp },
                        { key: 'includeWeather', label: 'Weather Integration', icon: Zap }
                      ].map(({ key, label, icon: Icon }) => (
                        <label key={key} className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={localSettings[key]}
                            onChange={(e) => setLocalSettings(prev => ({ ...prev, [key]: e.target.checked }))}
                            className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                          />
                          <Icon size={16} className="text-gray-400" />
                          <span className="text-sm text-gray-700">{label}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Event Types to Include
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {eventTypes.map(eventType => (
                      <label key={eventType.id} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                        <input
                          type="checkbox"
                          checked={localSettings.eventTypes.includes(eventType.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setLocalSettings(prev => ({
                                ...prev,
                                eventTypes: [...prev.eventTypes, eventType.id]
                              }));
                            } else {
                              setLocalSettings(prev => ({
                                ...prev,
                                eventTypes: prev.eventTypes.filter(type => type !== eventType.id)
                              }));
                            }
                          }}
                          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500 mt-0.5"
                        />
                        <div>
                          <div className="font-medium text-gray-900 text-sm">{eventType.name}</div>
                          <div className="text-xs text-gray-500">{eventType.description}</div>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Discount Settings */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Discount & Promotions</h3>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={localSettings.discounts.enabled}
                        onChange={(e) => setLocalSettings(prev => ({
                          ...prev,
                          discounts: { ...prev.discounts, enabled: e.target.checked }
                        }))}
                        className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <span className="text-sm text-gray-700">Enable discount promotions</span>
                    </label>
                  </div>

                  {localSettings.discounts.enabled && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Discount Percentage
                          </label>
                          <div className="relative">
                            <input
                              type="number"
                              min="1"
                              max="100"
                              value={localSettings.discounts.percentage}
                              onChange={(e) => setLocalSettings(prev => ({
                                ...prev,
                                discounts: { ...prev.discounts, percentage: e.target.value }
                              }))}
                              className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 pr-8"
                              placeholder="20"
                            />
                            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500">%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Promo Code (Optional)
                          </label>
                          <input
                            type="text"
                            value={localSettings.discounts.promoCode}
                            onChange={(e) => setLocalSettings(prev => ({
                              ...prev,
                              discounts: { ...prev.discounts, promoCode: e.target.value.toUpperCase() }
                            }))}
                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            placeholder="SAVE20"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            Valid Until
                          </label>
                          <input
                            type="date"
                            value={localSettings.discounts.validUntil}
                            onChange={(e) => setLocalSettings(prev => ({
                              ...prev,
                              discounts: { ...prev.discounts, validUntil: e.target.value }
                            }))}
                            className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Discount Description
                        </label>
                        <textarea
                          value={localSettings.discounts.description}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            discounts: { ...prev.discounts, description: e.target.value }
                          }))}
                          className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 h-20 resize-none"
                          placeholder="Get 20% off your first service! Perfect for new customers looking to try our premium services."
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Terms & Conditions
                        </label>
                        <textarea
                          value={localSettings.discounts.terms}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            discounts: { ...prev.discounts, terms: e.target.value }
                          }))}
                          className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 h-16 resize-none"
                          placeholder="Valid for new customers only. Cannot be combined with other offers. Expires on specified date."
                        />
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <p className="text-sm text-blue-800">
                          <strong>💡 Smart Integration:</strong> This discount information will be automatically included in relevant social media posts to drive engagement and conversions.
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="text-yellow-600 mt-0.5" size={20} />
                    <div>
                      <h4 className="font-medium text-yellow-900">Smart Content Strategy</h4>
                      <p className="text-sm text-yellow-700 mt-1">
                        We'll use real-time local data for the next 7 days, and seasonal/educational content for days 8-30 to ensure accuracy.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Social Media Analysis Tab */}
            {activeTab === 'analysis' && (
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <TrendingUp className="text-blue-600 mt-0.5" size={20} />
                    <div>
                      <h3 className="font-medium text-blue-900">Social Media Post Analysis</h3>
                      <p className="text-sm text-blue-700 mt-1">
                        Analyze your existing social media posts to understand what content performs best and train AI to generate similar high-performing content.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Platform Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Select Social Media Platform
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {[
                      { id: 'facebook', name: 'Facebook', icon: Facebook, color: 'blue' },
                      { id: 'instagram', name: 'Instagram', icon: Instagram, color: 'pink' },
                      { id: 'twitter', name: 'Twitter/X', icon: Twitter, color: 'sky' },
                      { id: 'linkedin', name: 'LinkedIn', icon: Linkedin, color: 'indigo' }
                    ].map((platform) => (
                      <button
                        key={platform.id}
                        onClick={() => setSelectedPlatform(platform.id)}
                        className={`p-4 rounded-lg border-2 transition-all ${selectedPlatform === platform.id
                          ? `border-${platform.color}-500 bg-${platform.color}-50`
                          : 'border-gray-200 hover:border-gray-300'
                          }`}
                      >
                        <platform.icon
                          className={`mx-auto mb-2 ${selectedPlatform === platform.id
                            ? `text-${platform.color}-600`
                            : 'text-gray-400'
                            }`}
                          size={24}
                        />
                        <p className={`text-sm font-medium ${selectedPlatform === platform.id
                          ? `text-${platform.color}-900`
                          : 'text-gray-700'
                          }`}>
                          {platform.name}
                        </p>
                        {connectedPlatforms[platform.id]?.connected && (
                          <div className="mt-1">
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Connected
                            </span>
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>

                {/* Analysis Actions */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h4 className="font-medium text-gray-900 mb-4">Analysis Options</h4>

                  <div className="space-y-4">
                    {/* Simulate Analysis (for testing) */}
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div>
                        <h5 className="font-medium text-gray-900">Demo Analysis</h5>
                        <p className="text-sm text-gray-600">
                          Run a simulated analysis with sample data to see how the feature works
                        </p>
                      </div>
                      <button
                        onClick={() => simulateAnalysis(selectedPlatform)}
                        disabled={analysisLoading}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center space-x-2"
                      >
                        {analysisLoading ? (
                          <>
                            <RefreshCw className="animate-spin" size={16} />
                            <span>Analyzing...</span>
                          </>
                        ) : (
                          <>
                            <TrendingUp size={16} />
                            <span>Run Demo</span>
                          </>
                        )}
                      </button>
                    </div>

                    {/* Real Analysis (requires API keys) */}
                    <div className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div>
                        <h5 className="font-medium text-gray-900">Real Analysis</h5>
                        <p className="text-sm text-gray-600">
                          Connect your {selectedPlatform} account to analyze real posts (requires API access)
                        </p>
                      </div>
                      <button
                        disabled
                        className="bg-gray-300 text-gray-500 px-4 py-2 rounded-md cursor-not-allowed flex items-center space-x-2"
                      >
                        <Settings size={16} />
                        <span>Setup Required</span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Analysis Results */}
                {analysisData && (
                  <div className="bg-white border border-gray-200 rounded-lg p-6">
                    <h4 className="font-medium text-gray-900 mb-4 flex items-center space-x-2">
                      <CheckCircle className="text-green-600" size={20} />
                      <span>Analysis Results for {analysisData.platform}</span>
                    </h4>

                    {/* Summary Stats */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                      <div className="bg-blue-50 p-4 rounded-lg">
                        <p className="text-sm text-blue-600 font-medium">Total Posts</p>
                        <p className="text-2xl font-bold text-blue-900">{analysisData.totalPosts}</p>
                      </div>
                      <div className="bg-green-50 p-4 rounded-lg">
                        <p className="text-sm text-green-600 font-medium">Avg Engagement</p>
                        <p className="text-2xl font-bold text-green-900">{analysisData.engagementAnalysis?.averageEngagement || 0}</p>
                      </div>
                      <div className="bg-purple-50 p-4 rounded-lg">
                        <p className="text-sm text-purple-600 font-medium">Best Content</p>
                        <p className="text-2xl font-bold text-purple-900">
                          {Object.keys(analysisData.engagementAnalysis?.engagementByType || {})[0] || 'N/A'}
                        </p>
                      </div>
                      <div className="bg-orange-50 p-4 rounded-lg">
                        <p className="text-sm text-orange-600 font-medium">Time Range</p>
                        <p className="text-2xl font-bold text-orange-900">{analysisData.timeRange?.span || 'N/A'}</p>
                      </div>
                    </div>

                    {/* Key Insights */}
                    <div className="mb-6">
                      <h5 className="font-medium text-gray-900 mb-3">Key Insights & Recommendations</h5>
                      <div className="space-y-2">
                        {analysisData.bestPractices?.recommendations?.map((recommendation, index) => (
                          <div key={index} className="flex items-start space-x-2">
                            <Zap className="text-yellow-500 mt-0.5 flex-shrink-0" size={16} />
                            <p className="text-sm text-gray-700">{recommendation}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Content Analysis */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h5 className="font-medium text-gray-900 mb-3">Content Patterns</h5>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Average Length:</span>
                            <span className="text-sm font-medium">{analysisData.contentAnalysis?.averageLength} chars</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Tone:</span>
                            <span className="text-sm font-medium capitalize">{analysisData.contentAnalysis?.toneAnalysis}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Top Hashtags:</span>
                            <span className="text-sm font-medium">{analysisData.contentAnalysis?.hashtags?.length || 0}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h5 className="font-medium text-gray-900 mb-3">Performance Insights</h5>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Media Impact:</span>
                            <span className={`text-sm font-medium ${analysisData.engagementAnalysis?.mediaImpact?.impact === 'positive'
                              ? 'text-green-600'
                              : 'text-red-600'
                              }`}>
                              {analysisData.engagementAnalysis?.mediaImpact?.improvement || 0}% improvement
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-600">Best Format:</span>
                            <span className="text-sm font-medium">
                              {Object.entries(analysisData.bestPractices?.successfulFormats || {})
                                .sort(([, a], [, b]) => b - a)[0]?.[0] || 'N/A'}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* AI Training Status */}
                    <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="text-green-600" size={20} />
                        <h5 className="font-medium text-green-900">AI Training Data Prepared</h5>
                      </div>
                      <p className="text-sm text-green-700 mt-1">
                        Your analysis has been processed and is ready to enhance AI content generation.
                        The AI will now use these insights to create posts that match your successful content patterns.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}



            {/* Brand & Design Tab */}
            {activeTab === 'brand' && (
              <div className="space-y-6">
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <Palette className="text-purple-600 mt-0.5" size={20} />
                    <div>
                      <h3 className="font-medium text-purple-900">Brand Colors & Visual Identity</h3>
                      <p className="text-sm text-purple-700 mt-1">
                        Define your brand colors to ensure consistent visual identity across all generated content.
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Primary Color
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-12 h-12 rounded-lg border-2 border-gray-300 shadow-sm"
                          style={{ backgroundColor: brandColors.primary }}
                        ></div>
                        <div className="flex-1">
                          <input
                            type="color"
                            value={brandColors.primary}
                            onChange={(e) => setBrandColors(prev => ({ ...prev, primary: e.target.value }))}
                            className="w-full h-10 rounded-md border border-gray-300 cursor-pointer"
                          />
                        </div>
                      </div>
                      <input
                        type="text"
                        value={brandColors.primary}
                        onChange={(e) => setBrandColors(prev => ({ ...prev, primary: e.target.value }))}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 font-mono text-sm"
                        placeholder="#3B82F6"
                      />
                      <p className="text-xs text-gray-500">Main brand color for buttons and highlights</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Secondary Color
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-12 h-12 rounded-lg border-2 border-gray-300 shadow-sm"
                          style={{ backgroundColor: brandColors.secondary }}
                        ></div>
                        <div className="flex-1">
                          <input
                            type="color"
                            value={brandColors.secondary}
                            onChange={(e) => setBrandColors(prev => ({ ...prev, secondary: e.target.value }))}
                            className="w-full h-10 rounded-md border border-gray-300 cursor-pointer"
                          />
                        </div>
                      </div>
                      <input
                        type="text"
                        value={brandColors.secondary}
                        onChange={(e) => setBrandColors(prev => ({ ...prev, secondary: e.target.value }))}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 font-mono text-sm"
                        placeholder="#10B981"
                      />
                      <p className="text-xs text-gray-500">Supporting color for accents and backgrounds</p>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Accent Color
                    </label>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-12 h-12 rounded-lg border-2 border-gray-300 shadow-sm"
                          style={{ backgroundColor: brandColors.accent }}
                        ></div>
                        <div className="flex-1">
                          <input
                            type="color"
                            value={brandColors.accent}
                            onChange={(e) => setBrandColors(prev => ({ ...prev, accent: e.target.value }))}
                            className="w-full h-10 rounded-md border border-gray-300 cursor-pointer"
                          />
                        </div>
                      </div>
                      <input
                        type="text"
                        value={brandColors.accent}
                        onChange={(e) => setBrandColors(prev => ({ ...prev, accent: e.target.value }))}
                        className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 font-mono text-sm"
                        placeholder="#F59E0B"
                      />
                      <p className="text-xs text-gray-500">Call-to-action and emphasis color</p>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Color Palette Preview</h4>
                  <div className="flex space-x-4">
                    <div className="text-center">
                      <div
                        className="w-16 h-16 rounded-lg shadow-sm mb-2"
                        style={{ backgroundColor: brandColors.primary }}
                      ></div>
                      <p className="text-xs text-gray-600 font-medium">Primary</p>
                      <p className="text-xs text-gray-500 font-mono">{brandColors.primary}</p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-16 h-16 rounded-lg shadow-sm mb-2"
                        style={{ backgroundColor: brandColors.secondary }}
                      ></div>
                      <p className="text-xs text-gray-600 font-medium">Secondary</p>
                      <p className="text-xs text-gray-500 font-mono">{brandColors.secondary}</p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-16 h-16 rounded-lg shadow-sm mb-2"
                        style={{ backgroundColor: brandColors.accent }}
                      ></div>
                      <p className="text-xs text-gray-600 font-medium">Accent</p>
                      <p className="text-xs text-gray-500 font-mono">{brandColors.accent}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <CheckCircle className="text-blue-600 mt-0.5" size={20} />
                    <div>
                      <h4 className="font-medium text-blue-900">Smart Color Integration</h4>
                      <p className="text-sm text-blue-700 mt-1">
                        These brand colors will be automatically applied to all generated social media content, ensuring consistent visual identity across platforms.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )
            }
          </div >
        </div >

        {/* Update & Train Button */}
        <div className="flex justify-end space-x-4">
          <button
            onClick={() => {
              toast.success('Business profile saved successfully!');
            }}
            className="bg-gray-600 text-white px-6 py-3 rounded-md hover:bg-gray-700 flex items-center space-x-2"
          >
            <Save size={16} />
            <span>Save Only</span>
          </button>

          <button
            onClick={handleUpdateAndTrain}
            disabled={isTraining}
            className="bg-primary-600 text-white px-6 py-3 rounded-md hover:bg-primary-700 disabled:bg-primary-400 flex items-center space-x-2"
          >
            {isTraining ? (
              <>
                <RefreshCw size={16} className="animate-spin" />
                <span>Training AI...</span>
              </>
            ) : (
              <>
                <Zap size={16} />
                <span>Update & Train AI</span>
              </>
            )}
          </button>
        </div>
      </div >
    </div >
  );
};

export default BusinessProfile;
