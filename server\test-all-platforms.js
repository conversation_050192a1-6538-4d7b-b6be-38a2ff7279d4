const axios = require('axios');

async function testAllPlatforms() {
  console.log('🌐 Testing All Social Media Platforms\n');

  const baseURL = 'http://localhost:5000/api/social-analysis';

  const platforms = [
    {
      name: 'Facebook',
      url: 'https://www.facebook.com/PayaFinance/',
      expected: 'Real data with images',
      supported: true
    },
    {
      name: 'Instagram', 
      url: 'https://www.instagram.com/microsoft/',
      expected: 'Real data (if available)',
      supported: true
    },
    {
      name: 'LinkedIn',
      url: 'https://www.linkedin.com/company/microsoft/',
      expected: 'Enhanced demo data',
      supported: false
    },
    {
      name: 'Twitter',
      url: 'https://twitter.com/microsoft',
      expected: 'Enhanced demo data',
      supported: false
    }
  ];

  console.log('🎯 PLATFORM SUPPORT STATUS:');
  console.log('═'.repeat(50));
  platforms.forEach(platform => {
    console.log(`${platform.supported ? '✅' : '⚠️ '} ${platform.name}: ${platform.expected}`);
  });
  console.log('');

  for (const platform of platforms) {
    try {
      console.log(`🧪 Testing ${platform.name}...`);
      console.log(`📱 URL: ${platform.url}`);
      
      const response = await axios.post(`${baseURL}/scrape-posts`, {
        platform: platform.name,
        url: platform.url,
        options: { limit: 10 }
      });

      if (response.data.success) {
        const data = response.data.data;
        console.log(`✅ ${platform.name} Results:`);
        console.log(`   📊 Posts: ${data.totalPosts}`);
        console.log(`   🔧 Method: ${data.scrapedWith}`);
        console.log(`   📈 Analysis: ${data.analysis ? 'Available' : 'None'}`);
        
        // Check if it's demo data
        const isDemo = data.isDemo || data.scrapedWith.includes('Demo') || data.scrapedWith.includes('Fallback');
        console.log(`   🎭 Data Type: ${isDemo ? 'Demo/Fallback' : 'Real scraped data'}`);
        
        // Show platform status if available
        if (data.platformStatus) {
          console.log(`   ℹ️  Status: ${data.platformStatus.reason}`);
        }

        // Show sample posts
        console.log(`   📝 Sample Posts:`);
        data.posts.slice(0, 2).forEach((post, index) => {
          console.log(`      ${index + 1}. "${post.content.substring(0, 60)}..."`);
          console.log(`         Engagement: ${post.engagement.likes + post.engagement.comments + post.engagement.shares}`);
          console.log(`         Has Media: ${post.media?.hasMedia ? 'Yes' : 'No'}`);
          if (post.media?.url) {
            console.log(`         Image URL: ${post.media.url.substring(0, 80)}...`);
          }
        });

        // Verify expectations
        if (platform.supported && !isDemo) {
          console.log(`   🎉 SUCCESS: Getting real data as expected!`);
        } else if (!platform.supported && isDemo) {
          console.log(`   ✅ EXPECTED: Demo data for unsupported platform`);
        } else {
          console.log(`   ⚠️  UNEXPECTED: Data type doesn't match expectations`);
        }

      } else {
        console.log(`❌ ${platform.name} failed: ${response.data.error}`);
      }

    } catch (error) {
      console.log(`❌ ${platform.name} error: ${error.response?.data?.error || error.message}`);
    }
    
    console.log(''); // Empty line for readability
  }

  console.log('🎊 COMPREHENSIVE PLATFORM SUMMARY:');
  console.log('═'.repeat(60));
  
  console.log('\n✅ FULLY SUPPORTED PLATFORMS:');
  console.log('🔹 Facebook: Real post extraction with perfect image URLs');
  console.log('🔹 Instagram: Real post extraction capability (actor available)');
  
  console.log('\n⚠️  DEMO-ONLY PLATFORMS:');
  console.log('🔹 LinkedIn: Enhanced demo data (platform restrictions)');
  console.log('🔹 Twitter: Enhanced demo data (API limitations)');
  
  console.log('\n🎯 WHAT THIS MEANS FOR YOU:');
  console.log('✅ Facebook: Perfect for your Paya Finance business analysis');
  console.log('✅ Instagram: Ready for business Instagram accounts');
  console.log('📊 LinkedIn/Twitter: Demo data for UI testing and development');
  
  console.log('\n🚀 RECOMMENDATIONS:');
  console.log('1. 🎯 Focus on Facebook for real business analysis');
  console.log('2. 📱 Test Instagram with your business account');
  console.log('3. 🔧 Use LinkedIn/Twitter demo mode for UI development');
  console.log('4. 📈 Consider manual post upload for unsupported platforms');
  
  console.log('\n🌐 READY FOR TESTING:');
  console.log('• Open http://localhost:3000');
  console.log('• Navigate to Business Profile → Social Media');
  console.log('• Try Facebook with your Paya Finance URL for real data');
  console.log('• Try LinkedIn/Twitter to see enhanced demo mode');
  
  console.log('\n💎 ENTERPRISE-GRADE MULTI-PLATFORM SUPPORT! 🎊');
}

testAllPlatforms().catch(console.error);
