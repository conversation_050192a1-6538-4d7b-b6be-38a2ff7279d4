import React, { createContext, useContext, useState, useCallback } from 'react';
import { toast } from 'react-hot-toast';

const PostsContext = createContext();

export const usePosts = () => {
  const context = useContext(PostsContext);
  if (!context) {
    throw new Error('usePosts must be used within a PostsProvider');
  }
  return context;
};

export const PostsProvider = ({ children }) => {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(false);

  // Generate unique ID for posts
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  };

  // Add a single post to calendar
  const addPostToCalendar = useCallback(async (post) => {
    try {
      setLoading(true);

      // Create a properly formatted post for the calendar
      const calendarPost = {
        id: generateId(),
        title: post.content.split('\n')[0].substring(0, 50) + '...',
        content: post.content,
        platform: post.platform,
        scheduledDate: post.date,
        status: 'scheduled',
        hashtags: post.hashtags || [],
        weather: post.weather || '',
        localEvent: post.localEvent || '',
        imageDesign: post.imageDesign || null,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Add to posts array
      setPosts(prevPosts => {
        const newPosts = [...prevPosts, calendarPost];
        return newPosts.sort((a, b) => new Date(a.scheduledDate) - new Date(b.scheduledDate));
      });

      toast.success(`Post added to calendar for ${post.date}`);
      return { success: true, post: calendarPost };

    } catch (error) {
      console.error('Error adding post to calendar:', error);
      toast.error('Failed to add post to calendar');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Add multiple posts to calendar
  const addPostsToCalendar = useCallback(async (postsArray) => {
    try {
      setLoading(true);

      const calendarPosts = postsArray.map(post => {
        // If post already has proper format (from API), use it
        if (post.scheduledDate && post.id) {
          return {
            ...post,
            id: post.id, // Keep original ID from database
            title: post.title || (post.content || '').split('\n')[0].substring(0, 50) + '...',
            status: post.status || 'generated'
          };
        }
        // Otherwise, format it (from GenerateContent page)
        return {
          id: generateId(),
          title: (post.content || '').split('\n')[0].substring(0, 50) + '...',
          content: post.content,
          platform: post.platform,
          scheduledDate: post.date,
          status: 'scheduled',
          hashtags: post.hashtags || [],
          weather: post.weather || '',
          localEvent: post.localEvent || '',
          imageDesign: post.imageDesign || null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
      });

      // Replace posts instead of adding to avoid duplicates
      setPosts(prevPosts => {
        // Remove existing posts with same IDs
        const existingIds = new Set(calendarPosts.map(p => p.id));
        const filteredPosts = prevPosts.filter(p => !existingIds.has(p.id));

        // Add new posts
        const newPosts = [...filteredPosts, ...calendarPosts];
        return newPosts.sort((a, b) => new Date(a.scheduledDate) - new Date(b.scheduledDate));
      });

      toast.success(`${calendarPosts.length} posts loaded to calendar`);
      return { success: true, posts: calendarPosts };

    } catch (error) {
      console.error('Error adding posts to calendar:', error);
      toast.error('Failed to add posts to calendar');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Get posts for a specific date range
  const getPostsForDateRange = useCallback((startDate, endDate) => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    return posts.filter(post => {
      const postDate = new Date(post.scheduledDate);
      return postDate >= start && postDate <= end;
    });
  }, [posts]);

  // Get posts for a specific date
  const getPostsForDate = useCallback((date) => {
    const targetDate = new Date(date).toDateString();
    return posts.filter(post => {
      const postDate = new Date(post.scheduledDate).toDateString();
      return postDate === targetDate;
    });
  }, [posts]);

  // Update a post
  const updatePost = useCallback(async (postId, updates) => {
    try {
      setLoading(true);

      setPosts(prevPosts =>
        prevPosts.map(post =>
          post.id === postId
            ? { ...post, ...updates, updatedAt: new Date().toISOString() }
            : post
        )
      );

      toast.success('Post updated successfully');
      return { success: true };

    } catch (error) {
      console.error('Error updating post:', error);
      toast.error('Failed to update post');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Delete a post
  const deletePost = useCallback(async (postId) => {
    try {
      setLoading(true);

      setPosts(prevPosts => prevPosts.filter(post => post.id !== postId));

      toast.success('Post deleted successfully');
      return { success: true };

    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Failed to delete post');
      return { success: false, error: error.message };
    } finally {
      setLoading(false);
    }
  }, []);

  // Clear all posts
  const clearAllPosts = useCallback(() => {
    setPosts([]);
    toast.success('All posts cleared');
  }, []);

  const value = {
    posts,
    loading,
    addPostToCalendar,
    addPostsToCalendar,
    getPostsForDateRange,
    getPostsForDate,
    updatePost,
    deletePost,
    clearAllPosts
  };

  return (
    <PostsContext.Provider value={value}>
      {children}
    </PostsContext.Provider>
  );
};

export default PostsContext;
