const { ApifyClient } = require('apify-client');
require('dotenv').config();

async function searchWorkingActors() {
  console.log('🔍 Searching for Working Social Media Actors\n');

  const client = new ApifyClient({
    token: process.env.APIFY_API_TOKEN,
  });

  // More comprehensive list of actors to try
  const actorsToTest = [
    // LinkedIn actors
    { platform: 'LinkedIn', actors: [
      'voyager/linkedin-company-scraper',
      'drobnikj/linkedin-company-posts-scraper',
      'apify/linkedin-company-posts-scraper',
      'lukaskrivka/linkedin-company-scraper',
      'apify/linkedin-posts-scraper'
    ]},
    
    // Twitter/X actors
    { platform: 'Twitter', actors: [
      'apify/twitter-scraper',
      'apify/x-scraper',
      'drobnikj/twitter-scraper',
      'lukaskrivka/twitter-scraper'
    ]},
    
    // Instagram actors
    { platform: 'Instagram', actors: [
      'apify/instagram-scraper',
      'drobnikj/instagram-scraper',
      'lukaskrivka/instagram-scraper'
    ]}
  ];

  for (const { platform, actors } of actorsToTest) {
    console.log(`\n🔍 Testing ${platform} Actors:`);
    console.log('='.repeat(40));
    
    let foundWorking = false;
    
    for (const actorId of actors) {
      try {
        console.log(`\nTesting: ${actorId}`);
        
        const actor = await client.actor(actorId).get();
        
        if (actor) {
          console.log(`✅ FOUND: ${actorId}`);
          console.log(`   Name: ${actor.name}`);
          console.log(`   Description: ${actor.description?.substring(0, 80)}...`);
          console.log(`   Total runs: ${actor.stats?.totalRuns || 0}`);
          console.log(`   Last modified: ${new Date(actor.modifiedAt).toLocaleDateString()}`);
          
          if (!foundWorking) {
            console.log(`   🎯 RECOMMENDED FOR ${platform}: ${actorId}`);
            foundWorking = true;
          }
          
        } else {
          console.log(`❌ Not found: ${actorId}`);
        }
        
      } catch (error) {
        console.log(`❌ Error with ${actorId}: ${error.message}`);
      }
    }
    
    if (!foundWorking) {
      console.log(`\n⚠️  No working ${platform} actors found`);
    }
  }

  console.log('\n\n🎯 FINAL RECOMMENDATIONS:');
  console.log('='.repeat(50));
  console.log('✅ Facebook: apify/facebook-posts-scraper (WORKING)');
  console.log('✅ Instagram: apify/instagram-scraper (WORKING)');
  console.log('❓ LinkedIn: Need to find working actor');
  console.log('❓ Twitter: Need to find working actor');
  
  console.log('\n💡 SOLUTION OPTIONS:');
  console.log('1. Use working actors found above');
  console.log('2. Implement manual post upload for unsupported platforms');
  console.log('3. Focus on Facebook + Instagram (most common for businesses)');
  console.log('4. Add fallback demo data for unsupported platforms');
}

searchWorkingActors().catch(console.error);
