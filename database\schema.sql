-- LocalPost.ai Database Schema
-- Run this in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    business_name VA<PERSON>HAR(255) NOT NULL,
    business_type VARCHAR(100) NOT NULL,
    location VARCHAR(255) NOT NULL,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    subscription_tier VARCHAR(50) DEFAULT 'starter',
    subscription_status VARCHAR(50) DEFAULT 'active',
    stripe_customer_id VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Business profiles table
CREATE TABLE public.business_profiles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    website_url VARCHAR(500),
    social_handles JSONB DEFAULT '{}',
    hours_operation JSONB DEFAULT '{}',
    services JSONB DEFAULT '[]',
    brand_colors JSONB DEFAULT '{}',
    visual_style JSONB DEFAULT '{}',
    writing_style JSONB DEFAULT '{}',
    target_audience TEXT,
    unique_selling_points TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Generated posts table
CREATE TABLE public.generated_posts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    date_scheduled DATE NOT NULL,
    platform VARCHAR(50) NOT NULL,
    content_text TEXT NOT NULL,
    image_url VARCHAR(500),
    image_prompt TEXT,
    hashtags TEXT[],
    status VARCHAR(50) DEFAULT 'generated',
    engagement_data JSONB DEFAULT '{}',
    local_context JSONB DEFAULT '{}',
    weather_context JSONB DEFAULT '{}',
    events_context JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date_scheduled, platform)
);

-- Local data cache table
CREATE TABLE public.local_data_cache (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    location_key VARCHAR(255) NOT NULL,
    date DATE NOT NULL,
    weather_data JSONB DEFAULT '{}',
    events_data JSONB DEFAULT '[]',
    holidays JSONB DEFAULT '[]',
    cached_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    UNIQUE(location_key, date)
);

-- Brand analysis table
CREATE TABLE public.brand_analysis (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    analysis_type VARCHAR(50) NOT NULL, -- 'social_media', 'website', 'manual'
    source_url VARCHAR(500),
    visual_elements JSONB DEFAULT '{}',
    color_palette JSONB DEFAULT '{}',
    typography_style JSONB DEFAULT '{}',
    content_themes JSONB DEFAULT '[]',
    posting_patterns JSONB DEFAULT '{}',
    brand_voice_analysis JSONB DEFAULT '{}',
    analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content templates table
CREATE TABLE public.content_templates (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    template_text TEXT NOT NULL,
    variables JSONB DEFAULT '[]',
    platforms VARCHAR(50)[] DEFAULT ARRAY['facebook', 'instagram'],
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscription history table
CREATE TABLE public.subscription_history (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    stripe_subscription_id VARCHAR(255),
    plan_name VARCHAR(100) NOT NULL,
    status VARCHAR(50) NOT NULL,
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_users_subscription ON public.users(subscription_tier, subscription_status);
CREATE INDEX idx_generated_posts_user_date ON public.generated_posts(user_id, date_scheduled);
CREATE INDEX idx_generated_posts_status ON public.generated_posts(status);
CREATE INDEX idx_local_data_cache_location_date ON public.local_data_cache(location_key, date);
CREATE INDEX idx_local_data_cache_expires ON public.local_data_cache(expires_at);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_business_profiles_updated_at BEFORE UPDATE ON public.business_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_generated_posts_updated_at BEFORE UPDATE ON public.generated_posts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_templates_updated_at BEFORE UPDATE ON public.content_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.business_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.generated_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.brand_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscription_history ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own business profile" ON public.business_profiles FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own posts" ON public.generated_posts FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own brand analysis" ON public.brand_analysis FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own templates" ON public.content_templates FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own subscription history" ON public.subscription_history FOR SELECT USING (auth.uid() = user_id);

-- Local data cache is readable by all authenticated users (shared resource)
ALTER TABLE public.local_data_cache ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can read local data cache" ON public.local_data_cache FOR SELECT TO authenticated USING (true);
CREATE POLICY "Service role can manage local data cache" ON public.local_data_cache FOR ALL TO service_role USING (true);
