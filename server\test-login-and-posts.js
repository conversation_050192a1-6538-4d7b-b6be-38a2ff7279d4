require('dotenv').config();
const axios = require('axios');

const API_BASE = 'http://localhost:5000/api';

async function testLoginAndPosts() {
  console.log('🚀 Testing Login and Posts with Real Data...\n');

  try {
    // Test 1: Login with existing user
    console.log('1️⃣ Testing user login...');

    const loginData = {
      email: '<EMAIL>',
      password: 'demo123456'
    };

    const loginResponse = await axios.post(`${API_BASE}/auth/login`, loginData);
    console.log('✅ User login successful');
    console.log('📧 Email:', loginResponse.data.user?.email);
    console.log('🏢 Business:', loginResponse.data.user?.businessName);
    console.log('📍 Location:', loginResponse.data.user?.location);
    console.log('💳 Subscription:', loginResponse.data.user?.subscriptionTier);

    const authToken = loginResponse.data.session?.access_token;
    if (!authToken) {
      console.log('❌ No auth token received');
      return;
    }

    const headers = { Authorization: `Bearer ${authToken}` };

    // Test 2: Get user profile
    console.log('\n2️⃣ Testing user profile retrieval...');

    const profileResponse = await axios.get(`${API_BASE}/auth/me`, { headers });
    console.log('✅ User profile retrieved');
    console.log('🆔 User ID:', profileResponse.data.user?.id);
    console.log('📊 Subscription Status:', profileResponse.data.user?.subscriptionStatus);

    // Test 3: Get existing posts
    console.log('\n3️⃣ Testing post retrieval...');

    const postsResponse = await axios.get(`${API_BASE}/content/posts`, { headers });
    console.log('✅ Posts retrieved successfully');
    console.log('📊 Total posts:', postsResponse.data.posts?.length || 0);

    if (postsResponse.data.posts?.length > 0) {
      console.log('📅 Existing posts:');
      postsResponse.data.posts.forEach((post, index) => {
        console.log(`   ${index + 1}. ${post.date_scheduled} - ${post.platform}: ${post.content_text?.substring(0, 60)}...`);
      });
    } else {
      console.log('📝 No posts found - database is ready for content generation');
    }

    // Test 4: Try content generation (will use mock data if OpenAI not configured)
    console.log('\n4️⃣ Testing content generation...');

    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const generateData = {
      startDate: today.toISOString().split('T')[0],
      endDate: tomorrow.toISOString().split('T')[0],
      platforms: ['facebook', 'instagram'],
      regenerate: true
    };

    try {
      const generateResponse = await axios.post(`${API_BASE}/content/generate`, generateData, { headers });
      console.log('✅ Content generation successful');
      console.log('📝 Generated posts:', generateResponse.data.generatedPosts?.length || 0);
      console.log('⚠️  Errors:', generateResponse.data.errors?.length || 0);

      if (generateResponse.data.generatedPosts?.length > 0) {
        console.log('📄 Sample generated post:');
        const firstPost = generateResponse.data.generatedPosts[0];
        console.log(`   Platform: ${firstPost.platform}`);
        console.log(`   Date: ${firstPost.date_scheduled}`);
        console.log(`   Content: ${firstPost.content_text?.substring(0, 100)}...`);
        console.log(`   Hashtags: ${firstPost.hashtags?.join(', ') || 'None'}`);
      }
    } catch (genError) {
      console.log('⚠️  Content generation failed:', genError.response?.data?.error || genError.message);
      if (genError.response?.status === 403) {
        console.log('💡 This is likely due to subscription status - user might be in trial mode');
      }
    }

    // Test 5: Get posts again to see if new ones were created
    console.log('\n5️⃣ Testing post retrieval after generation...');

    const newPostsResponse = await axios.get(`${API_BASE}/content/posts`, { headers });
    console.log('✅ Posts retrieved successfully');
    console.log('📊 Total posts now:', newPostsResponse.data.posts?.length || 0);

    console.log('\n🎉 Real API Integration Test Complete!');
    console.log('✅ Authentication is working');
    console.log('✅ Database integration is functional');
    console.log('✅ API endpoints are responding correctly');
    console.log('🔗 Ready for frontend integration!');

  } catch (error) {
    console.log('❌ API test failed:', error.response?.data?.error || error.message);
    if (error.response?.status) {
      console.log('📊 Status Code:', error.response.status);
    }
  }
}

// Make sure server is running
console.log('🔍 Testing if server is running...');
axios.get('http://localhost:5000/health')
  .then(() => {
    console.log('✅ Server is running\n');
    testLoginAndPosts();
  })
  .catch(() => {
    console.log('❌ Server is not running. Please start the server first.');
    console.log('👉 Run: npm run server:dev');
  });
