const { supabase } = require('./config/database');
const axios = require('axios');
require('dotenv').config();

async function testSupabaseAuthentication() {
  try {
    console.log('🔐 Testing Supabase Authentication Flow...\n');

    const email = '<EMAIL>';
    const password = 'qwerty123456';

    // Step 1: Authenticate with Supabase directly (like the frontend does)
    console.log('1. Testing Supabase authentication:');
    
    const { data, error: authError } = await supabase.auth.signInWithPassword({
      email: email,
      password: password
    });

    if (authError) {
      console.log('❌ Supabase authentication failed:', authError.message);
      return;
    }

    console.log('✅ Supabase authentication successful!');
    console.log('   User ID:', data.user?.id);
    console.log('   Email:', data.user?.email);
    console.log('   Session exists:', !!data.session);
    console.log('   Access token exists:', !!data.session?.access_token);

    // Step 2: Test content generation with Supabase token
    console.log('\n2. Testing content generation with Supabase token:');
    
    if (!data.session?.access_token) {
      console.log('❌ No access token available');
      return;
    }

    try {
      const contentResponse = await axios.post('http://localhost:5000/api/content/generate', {
        startDate: '2024-01-15',
        endDate: '2024-01-21',
        platforms: ['facebook', 'instagram'],
        contentTypes: ['promotional']
      }, {
        headers: {
          'Authorization': `Bearer ${data.session.access_token}`,
          'Content-Type': 'application/json'
        }
      });
      
      console.log('✅ Content generation successful!');
      console.log('   Generated posts:', contentResponse.data.posts?.length || 0);
      
      if (contentResponse.data.posts && contentResponse.data.posts.length > 0) {
        const firstPost = contentResponse.data.posts[0];
        console.log('   Sample post:');
        console.log('     Text:', firstPost.content_text?.substring(0, 100) + '...');
        console.log('     Platform:', firstPost.platform);
        console.log('     Hashtags:', firstPost.hashtags?.join(', '));
      }
      
    } catch (error) {
      console.log('❌ Content generation failed:');
      console.log('   Status:', error.response?.status);
      console.log('   Error:', error.response?.data);
      
      // Let's also test the auth middleware directly
      console.log('\n   Testing auth middleware with this token:');
      try {
        const { data: { user }, error: userError } = await supabase.auth.getUser(data.session.access_token);
        if (userError) {
          console.log('   ❌ Token validation failed:', userError.message);
        } else {
          console.log('   ✅ Token is valid for user:', user.email);
        }
      } catch (middlewareError) {
        console.log('   ❌ Middleware test failed:', middlewareError.message);
      }
    }

    // Step 3: Test profile access
    console.log('\n3. Testing profile access:');
    
    try {
      const profileResponse = await axios.get('http://localhost:5000/api/profile', {
        headers: {
          'Authorization': `Bearer ${data.session.access_token}`
        }
      });
      
      console.log('✅ Profile access successful!');
      console.log('   Business name:', profileResponse.data.business_name || 'Not set');
      console.log('   Business type:', profileResponse.data.business_type || 'Not set');
      
    } catch (error) {
      console.log('❌ Profile access failed:', error.response?.status, error.response?.data);
    }

    // Step 4: Sign out
    console.log('\n4. Cleaning up - signing out:');
    await supabase.auth.signOut();
    console.log('✅ Signed out successfully');

    console.log('\n🎯 Supabase Authentication Test Complete!');
    console.log('\nIf content generation is still failing in the UI:');
    console.log('1. Check browser console for authentication errors');
    console.log('2. Make sure you are properly logged in');
    console.log('3. Try refreshing the page after login');
    console.log('4. Check if your business profile is complete');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSupabaseAuthentication();
