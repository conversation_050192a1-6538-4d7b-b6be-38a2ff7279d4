import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  User,
  Save
} from 'lucide-react';
import LoadingSpinner from '../components/LoadingSpinner';
import toast from 'react-hot-toast';

const Profile = () => {
  const { user, apiCall, updateProfile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const [profileData, setProfileData] = useState({
    // Personal account info
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    timezone: '',

    // Account preferences
    emailNotifications: true,
    weeklyReports: true,
    contentReminders: true
  });

  const [brandAnalysis, setBrandAnalysis] = useState(null);

  useEffect(() => {
    fetchProfileData();
  }, []);

  const fetchProfileData = async () => {
    try {
      setLoading(true);

      const response = await apiCall('/api/profile');

      if (response.ok) {
        const data = await response.json();

        setProfileData({
          businessName: data.user.businessName || '',
          businessType: data.user.businessType || '',
          location: data.user.location || '',
          city: data.user.city || '',
          state: data.user.state || '',
          zipCode: data.user.zipCode || '',
          websiteUrl: data.businessProfile?.websiteUrl || '',
          services: data.businessProfile?.services || [],
          targetAudience: data.businessProfile?.targetAudience || '',
          uniqueSellingPoints: data.businessProfile?.uniqueSellingPoints || [],
          brandColors: data.businessProfile?.brandColors?.primary || [],
          visualStyle: data.businessProfile?.visualStyle?.style || '',
          writingTone: data.businessProfile?.writingStyle?.tone || ''
        });

        setBrandAnalysis(data.brandAnalysis);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast.error('Failed to load profile data');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setProfileData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleArrayInput = (field, value) => {
    const items = value.split(',').map(item => item.trim()).filter(item => item);
    setProfileData(prev => ({
      ...prev,
      [field]: items
    }));
  };

  const saveAccountSettings = async () => {
    try {
      setSaving(true);

      const response = await apiCall('/api/account/settings', {
        method: 'PUT',
        body: JSON.stringify({
          firstName: profileData.firstName,
          lastName: profileData.lastName,
          email: profileData.email,
          phone: profileData.phone,
          timezone: profileData.timezone,
          emailNotifications: profileData.emailNotifications,
          weeklyReports: profileData.weeklyReports,
          contentReminders: profileData.contentReminders
        })
      });

      if (response.ok) {
        const data = await response.json();
        await updateProfile(data.user);
        toast.success('Account settings updated successfully');
      } else {
        toast.error('Failed to update account settings');
      }
    } catch (error) {
      console.error('Error saving account settings:', error);
      toast.error('Failed to update account settings');
    } finally {
      setSaving(false);
    }
  };

  const timezones = [
    'America/New_York', 'America/Chicago', 'America/Denver', 'America/Los_Angeles',
    'America/Phoenix', 'America/Anchorage', 'Pacific/Honolulu',
    'UTC', 'Europe/London', 'Europe/Paris', 'Asia/Tokyo'
  ];

  if (loading) {
    return <LoadingSpinner text="Loading profile..." />;
  }

  // No tabs needed - just Account Settings

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Account Settings</h1>
        <p className="text-gray-600 mt-2">
          Manage your personal account settings and notification preferences
        </p>
      </div>

      {/* Account Settings Content */}
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Account Settings</h2>
          <button
            onClick={saveAccountSettings}
            disabled={saving}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50"
          >
            <Save size={16} />
            <span>{saving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>

        <div className="space-y-8">
          {/* Personal Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="label">First Name</label>
                <input
                  type="text"
                  value={profileData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  className="input-field"
                  placeholder="Enter your first name"
                />
              </div>

              <div>
                <label className="label">Last Name</label>
                <input
                  type="text"
                  value={profileData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  className="input-field"
                  placeholder="Enter your last name"
                />
              </div>

              <div>
                <label className="label">Email Address</label>
                <input
                  type="email"
                  value={profileData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="input-field"
                  placeholder="Enter your email"
                />
              </div>

              <div>
                <label className="label">Phone Number</label>
                <input
                  type="tel"
                  value={profileData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  className="input-field"
                  placeholder="Enter your phone number"
                />
              </div>

              <div>
                <label className="label">Timezone</label>
                <select
                  value={profileData.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  className="input-field"
                >
                  <option value="">Select timezone</option>
                  {timezones.map((tz) => (
                    <option key={tz} value={tz}>{tz}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Notification Preferences */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="emailNotifications"
                  checked={profileData.emailNotifications}
                  onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="emailNotifications" className="ml-2 block text-sm text-gray-900">
                  Email notifications for important updates
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="weeklyReports"
                  checked={profileData.weeklyReports}
                  onChange={(e) => handleInputChange('weeklyReports', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="weeklyReports" className="ml-2 block text-sm text-gray-900">
                  Weekly performance reports
                </label>
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="contentReminders"
                  checked={profileData.contentReminders}
                  onChange={(e) => handleInputChange('contentReminders', e.target.checked)}
                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                />
                <label htmlFor="contentReminders" className="ml-2 block text-sm text-gray-900">
                  Content generation reminders
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
