require('dotenv').config();
const { supabaseAdmin } = require('./config/database');

async function checkExistingUsers() {
  console.log('🔍 Checking existing users in database\n');

  try {
    // Get all users
    const { data: users, error: usersError } = await supabaseAdmin
      .from('users')
      .select('*')
      .limit(10);

    if (usersError) {
      console.log('❌ Error fetching users:', usersError.message);
      return;
    }

    console.log(`📊 Found ${users.length} users in database:`);
    
    users.forEach((user, index) => {
      console.log(`\n${index + 1}. User:`);
      console.log(`   👤 ID: ${user.id}`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   🏢 Business: ${user.business_name || 'N/A'}`);
      console.log(`   📍 Location: ${user.location || 'N/A'}`);
      console.log(`   💳 Subscription: ${user.subscription_status || 'N/A'}`);
    });

    // Check if our test user ID exists
    const testUserId = '550e8400-e29b-41d4-a716-446655440000';
    const testUser = users.find(u => u.id === testUserId);
    
    if (testUser) {
      console.log('\n✅ Test user found in database!');
      console.log('🎉 Content generation should work without foreign key errors');
      
      // Test content generation now
      console.log('\n🧪 Testing content generation with existing user...');
      
      const axios = require('axios');
      
      try {
        const generateData = {
          startDate: new Date().toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
          platforms: ['facebook'],
          regenerate: true
        };

        const response = await axios.post('http://localhost:5000/api/content/generate', generateData, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token'
          },
          timeout: 30000
        });

        console.log('✅ Content generation successful!');
        
        if (response.data.generatedPosts?.length > 0) {
          const post = response.data.generatedPosts[0];
          console.log('\n📱 Generated Post:');
          console.log(`   📄 Content: ${post.content_text}`);
          console.log(`   🏷️  Hashtags: ${post.hashtags?.join(', ') || 'None'}`);
          console.log(`   🌤️  Weather: ${post.weather_context || 'None'}`);
          
          const isAIGenerated = post.content_text && 
                               post.content_text.length > 50 && 
                               !post.content_text.includes('Demo Restaurant, we\'re passionate');
          
          console.log(`   🤖 Content Type: ${isAIGenerated ? '✅ Real AI Generated!' : '⚠️  Mock/Fallback Data'}`);
        }
        
        if (response.data.errors?.length > 0) {
          console.log('\n⚠️  Errors:');
          response.data.errors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${typeof error === 'object' ? JSON.stringify(error) : error}`);
          });
        } else {
          console.log('\n🎉 NO ERRORS! Content generation is working perfectly!');
        }
        
      } catch (contentError) {
        console.log('\n❌ Content generation test failed:', contentError.message);
      }
      
    } else {
      console.log('\n❌ Test user not found in database');
      console.log('💡 Need to create user with different ID or email');
    }

  } catch (error) {
    console.log('❌ Check failed:', error.message);
  }
}

// Run check
checkExistingUsers().catch(console.error);
