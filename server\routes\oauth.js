const express = require('express');
const router = express.Router();
const oauthService = require('../services/oauthService');
const { authenticateUser } = require('../middleware/auth');

// In-memory storage for user OAuth tokens (in production, use database)
const userTokens = new Map();

// Helper function to store user tokens
function storeUserToken(userId, platform, tokenData) {
  if (!userTokens.has(userId)) {
    userTokens.set(userId, {});
  }
  userTokens.get(userId)[platform] = tokenData;
}

// Helper function to get user tokens
function getUserToken(userId, platform) {
  const userTokensData = userTokens.get(userId);
  return userTokensData ? userTokensData[platform] : null;
}

// Helper function to remove user tokens
function removeUserToken(userId, platform) {
  const userTokensData = userTokens.get(userId);
  if (userTokensData) {
    delete userTokensData[platform];
  }
}

// Get user's connected social media accounts
router.get('/connections', authenticateUser, (req, res) => {
  try {
    const userId = req.user.id;
    const userTokensData = userTokens.get(userId) || {};

    const connections = {
      facebook: {
        connected: !!userTokensData.facebook,
        user: userTokensData.facebook?.user || null,
        pages: userTokensData.facebook?.pages || [],
        connectedAt: userTokensData.facebook?.connectedAt || null
      },
      instagram: {
        connected: !!userTokensData.instagram,
        user: userTokensData.instagram?.user || null,
        connectedAt: userTokensData.instagram?.connectedAt || null
      }
    };

    res.json({ success: true, connections });
  } catch (error) {
    console.error('Get connections error:', error);
    res.status(500).json({ success: false, error: 'Failed to get connections' });
  }
});

// Initiate Facebook OAuth
router.get('/facebook/auth', authenticateUser, (req, res) => {
  try {
    const userId = req.user.id;
    const authUrl = oauthService.getFacebookAuthUrl(userId);

    res.json({
      success: true,
      authUrl,
      message: 'Redirect user to this URL to authorize Facebook access'
    });
  } catch (error) {
    console.error('Facebook auth initiation error:', error);
    res.status(500).json({ success: false, error: 'Failed to initiate Facebook authentication' });
  }
});

// Facebook OAuth callback
router.get('/facebook/callback', async (req, res) => {
  try {
    const { code, state, error } = req.query;

    if (error) {
      return res.redirect(`${process.env.CLIENT_URL}/connect-social-media?error=facebook_denied`);
    }

    if (!code || !state) {
      return res.redirect(`${process.env.CLIENT_URL}/connect-social-media?error=facebook_invalid`);
    }

    // Extract userId from state (in production, decode from JWT or session)
    // For now, we'll need to handle this differently
    // This is a simplified version - in production, you'd store state with user mapping

    res.redirect(`${process.env.CLIENT_URL}/connect-social-media?facebook_code=${code}&state=${state}`);

  } catch (error) {
    console.error('Facebook callback error:', error);
    res.redirect(`${process.env.CLIENT_URL}/connect-social-media?error=facebook_error`);
  }
});

// Complete Facebook OAuth (called from frontend with code)
router.post('/facebook/complete', authenticateUser, async (req, res) => {
  try {
    const { code, state } = req.body;
    const userId = req.user.id;

    if (!code || !state) {
      return res.status(400).json({ success: false, error: 'Missing authorization code or state' });
    }

    const tokenData = await oauthService.exchangeFacebookCode(code, state, userId);
    storeUserToken(userId, 'facebook', tokenData);

    res.json({
      success: true,
      message: 'Facebook account connected successfully',
      user: tokenData.user,
      pages: tokenData.pages
    });

  } catch (error) {
    console.error('Facebook OAuth completion error:', error);
    res.status(400).json({ success: false, error: error.message });
  }
});

// Initiate Instagram OAuth
router.get('/instagram/auth', authenticateUser, (req, res) => {
  try {
    const userId = req.user.id;
    const authUrl = oauthService.getInstagramAuthUrl(userId);

    res.json({
      success: true,
      authUrl,
      message: 'Redirect user to this URL to authorize Instagram access'
    });
  } catch (error) {
    console.error('Instagram auth initiation error:', error);
    res.status(500).json({ success: false, error: 'Failed to initiate Instagram authentication' });
  }
});

// Instagram OAuth callback
router.get('/instagram/callback', async (req, res) => {
  try {
    const { code, state, error } = req.query;

    if (error) {
      return res.redirect(`${process.env.CLIENT_URL}/connect-social-media?error=instagram_denied`);
    }

    if (!code || !state) {
      return res.redirect(`${process.env.CLIENT_URL}/connect-social-media?error=instagram_invalid`);
    }

    res.redirect(`${process.env.CLIENT_URL}/connect-social-media?instagram_code=${code}&state=${state}`);

  } catch (error) {
    console.error('Instagram callback error:', error);
    res.redirect(`${process.env.CLIENT_URL}/connect-social-media?error=instagram_error`);
  }
});

// Complete Instagram OAuth (called from frontend with code)
router.post('/instagram/complete', authenticateUser, async (req, res) => {
  try {
    const { code, state } = req.body;
    const userId = req.user.id;

    if (!code || !state) {
      return res.status(400).json({ success: false, error: 'Missing authorization code or state' });
    }

    const tokenData = await oauthService.exchangeInstagramCode(code, state, userId);
    storeUserToken(userId, 'instagram', tokenData);

    res.json({
      success: true,
      message: 'Instagram account connected successfully',
      user: tokenData.user
    });

  } catch (error) {
    console.error('Instagram OAuth completion error:', error);
    res.status(400).json({ success: false, error: error.message });
  }
});

// Disconnect Facebook
router.delete('/facebook/disconnect', authenticateUser, (req, res) => {
  try {
    const userId = req.user.id;
    removeUserToken(userId, 'facebook');

    res.json({ success: true, message: 'Facebook account disconnected successfully' });
  } catch (error) {
    console.error('Facebook disconnect error:', error);
    res.status(500).json({ success: false, error: 'Failed to disconnect Facebook account' });
  }
});

// Disconnect Instagram
router.delete('/instagram/disconnect', authenticateUser, (req, res) => {
  try {
    const userId = req.user.id;
    removeUserToken(userId, 'instagram');

    res.json({ success: true, message: 'Instagram account disconnected successfully' });
  } catch (error) {
    console.error('Instagram disconnect error:', error);
    res.status(500).json({ success: false, error: 'Failed to disconnect Instagram account' });
  }
});

// Get Facebook posts using OAuth token
router.get('/facebook/posts', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const { pageId, limit = 25 } = req.query;

    const tokenData = getUserToken(userId, 'facebook');
    if (!tokenData) {
      return res.status(401).json({ success: false, error: 'Facebook account not connected' });
    }

    // Use page token if pageId provided, otherwise use user token
    let accessToken = tokenData.accessToken;
    if (pageId && tokenData.pages) {
      const page = tokenData.pages.find(p => p.id === pageId);
      if (page && page.access_token) {
        accessToken = page.access_token;
      }
    }

    const posts = await oauthService.getFacebookPosts(accessToken, pageId || 'me', limit);

    res.json({
      success: true,
      data: {
        posts,
        totalPosts: posts.length,
        platform: 'Facebook',
        scrapedWith: 'Facebook Graph API',
        analysis: {
          averageEngagement: posts.reduce((sum, post) => sum + post.engagement.likes + post.engagement.comments, 0) / posts.length,
          highPerformingPosts: posts.filter(post => post.performance === 'high').length,
          contentThemes: ['Facebook API Content'],
          postingFrequency: 'Regular',
          engagementTrends: 'Stable'
        }
      }
    });

  } catch (error) {
    console.error('Facebook posts error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get Instagram posts using OAuth token
router.get('/instagram/posts', authenticateUser, async (req, res) => {
  try {
    const userId = req.user.id;
    const { limit = 25 } = req.query;

    const tokenData = getUserToken(userId, 'instagram');
    if (!tokenData) {
      return res.status(401).json({ success: false, error: 'Instagram account not connected' });
    }

    const posts = await oauthService.getInstagramPosts(tokenData.accessToken, limit);

    res.json({
      success: true,
      data: {
        posts,
        totalPosts: posts.length,
        platform: 'Instagram',
        scrapedWith: 'Instagram Graph API',
        analysis: {
          averageEngagement: posts.reduce((sum, post) => sum + post.engagement.likes + post.engagement.comments, 0) / posts.length,
          highPerformingPosts: posts.filter(post => post.performance === 'high').length,
          contentThemes: ['Instagram API Content'],
          postingFrequency: 'Regular',
          engagementTrends: 'Stable'
        }
      }
    });

  } catch (error) {
    console.error('Instagram posts error:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

module.exports = router;
