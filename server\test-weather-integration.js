require('dotenv').config();
const axios = require('axios');

async function testWeatherIntegration() {
  console.log('🌤️  Testing Weather API Integration...\n');

  // Check if API key is configured
  const apiKey = process.env.OPENWEATHER_API_KEY;
  
  if (!apiKey || apiKey === 'your_openweather_api_key') {
    console.log('❌ OpenWeatherMap API key not configured');
    console.log('📝 Please update OPENWEATHER_API_KEY in server/.env');
    console.log('🔗 Get your free key at: https://openweathermap.org/api');
    console.log('💡 Free tier includes 1000 calls/day');
    return;
  }

  console.log('✅ OpenWeatherMap API key found');
  console.log('🔑 Key format:', `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`);

  const baseUrl = 'https://api.openweathermap.org/data/2.5';
  const testLocation = 'New York, NY';

  try {
    // Test 1: Current Weather
    console.log('\n1️⃣ Testing Current Weather...');
    
    const currentResponse = await axios.get(`${baseUrl}/weather`, {
      params: {
        q: testLocation,
        appid: apiKey,
        units: 'imperial'
      }
    });

    const current = currentResponse.data;
    console.log('✅ Current weather retrieved successfully!');
    console.log('📍 Location:', `${current.name}, ${current.sys.country}`);
    console.log('🌡️  Temperature:', `${Math.round(current.main.temp)}°F`);
    console.log('🌤️  Condition:', current.weather[0].main);
    console.log('📝 Description:', current.weather[0].description);
    console.log('💨 Wind Speed:', `${current.wind?.speed || 0} mph`);
    console.log('💧 Humidity:', `${current.main.humidity}%`);

    // Test 2: Weather Forecast
    console.log('\n2️⃣ Testing Weather Forecast...');
    
    const forecastResponse = await axios.get(`${baseUrl}/forecast`, {
      params: {
        q: testLocation,
        appid: apiKey,
        units: 'imperial',
        cnt: 8 // Next 24 hours (8 x 3-hour intervals)
      }
    });

    const forecast = forecastResponse.data;
    console.log('✅ Weather forecast retrieved successfully!');
    console.log('📅 Forecast periods:', forecast.list.length);
    
    // Show next few forecasts
    forecast.list.slice(0, 3).forEach((period, index) => {
      const date = new Date(period.dt * 1000);
      console.log(`   ${index + 1}. ${date.toLocaleString()}: ${Math.round(period.main.temp)}°F, ${period.weather[0].description}`);
    });

    // Test 3: Geocoding for Coordinates
    console.log('\n3️⃣ Testing Geocoding...');
    
    const geoResponse = await axios.get(`http://api.openweathermap.org/geo/1.0/direct`, {
      params: {
        q: testLocation,
        limit: 1,
        appid: apiKey
      }
    });

    if (geoResponse.data.length > 0) {
      const location = geoResponse.data[0];
      console.log('✅ Geocoding successful!');
      console.log('📍 Coordinates:', `${location.lat}, ${location.lon}`);
      console.log('🏙️  Full name:', `${location.name}, ${location.state}, ${location.country}`);

      // Test 4: Weather Alerts (requires coordinates)
      console.log('\n4️⃣ Testing Weather Alerts...');
      
      try {
        const alertsResponse = await axios.get(`${baseUrl}/onecall`, {
          params: {
            lat: location.lat,
            lon: location.lon,
            appid: apiKey,
            exclude: 'minutely,hourly,daily'
          }
        });

        const alerts = alertsResponse.data.alerts || [];
        console.log('✅ Weather alerts check successful!');
        console.log('⚠️  Active alerts:', alerts.length);
        
        if (alerts.length > 0) {
          alerts.forEach((alert, index) => {
            console.log(`   ${index + 1}. ${alert.event}: ${alert.description.substring(0, 100)}...`);
          });
        } else {
          console.log('   No active weather alerts for this location');
        }
      } catch (alertError) {
        console.log('⚠️  Weather alerts not available (may require paid plan)');
      }
    }

    // Test 5: Content Suggestions Based on Weather
    console.log('\n5️⃣ Testing Weather Content Suggestions...');
    
    const weatherCondition = current.weather[0].main.toLowerCase();
    const temperature = current.main.temp;
    
    let suggestions = [];
    
    if (weatherCondition.includes('rain')) {
      suggestions.push('Cozy indoor dining', 'Comfort food specials', 'Rainy day promotions');
    } else if (weatherCondition.includes('sun') || weatherCondition.includes('clear')) {
      suggestions.push('Outdoor seating', 'Fresh summer menu', 'Sunny day specials');
    } else if (temperature < 40) {
      suggestions.push('Warm beverages', 'Hearty winter dishes', 'Indoor comfort');
    } else if (temperature > 80) {
      suggestions.push('Cool refreshments', 'Light summer fare', 'Air conditioning comfort');
    }
    
    console.log('✅ Content suggestions generated!');
    console.log('💡 Weather-based suggestions:', suggestions.join(', '));

    console.log('\n🎉 Weather Integration Test Complete!');
    console.log('✅ Current weather: Working');
    console.log('✅ Weather forecast: Working');
    console.log('✅ Geocoding: Working');
    console.log('✅ Content suggestions: Working');
    console.log('\n🌤️  Your LocalPost.ai now has weather-aware content generation!');

  } catch (error) {
    console.log('\n❌ Weather API Error:', error.message);
    
    if (error.response?.status === 401) {
      console.log('🔑 Authentication failed - check your API key');
      console.log('💡 Make sure your API key is activated (can take a few minutes)');
    } else if (error.response?.status === 429) {
      console.log('⏱️  Rate limit exceeded - try again later');
    } else if (error.response?.status === 404) {
      console.log('📍 Location not found - try a different location format');
    } else {
      console.log('🔍 Full error details:', error.response?.data || error);
    }
  }
}

// Run the test
testWeatherIntegration().catch(console.error);
