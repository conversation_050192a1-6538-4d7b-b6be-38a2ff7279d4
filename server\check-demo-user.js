require('dotenv').config();
const { supabase } = require('./config/database');
const axios = require('axios');

async function checkDemoUser() {
  console.log('🔍 Checking demo user status\n');

  try {
    // Check if demo user exists in users table
    console.log('1️⃣ Checking users table...');
    
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .eq('email', '<EMAIL>');

    if (usersError) {
      console.log('❌ Error checking users:', usersError.message);
    } else {
      console.log(`📊 Found ${users.length} users with demo email`);
      if (users.length > 0) {
        const user = users[0];
        console.log('✅ Demo user found in database:');
        console.log(`   👤 ID: ${user.id}`);
        console.log(`   📧 Email: ${user.email}`);
        console.log(`   🏢 Business: ${user.business_name}`);
        console.log(`   📍 Location: ${user.location}`);
        console.log(`   💳 Subscription: ${user.subscription_status}`);
      }
    }

    // Try to login with demo credentials
    console.log('\n2️⃣ Testing login with demo credentials...');
    
    try {
      const loginResponse = await axios.post('http://localhost:5000/api/auth/login', {
        email: '<EMAIL>',
        password: 'demo123'
      });
      
      console.log('✅ Login successful!');
      console.log(`   🎫 Token received: ${loginResponse.data.token.substring(0, 20)}...`);
      
      // Test content generation with the token
      console.log('\n3️⃣ Testing content generation...');
      
      const generateData = {
        startDate: new Date().toISOString().split('T')[0],
        endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        platforms: ['facebook', 'instagram'],
        regenerate: true
      };

      const contentResponse = await axios.post('http://localhost:5000/api/content/generate', generateData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${loginResponse.data.token}`
        },
        timeout: 30000
      });

      console.log('✅ Content generation successful!');
      console.log(`🎉 Generated ${contentResponse.data.generatedPosts?.length || 0} posts!`);
      
      if (contentResponse.data.generatedPosts?.length > 0) {
        const post = contentResponse.data.generatedPosts[0];
        console.log('\n📱 Sample Generated Post:');
        console.log(`   🎯 Platform: ${post.platform}`);
        console.log(`   📄 Content: ${post.content_text?.substring(0, 100)}...`);
        console.log(`   🏷️  Hashtags: ${post.hashtags?.join(', ') || 'None'}`);
        console.log(`   🌤️  Weather: ${post.weather_context || 'None'}`);
      }
      
      return true;
      
    } catch (loginError) {
      console.log('❌ Login failed:', loginError.response?.data?.error || loginError.message);
      
      if (loginError.response?.status === 401) {
        console.log('💡 Demo user credentials are invalid or user doesn\'t exist');
      }
      
      return false;
    }

  } catch (error) {
    console.log('❌ Check failed:', error.message);
    return false;
  }
}

// Check existing posts
async function checkExistingPosts() {
  console.log('\n4️⃣ Checking existing posts...');
  
  try {
    const { data: posts, error } = await supabase
      .from('generated_posts')
      .select('*')
      .limit(5);

    if (error) {
      console.log('❌ Error fetching posts:', error.message);
    } else {
      console.log(`📊 Found ${posts.length} existing posts in database`);
      
      posts.forEach((post, index) => {
        console.log(`   ${index + 1}. ${post.platform} - ${post.content_text?.substring(0, 50)}...`);
      });
    }
  } catch (error) {
    console.log('❌ Posts check failed:', error.message);
  }
}

// Run checks
async function runChecks() {
  const loginWorking = await checkDemoUser();
  await checkExistingPosts();
  
  console.log('\n🎯 Summary:');
  if (loginWorking) {
    console.log('✅ Demo user login and content generation working!');
    console.log('🎉 The "Failed to generate content" issue should be resolved!');
  } else {
    console.log('❌ Demo user login not working');
    console.log('💡 Need to create demo user or fix authentication');
  }
}

runChecks().catch(console.error);
