{"name": "localpost-server", "version": "1.0.0", "description": "LocalPost.ai Backend API", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest"}, "dependencies": {"@supabase/supabase-js": "^2.38.4", "apify-client": "^2.13.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "sharp": "^0.32.6", "stripe": "^14.7.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}