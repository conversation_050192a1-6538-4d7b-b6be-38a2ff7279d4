console.log('🔍 Direct Environment Test...\n');

console.log('Raw process.env.OPENAI_API_KEY:');
console.log(process.env.OPENAI_API_KEY);

console.log('\nLoading dotenv...');
require('dotenv').config();

console.log('After dotenv - process.env.OPENAI_API_KEY:');
console.log(process.env.OPENAI_API_KEY);

console.log('\nKey details:');
console.log('  Exists:', !!process.env.OPENAI_API_KEY);
console.log('  Length:', process.env.OPENAI_API_KEY?.length || 0);
console.log('  Starts with:', process.env.OPENAI_API_KEY?.substring(0, 15) || 'N/A');
console.log('  Ends with:', process.env.OPENAI_API_KEY ? '...' + process.env.OPENAI_API_KEY.substring(process.env.OPENAI_API_KEY.length - 10) : 'N/A');

// Test the OpenAI service
console.log('\n🤖 Testing OpenAI Service:');
try {
  const openaiService = require('./services/openai');
  console.log('✅ OpenAI service loaded');
  
  // Test content generation
  openaiService.generateContent({
    businessName: 'Test Business',
    businessType: 'restaurant',
    location: 'Downtown',
    tone: 'friendly'
  })
  .then(result => {
    console.log('✅ Content generation result:');
    console.log('   Text:', result.text?.substring(0, 100) + '...');
    console.log('   Type:', typeof result);
  })
  .catch(error => {
    console.log('❌ Content generation error:', error.message);
  });
  
} catch (error) {
  console.log('❌ OpenAI service error:', error.message);
}
